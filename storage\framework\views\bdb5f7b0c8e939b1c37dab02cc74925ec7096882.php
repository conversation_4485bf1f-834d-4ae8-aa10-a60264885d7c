<?php $__env->startSection('title', 'Page Not Found | Codibu Design'); ?>
<?php $__env->startSection('description', 'The page you are looking for could not be found.'); ?>

<?php $__env->startSection('content'); ?>
<section class="error-page">
    <div class="error-container">
        <div class="error-content">
            <h1 class="error-code">404</h1>
            <h2 class="error-title">Page Not Found</h2>
            <p class="error-description">
                Sorry, the page you are looking for could not be found. It might have been moved, deleted, or you entered the wrong URL.
            </p>
            
            <div class="error-actions">
                <a href="<?php echo e(route('home')); ?>" class="btn-primary">Go Home</a>
                <a href="javascript:history.back()" class="btn-secondary">Go Back</a>
            </div>
        </div>
    </div>
</section>

<style>
.error-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f3f0;
    padding: 120px 20px 80px;
}

.error-container {
    max-width: 600px;
    text-align: center;
}

.error-code {
    font-size: 120px;
    font-weight: 800;
    color: #ff6b35;
    line-height: 1;
    margin-bottom: 20px;
}

.error-title {
    font-size: 36px;
    font-weight: 700;
    color: #000000;
    margin-bottom: 20px;
}

.error-description {
    font-size: 18px;
    color: #666666;
    line-height: 1.6;
    margin-bottom: 40px;
}

.error-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #ff6b35;
    color: white;
}

.btn-primary:hover {
    background-color: #e55a2b;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: transparent;
    color: #000000;
    border: 2px solid #000000;
}

.btn-secondary:hover {
    background-color: #000000;
    color: white;
}

@media (max-width: 768px) {
    .error-code {
        font-size: 80px;
    }
    
    .error-title {
        font-size: 28px;
    }
    
    .error-description {
        font-size: 16px;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\codibu\resources\views/errors/404.blade.php ENDPATH**/ ?>