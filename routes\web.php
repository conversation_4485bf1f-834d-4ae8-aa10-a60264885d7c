<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Home Routes
Route::get('/', [HomeController::class, 'index'])->name('home');

// Service Routes
Route::prefix('services')->name('services.')->group(function () {
    Route::get('/{service}', [HomeController::class, 'service'])
        ->where('service', 'codibu-design|web-design|video-production|visual-media|graphic-designs')
        ->name('show');
});

// Static Pages Routes
Route::prefix('pages')->name('pages.')->group(function () {
    Route::view('/privacy-policy', 'pages.privacy-policy')->name('privacy');
    Route::view('/terms-of-service', 'pages.terms')->name('terms');
});

// Contact Routes (for future implementation)
Route::prefix('contact')->name('contact.')->group(function () {
    Route::view('/', 'contact.index')->name('index');
    // Route::post('/', [ContactController::class, 'store'])->name('store');
});

// Sitemap and SEO Routes
Route::get('/sitemap.xml', function () {
    return response()->view('sitemap.xml')->header('Content-Type', 'text/xml');
})->name('sitemap');

// Health check route
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'app' => config('app.name'),
        'version' => '1.0.0'
    ]);
})->name('health');


