!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[Section]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[Section]"]=t(require("react")):e["rb_wixui.thunderbolt[Section]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var a in i)({}).hasOwnProperty.call(i,a)&&(e[a]=i[a])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},i={};function a(e){var n=i[e];if(void 0!==n)return n.exports;var r=i[e]={exports:{}};return t[e](r,r.exports,a),r.exports}a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,{a:t}),t},a.d=function(e,t){for(var i in t)a.o(t,i)&&!a.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return function(){"use strict";a.r(n),a.d(n,{components:function(){return ai}});var e={};a.r(e),a.d(e,{STATIC_MEDIA_URL:function(){return et},ph:function(){return Ke}});var t=a(448),i=a.n(t),r=a(5329),o=a.n(r);function c(e){var t,i,a="";if("string"==typeof e||"number"==typeof e)a+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(i=c(e[t]))&&(a&&(a+=" "),a+=i);else for(t in e)e[t]&&(a&&(a+=" "),a+=t);return a}var s=function(){for(var e,t,i=0,a="";i<arguments.length;)(e=arguments[i++])&&(t=c(e))&&(a&&(a+=" "),a+=t);return a};const l="wixui-",d=(e,...t)=>{const i=[];return e&&i.push(`${l}${e}`),t.forEach((e=>{e&&(i.push(`${l}${e}`),i.push(e))})),i.join(" ")};const u=13,h=27;function g(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}g(32),g(u),g(h);const m="section-container";var p="jhxvbR";const _="v1",f=2,T=1920,I=1920,E=1e3,L=1e3,b={SCALE_TO_FILL:"fill",SCALE_TO_FIT:"fit",STRETCH:"stretch",ORIGINAL_SIZE:"original_size",TILE:"tile",TILE_HORIZONTAL:"tile_horizontal",TILE_VERTICAL:"tile_vertical",FIT_AND_TILE:"fit_and_tile",LEGACY_STRIP_TILE:"legacy_strip_tile",LEGACY_STRIP_TILE_HORIZONTAL:"legacy_strip_tile_horizontal",LEGACY_STRIP_TILE_VERTICAL:"legacy_strip_tile_vertical",LEGACY_STRIP_SCALE_TO_FILL:"legacy_strip_fill",LEGACY_STRIP_SCALE_TO_FIT:"legacy_strip_fit",LEGACY_STRIP_FIT_AND_TILE:"legacy_strip_fit_and_tile",LEGACY_STRIP_ORIGINAL_SIZE:"legacy_strip_original_size",LEGACY_ORIGINAL_SIZE:"actual_size",LEGACY_FIT_WIDTH:"fitWidth",LEGACY_FIT_HEIGHT:"fitHeight",LEGACY_FULL:"full",LEGACY_BG_FIT_AND_TILE:"legacy_tile",LEGACY_BG_FIT_AND_TILE_HORIZONTAL:"legacy_tile_horizontal",LEGACY_BG_FIT_AND_TILE_VERTICAL:"legacy_tile_vertical",LEGACY_BG_NORMAL:"legacy_normal"},A={FIT:"fit",FILL:"fill",FILL_FOCAL:"fill_focal",CROP:"crop",LEGACY_CROP:"legacy_crop",LEGACY_FILL:"legacy_fill"},y={CENTER:"center",TOP:"top",TOP_LEFT:"top_left",TOP_RIGHT:"top_right",BOTTOM:"bottom",BOTTOM_LEFT:"bottom_left",BOTTOM_RIGHT:"bottom_right",LEFT:"left",RIGHT:"right"},M={[y.CENTER]:{x:.5,y:.5},[y.TOP_LEFT]:{x:0,y:0},[y.TOP_RIGHT]:{x:1,y:0},[y.TOP]:{x:.5,y:0},[y.BOTTOM_LEFT]:{x:0,y:1},[y.BOTTOM_RIGHT]:{x:1,y:1},[y.BOTTOM]:{x:.5,y:1},[y.RIGHT]:{x:1,y:.5},[y.LEFT]:{x:0,y:.5}},w={center:"c",top:"t",top_left:"tl",top_right:"tr",bottom:"b",bottom_left:"bl",bottom_right:"br",left:"l",right:"r"},O={BG:"bg",IMG:"img",SVG:"svg"},v={AUTO:"auto",CLASSIC:"classic",SUPER:"super"},S={classic:1,super:2},C={radius:"0.66",amount:"1.00",threshold:"0.01"},R={uri:"",css:{img:{},container:{}},attr:{img:{},container:{}},transformed:!1},G=25e6,N=[1.5,2,4],P={HIGH:{size:196e4,quality:90,maxUpscale:1},MEDIUM:{size:36e4,quality:85,maxUpscale:1},LOW:{size:16e4,quality:80,maxUpscale:1.2},TINY:{size:0,quality:80,maxUpscale:1.4}},F={HIGH:"HIGH",MEDIUM:"MEDIUM",LOW:"LOW",TINY:"TINY"},x={CONTRAST:"contrast",BRIGHTNESS:"brightness",SATURATION:"saturation",HUE:"hue",BLUR:"blur"},k={JPG:"jpg",JPEG:"jpeg",JPE:"jpe",PNG:"png",WEBP:"webp",WIX_ICO_MP:"wix_ico_mp",WIX_MP:"wix_mp",GIF:"gif",SVG:"svg",AVIF:"avif",UNRECOGNIZED:"unrecognized"},H={AVIF:"AVIF",PAVIF:"PAVIF"};k.JPG,k.JPEG,k.JPE,k.PNG,k.GIF,k.WEBP;function B(e,...t){return function(...i){const a=i[i.length-1]||{},n=[e[0]];return t.forEach((function(t,r){const o=Number.isInteger(t)?i[t]:a[t];n.push(o,e[r+1])})),n.join("")}}function Y(e){return e[e.length-1]}const U=[k.PNG,k.JPEG,k.JPG,k.JPE,k.WIX_ICO_MP,k.WIX_MP,k.WEBP,k.AVIF],$=[k.JPEG,k.JPG,k.JPE];function z(e,t,i){return i&&t&&!(!(a=t.id)||!a.trim()||"none"===a.toLowerCase())&&Object.values(b).includes(e);var a}function j(e,t,i){return function(e,t,i=!1){return!((V(e)||Z(e))&&t&&!i)}(e,t,i)&&(function(e){return U.includes(Q(e))}(e)||function(e,t=!1){return W(e)&&t}(e,i))&&!/(^https?)|(^data)|(^\/\/)/.test(e)}function D(e){return Q(e)===k.PNG}function V(e){return Q(e)===k.WEBP}function W(e){return Q(e)===k.GIF}function Z(e){return Q(e)===k.AVIF}const q=["/","\\","?","<",">","|","\u201c",":",'"'].map(encodeURIComponent),J=["\\.","\\*"],K="_";function X(e){return function(e){return $.includes(Q(e))}(e)?k.JPG:D(e)?k.PNG:V(e)?k.WEBP:W(e)?k.GIF:Z(e)?k.AVIF:k.UNRECOGNIZED}function Q(e){return(/[.]([^.]+)$/.exec(e)&&/[.]([^.]+)$/.exec(e)[1]||"").toLowerCase()}function ee(e,t,i,a,n){let r;return r=n===A.FILL?function(e,t,i,a){return Math.max(i/e,a/t)}(e,t,i,a):n===A.FIT?function(e,t,i,a){return Math.min(i/e,a/t)}(e,t,i,a):1,r}function te(e,t,i,a,n,r){e=e||a.width,t=t||a.height;const{scaleFactor:o,width:c,height:s}=function(e,t,i,a,n){let r,o=i,c=a;if(r=ee(e,t,i,a,n),n===A.FIT&&(o=e*r,c=t*r),o&&c&&o*c>G){const i=Math.sqrt(G/(o*c));o*=i,c*=i,r=ee(e,t,o,c,n)}return{scaleFactor:r,width:o,height:c}}(e,t,a.width*n,a.height*n,i);return function(e,t,i,a,n,r,o){const{optimizedScaleFactor:c,upscaleMethodValue:s,forceUSM:l}=function(e,t,i,a){if("auto"===a)return function(e,t){const i=re(e,t);return{optimizedScaleFactor:P[i].maxUpscale,upscaleMethodValue:S.classic,forceUSM:!1}}(e,t);if("super"===a)return function(e){return{optimizedScaleFactor:Y(N),upscaleMethodValue:S.super,forceUSM:!(N.includes(e)||e>Y(N))}}(i);return function(e,t){const i=re(e,t);return{optimizedScaleFactor:P[i].maxUpscale,upscaleMethodValue:S.classic,forceUSM:!1}}(e,t)}(e,t,r,n);let d=i,u=a;if(r<=c)return{width:d,height:u,scaleFactor:r,upscaleMethodValue:s,forceUSM:l,cssUpscaleNeeded:!1};switch(o){case A.FILL:d=i*(c/r),u=a*(c/r);break;case A.FIT:d=e*c,u=t*c}return{width:d,height:u,scaleFactor:c,upscaleMethodValue:s,forceUSM:l,cssUpscaleNeeded:!0}}(e,t,c,s,r,o,i)}function ie(e,t,i,a){const n=ne(i)||function(e=y.CENTER){return M[e]}(a);return{x:Math.max(0,Math.min(e.width-t.width,n.x*e.width-t.width/2)),y:Math.max(0,Math.min(e.height-t.height,n.y*e.height-t.height/2)),width:Math.min(e.width,t.width),height:Math.min(e.height,t.height)}}function ae(e){return e.alignment&&w[e.alignment]||w[y.CENTER]}function ne(e){let t;return!e||"number"!=typeof e.x||isNaN(e.x)||"number"!=typeof e.y||isNaN(e.y)||(t={x:oe(Math.max(0,Math.min(100,e.x))/100,2),y:oe(Math.max(0,Math.min(100,e.y))/100,2)}),t}function re(e,t){const i=e*t;return i>P[F.HIGH].size?F.HIGH:i>P[F.MEDIUM].size?F.MEDIUM:i>P[F.LOW].size?F.LOW:F.TINY}function oe(e,t){const i=Math.pow(10,t||0);return(e*i/i).toFixed(t)}function ce(e){return e&&e.upscaleMethod&&v[e.upscaleMethod.toUpperCase()]||v.AUTO}function se(e,t){const i=V(e)||Z(e);return Q(e)===k.GIF||i&&t}const le={isMobile:!1},de=function(e){return le[e]};function ue(){if("undefined"!=typeof window&&"undefined"!=typeof navigator){const t=window.matchMedia&&window.matchMedia("(max-width: 767px)").matches,i=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);e=t&&i,le["isMobile"]=e}var e}function he(e,t){const i={css:{container:{}}},{css:a}=i,{fittingType:n}=e;switch(n){case b.ORIGINAL_SIZE:case b.LEGACY_ORIGINAL_SIZE:case b.LEGACY_STRIP_ORIGINAL_SIZE:a.container.backgroundSize="auto",a.container.backgroundRepeat="no-repeat";break;case b.SCALE_TO_FIT:case b.LEGACY_STRIP_SCALE_TO_FIT:a.container.backgroundSize="contain",a.container.backgroundRepeat="no-repeat";break;case b.STRETCH:a.container.backgroundSize="100% 100%",a.container.backgroundRepeat="no-repeat";break;case b.SCALE_TO_FILL:case b.LEGACY_STRIP_SCALE_TO_FILL:a.container.backgroundSize="cover",a.container.backgroundRepeat="no-repeat";break;case b.TILE_HORIZONTAL:case b.LEGACY_STRIP_TILE_HORIZONTAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-x";break;case b.TILE_VERTICAL:case b.LEGACY_STRIP_TILE_VERTICAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-y";break;case b.TILE:case b.LEGACY_STRIP_TILE:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat";break;case b.LEGACY_STRIP_FIT_AND_TILE:a.container.backgroundSize="contain",a.container.backgroundRepeat="repeat";break;case b.FIT_AND_TILE:case b.LEGACY_BG_FIT_AND_TILE:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat";break;case b.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-x";break;case b.LEGACY_BG_FIT_AND_TILE_VERTICAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-y";break;case b.LEGACY_BG_NORMAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="no-repeat"}switch(t.alignment){case y.CENTER:a.container.backgroundPosition="center center";break;case y.LEFT:a.container.backgroundPosition="left center";break;case y.RIGHT:a.container.backgroundPosition="right center";break;case y.TOP:a.container.backgroundPosition="center top";break;case y.BOTTOM:a.container.backgroundPosition="center bottom";break;case y.TOP_RIGHT:a.container.backgroundPosition="right top";break;case y.TOP_LEFT:a.container.backgroundPosition="left top";break;case y.BOTTOM_RIGHT:a.container.backgroundPosition="right bottom";break;case y.BOTTOM_LEFT:a.container.backgroundPosition="left bottom"}return i}const ge={[y.CENTER]:"center",[y.TOP]:"top",[y.TOP_LEFT]:"top left",[y.TOP_RIGHT]:"top right",[y.BOTTOM]:"bottom",[y.BOTTOM_LEFT]:"bottom left",[y.BOTTOM_RIGHT]:"bottom right",[y.LEFT]:"left",[y.RIGHT]:"right"},me={position:"absolute",top:"auto",right:"auto",bottom:"auto",left:"auto"};function pe(e,t){const i={css:{container:{},img:{}}},{css:a}=i,{fittingType:n}=e,r=t.alignment;switch(a.container.position="relative",n){case b.ORIGINAL_SIZE:case b.LEGACY_ORIGINAL_SIZE:e.parts&&e.parts.length?(a.img.width=e.parts[0].width,a.img.height=e.parts[0].height):(a.img.width=e.src.width,a.img.height=e.src.height);break;case b.SCALE_TO_FIT:case b.LEGACY_FIT_WIDTH:case b.LEGACY_FIT_HEIGHT:case b.LEGACY_FULL:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="contain",a.img.objectPosition=ge[r]||"unset";break;case b.LEGACY_BG_NORMAL:a.img.width="100%",a.img.height="100%",a.img.objectFit="none",a.img.objectPosition=ge[r]||"unset";break;case b.STRETCH:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="fill";break;case b.SCALE_TO_FILL:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="cover"}if("number"==typeof a.img.width&&"number"==typeof a.img.height&&(a.img.width!==t.width||a.img.height!==t.height)){const e=Math.round((t.height-a.img.height)/2),i=Math.round((t.width-a.img.width)/2);Object.assign(a.img,me,function(e,t,i){return{[y.TOP_LEFT]:{top:0,left:0},[y.TOP_RIGHT]:{top:0,right:0},[y.TOP]:{top:0,left:t},[y.BOTTOM_LEFT]:{bottom:0,left:0},[y.BOTTOM_RIGHT]:{bottom:0,right:0},[y.BOTTOM]:{bottom:0,left:t},[y.RIGHT]:{top:e,right:0},[y.LEFT]:{top:e,left:0},[y.CENTER]:{width:i.width,height:i.height,objectFit:"none"}}}(e,i,t)[r])}return i}function _e(e,t){const i={css:{container:{}},attr:{container:{},img:{}}},{css:a,attr:n}=i,{fittingType:r}=e,o=t.alignment,{width:c,height:s}=e.src;let l;switch(a.container.position="relative",r){case b.ORIGINAL_SIZE:case b.LEGACY_ORIGINAL_SIZE:case b.TILE:e.parts&&e.parts.length?(n.img.width=e.parts[0].width,n.img.height=e.parts[0].height):(n.img.width=c,n.img.height=s),n.img.preserveAspectRatio="xMidYMid slice";break;case b.SCALE_TO_FIT:case b.LEGACY_FIT_WIDTH:case b.LEGACY_FIT_HEIGHT:case b.LEGACY_FULL:n.img.width="100%",n.img.height="100%",n.img.transform="",n.img.preserveAspectRatio="";break;case b.STRETCH:n.img.width=t.width,n.img.height=t.height,n.img.x=0,n.img.y=0,n.img.transform="",n.img.preserveAspectRatio="none";break;case b.SCALE_TO_FILL:j(e.src.id)?(n.img.width=t.width,n.img.height=t.height):(l=function(e,t,i,a,n){const r=ee(e,t,i,a,n);return{width:Math.round(e*r),height:Math.round(t*r)}}(c,s,t.width,t.height,A.FILL),n.img.width=l.width,n.img.height=l.height),n.img.x=0,n.img.y=0,n.img.transform="",n.img.preserveAspectRatio="xMidYMid slice"}if("number"==typeof n.img.width&&"number"==typeof n.img.height&&(n.img.width!==t.width||n.img.height!==t.height)){let e,i,a=0,c=0;r===b.TILE?(e=t.width%n.img.width,i=t.height%n.img.height):(e=t.width-n.img.width,i=t.height-n.img.height);const s=Math.round(e/2),l=Math.round(i/2);switch(o){case y.TOP_LEFT:a=0,c=0;break;case y.TOP:a=s,c=0;break;case y.TOP_RIGHT:a=e,c=0;break;case y.LEFT:a=0,c=l;break;case y.CENTER:a=s,c=l;break;case y.RIGHT:a=e,c=l;break;case y.BOTTOM_LEFT:a=0,c=i;break;case y.BOTTOM:a=s,c=i;break;case y.BOTTOM_RIGHT:a=e,c=i}n.img.x=a,n.img.y=c}return n.container.width=t.width,n.container.height=t.height,n.container.viewBox=[0,0,t.width,t.height].join(" "),i}function fe(e,t,i){let a;switch(t.crop&&(a=function(e,t){const i=Math.max(0,Math.min(e.width,t.x+t.width)-Math.max(0,t.x)),a=Math.max(0,Math.min(e.height,t.y+t.height)-Math.max(0,t.y));return i&&a&&(e.width!==i||e.height!==a)?{x:Math.max(0,t.x),y:Math.max(0,t.y),width:i,height:a}:null}(t,t.crop),a&&(e.src.width=a.width,e.src.height=a.height,e.src.isCropped=!0,e.parts.push(Ie(a)))),e.fittingType){case b.SCALE_TO_FIT:case b.LEGACY_FIT_WIDTH:case b.LEGACY_FIT_HEIGHT:case b.LEGACY_FULL:case b.FIT_AND_TILE:case b.LEGACY_BG_FIT_AND_TILE:case b.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case b.LEGACY_BG_FIT_AND_TILE_VERTICAL:case b.LEGACY_BG_NORMAL:e.parts.push(Te(e,i));break;case b.SCALE_TO_FILL:e.parts.push(function(e,t){const i=te(e.src.width,e.src.height,A.FILL,t,e.devicePixelRatio,e.upscaleMethod),a=ne(e.focalPoint);return{transformType:a?A.FILL_FOCAL:A.FILL,width:Math.round(i.width),height:Math.round(i.height),alignment:ae(t),focalPointX:a&&a.x,focalPointY:a&&a.y,upscale:i.scaleFactor>1,forceUSM:i.forceUSM,scaleFactor:i.scaleFactor,cssUpscaleNeeded:i.cssUpscaleNeeded,upscaleMethodValue:i.upscaleMethodValue}}(e,i));break;case b.STRETCH:e.parts.push(function(e,t){const i=ee(e.src.width,e.src.height,t.width,t.height,A.FILL),a={...t};return a.width=e.src.width*i,a.height=e.src.height*i,Te(e,a)}(e,i));break;case b.TILE_HORIZONTAL:case b.TILE_VERTICAL:case b.TILE:case b.LEGACY_ORIGINAL_SIZE:case b.ORIGINAL_SIZE:a=ie(e.src,i,e.focalPoint,i.alignment),e.src.isCropped?(Object.assign(e.parts[0],a),e.src.width=a.width,e.src.height=a.height):e.parts.push(Ie(a));break;case b.LEGACY_STRIP_TILE_HORIZONTAL:case b.LEGACY_STRIP_TILE_VERTICAL:case b.LEGACY_STRIP_TILE:case b.LEGACY_STRIP_ORIGINAL_SIZE:e.parts.push(function(e){return{transformType:A.LEGACY_CROP,width:Math.round(e.width),height:Math.round(e.height),alignment:ae(e),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}(i));break;case b.LEGACY_STRIP_SCALE_TO_FIT:case b.LEGACY_STRIP_FIT_AND_TILE:e.parts.push(function(e){return{transformType:A.FIT,width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(i));break;case b.LEGACY_STRIP_SCALE_TO_FILL:e.parts.push(function(e){return{transformType:A.LEGACY_FILL,width:Math.round(e.width),height:Math.round(e.height),alignment:ae(e),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(i))}}function Te(e,t){const i=te(e.src.width,e.src.height,A.FIT,t,e.devicePixelRatio,e.upscaleMethod);return{transformType:!e.src.width||!e.src.height?A.FIT:A.FILL,width:Math.round(i.width),height:Math.round(i.height),alignment:w.center,upscale:i.scaleFactor>1,forceUSM:i.forceUSM,scaleFactor:i.scaleFactor,cssUpscaleNeeded:i.cssUpscaleNeeded,upscaleMethodValue:i.upscaleMethodValue}}function Ie(e){return{transformType:A.CROP,x:Math.round(e.x),y:Math.round(e.y),width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}function Ee(e,t){t=t||{},e.quality=function(e,t){const i=e.fileType===k.PNG,a=e.fileType===k.JPG,n=e.fileType===k.WEBP,r=e.fileType===k.AVIF,o=a||i||n||r;if(o){const a=Y(e.parts),n=(c=a.width,s=a.height,P[re(c,s)].quality);let r=t.quality&&t.quality>=5&&t.quality<=90?t.quality:n;return r=i?r+5:r,r}var c,s;return 0}(e,t),e.progressive=function(e){return!1!==e.progressive}(t),e.watermark=function(e){return e.watermark}(t),e.autoEncode=t.autoEncode??!0,e.encoding=t?.encoding,e.unsharpMask=function(e,t){if(function(e){const t="number"==typeof(e=e||{}).radius&&!isNaN(e.radius)&&e.radius>=.1&&e.radius<=500,i="number"==typeof e.amount&&!isNaN(e.amount)&&e.amount>=0&&e.amount<=10,a="number"==typeof e.threshold&&!isNaN(e.threshold)&&e.threshold>=0&&e.threshold<=255;return t&&i&&a}(t.unsharpMask))return{radius:oe(t.unsharpMask?.radius,2),amount:oe(t.unsharpMask?.amount,2),threshold:oe(t.unsharpMask?.threshold,2)};if(("number"!=typeof(i=(i=t.unsharpMask)||{}).radius||isNaN(i.radius)||0!==i.radius||"number"!=typeof i.amount||isNaN(i.amount)||0!==i.amount||"number"!=typeof i.threshold||isNaN(i.threshold)||0!==i.threshold)&&function(e){const t=Y(e.parts);return!(t.scaleFactor>=1)||t.forceUSM||t.transformType===A.FIT}(e))return C;var i;return}(e,t),e.filters=function(e){const t=e.filters||{},i={};Le(t[x.CONTRAST],-100,100)&&(i[x.CONTRAST]=t[x.CONTRAST]);Le(t[x.BRIGHTNESS],-100,100)&&(i[x.BRIGHTNESS]=t[x.BRIGHTNESS]);Le(t[x.SATURATION],-100,100)&&(i[x.SATURATION]=t[x.SATURATION]);Le(t[x.HUE],-180,180)&&(i[x.HUE]=t[x.HUE]);Le(t[x.BLUR],0,100)&&(i[x.BLUR]=t[x.BLUR]);return i}(t)}function Le(e,t,i){return"number"==typeof e&&!isNaN(e)&&0!==e&&e>=t&&e<=i}function be(e,t,i,a){const n=function(e){return e?.isSEOBot??!1}(a),r=X(t.id),o=function(e,t){const i=/\.([^.]*)$/,a=new RegExp(`(${q.concat(J).join("|")})`,"g");if(t&&t.length){let e=t;const n=t.match(i);return n&&U.includes(n[1])&&(e=t.replace(i,"")),encodeURIComponent(e).replace(a,K)}const n=e.match(/\/(.*?)$/);return(n?n[1]:e).replace(i,"")}(t.id,t.name),c=n?1:function(e){return Math.min(e.pixelAspectRatio||1,f)}(i),s=Q(t.id),l=s,d=j(t.id,a?.hasAnimation,a?.allowAnimatedTransform),u={fileName:o,fileExtension:s,fileType:r,fittingType:e,preferredExtension:l,src:{id:t.id,width:t.width,height:t.height,isCropped:!1,isAnimated:se(t.id,a?.hasAnimation)},focalPoint:{x:t.focalPoint&&t.focalPoint.x,y:t.focalPoint&&t.focalPoint.y},parts:[],devicePixelRatio:c,quality:0,upscaleMethod:ce(a),progressive:!0,watermark:"",unsharpMask:{},filters:{},transformed:d};return d&&(fe(u,t,i),Ee(u,a)),u}function Ae(e,t,i){const a={...i},n=de("isMobile");switch(e){case b.LEGACY_BG_FIT_AND_TILE:case b.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case b.LEGACY_BG_FIT_AND_TILE_VERTICAL:case b.LEGACY_BG_NORMAL:const e=n?E:T,i=n?L:I;a.width=Math.min(e,t.width),a.height=Math.min(i,Math.round(a.width/(t.width/t.height))),a.pixelAspectRatio=1}return a}const ye=B`fit/w_${"width"},h_${"height"}`,Me=B`fill/w_${"width"},h_${"height"},al_${"alignment"}`,we=B`fill/w_${"width"},h_${"height"},fp_${"focalPointX"}_${"focalPointY"}`,Oe=B`crop/x_${"x"},y_${"y"},w_${"width"},h_${"height"}`,ve=B`crop/w_${"width"},h_${"height"},al_${"alignment"}`,Se=B`fill/w_${"width"},h_${"height"},al_${"alignment"}`,Ce=B`,lg_${"upscaleMethodValue"}`,Re=B`,q_${"quality"}`,Ge=B`,quality_auto`,Ne=B`,usm_${"radius"}_${"amount"}_${"threshold"}`,Pe=B`,bl`,Fe=B`,wm_${"watermark"}`,xe={[x.CONTRAST]:B`,con_${"contrast"}`,[x.BRIGHTNESS]:B`,br_${"brightness"}`,[x.SATURATION]:B`,sat_${"saturation"}`,[x.HUE]:B`,hue_${"hue"}`,[x.BLUR]:B`,blur_${"blur"}`},ke=B`,enc_auto`,He=B`,enc_avif`,Be=B`,enc_pavif`,Ye=B`,pstr`;function Ue(e,t,i,a={},n){if(j(t.id,a?.hasAnimation,a?.allowAnimatedTransform)){if(V(t.id)||Z(t.id)){const{alignment:r,...o}=i;t.focalPoint={x:void 0,y:void 0},delete t?.crop,n=be(e,t,o,a)}else n=n||be(e,t,i,a);return function(e){const t=[];e.parts.forEach((e=>{switch(e.transformType){case A.CROP:t.push(Oe(e));break;case A.LEGACY_CROP:t.push(ve(e));break;case A.LEGACY_FILL:let i=Se(e);e.upscale&&(i+=Ce(e)),t.push(i);break;case A.FIT:let a=ye(e);e.upscale&&(a+=Ce(e)),t.push(a);break;case A.FILL:let n=Me(e);e.upscale&&(n+=Ce(e)),t.push(n);break;case A.FILL_FOCAL:let r=we(e);e.upscale&&(r+=Ce(e)),t.push(r)}}));let i=t.join("/");return e.quality&&(i+=Re(e)),e.unsharpMask&&(i+=Ne(e.unsharpMask)),e.progressive||(i+=Pe(e)),e.watermark&&(i+=Fe(e)),e.filters&&(i+=Object.keys(e.filters).map((t=>xe[t](e.filters))).join("")),e.fileType!==k.GIF&&(e.encoding===H.AVIF?(i+=He(e),i+=Ge(e)):e.encoding===H.PAVIF?(i+=Be(e),i+=Ge(e)):e.autoEncode&&(i+=ke(e))),e.src?.isAnimated&&e.transformed&&(i+=Ye(e)),`${e.src.id}/${_}/${i}/${e.fileName}.${e.preferredExtension}`}(n)}return t.id}const $e={[y.CENTER]:"50% 50%",[y.TOP_LEFT]:"0% 0%",[y.TOP_RIGHT]:"100% 0%",[y.TOP]:"50% 0%",[y.BOTTOM_LEFT]:"0% 100%",[y.BOTTOM_RIGHT]:"100% 100%",[y.BOTTOM]:"50% 100%",[y.RIGHT]:"100% 50%",[y.LEFT]:"0% 50%"},ze=Object.entries($e).reduce(((e,[t,i])=>(e[i]=t,e)),{}),je=[b.TILE,b.TILE_HORIZONTAL,b.TILE_VERTICAL,b.LEGACY_BG_FIT_AND_TILE,b.LEGACY_BG_FIT_AND_TILE_HORIZONTAL,b.LEGACY_BG_FIT_AND_TILE_VERTICAL],De=[b.LEGACY_ORIGINAL_SIZE,b.ORIGINAL_SIZE,b.LEGACY_BG_NORMAL];function Ve(e,t,{width:i,height:a}){return e===b.TILE&&t.width>i&&t.height>a}function We(e,{width:t,height:i}){if(!t||!i){const a=t||Math.min(980,e.width),n=a/e.width;return{width:a,height:i||e.height*n}}return{width:t,height:i}}function Ze(e,t,i,a="center"){const n={img:{},container:{}};if(e===b.SCALE_TO_FILL){const e=t.focalPoint&&function(e){const t=`${e.x}% ${e.y}%`;return ze[t]||""}(t.focalPoint),r=e||a;t.focalPoint&&!e?n.img={objectPosition:qe(t,i,t.focalPoint)}:n.img={objectPosition:$e[r]}}else[b.LEGACY_ORIGINAL_SIZE,b.ORIGINAL_SIZE].includes(e)?n.img={objectFit:"none",top:"auto",left:"auto",right:"auto",bottom:"auto"}:je.includes(e)&&(n.container={backgroundSize:`${t.width}px ${t.height}px`});return n}function qe(e,t,i){const{width:a,height:n}=e,{width:r,height:o}=t,{x:c,y:s}=i;if(!r||!o)return`${c}% ${s}%`;const l=Math.max(r/a,o/n),d=a*l,u=n*l,h=Math.max(0,Math.min(d-r,d*(c/100)-r/2)),g=Math.max(0,Math.min(u-o,u*(s/100)-o/2));return`${h&&Math.floor(h/(d-r)*100)}% ${g&&Math.floor(g/(u-o)*100)}%`}const Je={width:"100%",height:"100%"};function Ke(e,t,i,a={}){const{autoEncode:n=!0,isSEOBot:r,shouldLoadHQImage:o,hasAnimation:c,allowAnimatedTransform:s,encoding:l}=a;if(!z(e,t,i))return R;const d=void 0===s||s,u=j(t.id,c,d);if(!u||o)return Xe(e,t,i,{...a,autoEncode:n,useSrcset:u});const h={...i,...We(t,i)},{alignment:g,htmlTag:m}=h,p=Ve(e,t,h),_=function(e,t,{width:i,height:a},n=!1){if(n)return{width:i,height:a};const r=!De.includes(e),o=Ve(e,t,{width:i,height:a}),c=!o&&je.includes(e),s=c?t.width:i,l=c?t.height:a,d=r?function(e,t){return e>900?t?.05:.15:e>500?t?.1:.18:e>200?.25:1}(s,D(t.id)):1;return{width:o?1920:s*d,height:l*d}}(e,t,h,r),f=function(e,t,i){return i?0:je.includes(t)?1:e>200?2:3}(h.width,e,r),T=function(e,t){const i=je.includes(e)&&!t;return e===b.SCALE_TO_FILL||i?b.SCALE_TO_FIT:e}(e,p),I=Ze(e,t,i,g),{uri:E}=Xe(T,t,{..._,alignment:g,htmlTag:m},{autoEncode:n,filters:f?{blur:f}:{},hasAnimation:c,allowAnimatedTransform:d,encoding:l}),{attr:L={},css:A}=Xe(e,t,{...h,alignment:g,htmlTag:m},{});return A.img=A.img||{},A.container=A.container||{},Object.assign(A.img,I.img,Je),Object.assign(A.container,I.container),{uri:E,css:A,attr:L,transformed:!0}}function Xe(e,t,i,a){let n={};if(z(e,t,i)){const r=Ae(e,t,i),o=be(e,t,r,a);n.uri=Ue(e,t,r,a,o),a?.useSrcset&&(n.srcset=function(e,t,i,a,n){const r=i.pixelAspectRatio||1;return{dpr:[`${1===r?n.uri:Ue(e,t,{...i,pixelAspectRatio:1},a)} 1x`,`${2===r?n.uri:Ue(e,t,{...i,pixelAspectRatio:2},a)} 2x`]}}(e,t,r,a,n)),Object.assign(n,function(e,t){let i;return i=t.htmlTag===O.BG?he:t.htmlTag===O.SVG?_e:pe,i(e,t)}(o,r),{transformed:o.transformed})}else n=R;return n}const Qe="https://static.wixstatic.com/media/";"undefined"!=typeof window&&window.devicePixelRatio;ue();ue();const et=Qe,{STATIC_MEDIA_URL:tt}=e,it=({fittingType:e,src:t,target:i,options:a})=>{const n=Ke(e,t,i,{...a,autoEncode:!0});return n?.uri&&!/^[a-z]+:/.test(n.uri)&&(n.uri=`${tt}${n.uri}`),n},at=/^[a-z]+:/,nt=e=>{const{id:t,containerId:i,uri:a,alt:n,name:o="",role:c,width:s,height:l,displayMode:d,devicePixelRatio:u,quality:h,alignType:g,bgEffectName:m="",focalPoint:_,upscaleMethod:f,className:T="",crop:I,imageStyles:E={},targetWidth:L,targetHeight:b,targetScale:A,onLoad:y=()=>{},onError:M=()=>{},shouldUseLQIP:w,containerWidth:O,containerHeight:v,getPlaceholder:S,isInFirstFold:C,placeholderTransition:R,socialAttrs:G,isSEOBot:N,skipMeasure:P,hasAnimation:F,encoding:x}=e,k=r.useRef(null);let H="";const B="blur"===R,Y=r.useRef(null);if(!Y.current)if(S||w||C||N){const e={upscaleMethod:f,...h||{},shouldLoadHQImage:C,isSEOBot:N,hasAnimation:F,encoding:x};Y.current=(S||it)({fittingType:d,src:{id:a,width:s,height:l,crop:I,name:o,focalPoint:_},target:{width:O,height:v,alignment:g,htmlTag:"img"},options:e}),H=!Y.current.transformed||C||N?"":"true"}else Y.current={uri:void 0,css:{img:{}},attr:{img:{},container:{}},transformed:!1};const U=!N&&(S||w)&&!C&&Y.current.transformed,$=r.useMemo((()=>JSON.stringify({containerId:i,...i&&{containerId:i},...g&&{alignType:g},...P&&{skipMeasure:!0},displayMode:d,...O&&{targetWidth:O},...v&&{targetHeight:v},...L&&{targetWidth:L},...b&&{targetHeight:b},...A&&{targetScale:A},isLQIP:U,isSEOBot:N,lqipTransition:R,encoding:x,imageData:{width:s,height:l,uri:a,name:o,displayMode:d,hasAnimation:F,...h&&{quality:h},...u&&{devicePixelRatio:u},..._&&{focalPoint:_},...I&&{crop:I},...f&&{upscaleMethod:f}}})),[i,g,P,d,O,v,L,b,A,U,N,R,x,s,l,a,o,F,h,u,_,I,f]),z=Y.current,j=z?.uri,D=z?.srcset,V=z.css?.img,W=`${p} ${T}`;r.useEffect((()=>{const e=k.current;y&&e?.currentSrc&&e?.complete&&y({target:e})}),[]);const Z=z&&!z?.transformed?`max(${s}px, 100%)`:L?`${L}px`:null;return r.createElement("wow-image",{id:t,class:W,"data-image-info":$,"data-motion-part":`BG_IMG ${i}`,"data-bg-effect-name":m,"data-has-ssr-src":H,"data-animate-blur":!N&&U&&B?"":void 0,style:Z?{"--wix-img-max-width":Z}:{}},r.createElement("img",{src:j,ref:k,alt:n||"",role:c,style:{...V,...E},onLoad:y,onError:M,width:O||void 0,height:v||void 0,...G,srcSet:C?D?.dpr?.map((e=>at.test(e)?e:`${tt}${e}`)).join(", "):void 0,fetchpriority:C?"high":void 0,loading:!1===C?"lazy":void 0,suppressHydrationWarning:!0}))};var rt="Tj01hh";var ot=e=>{var t,a;const{id:n,alt:o,role:c,className:l,imageStyles:d={},targetWidth:u,targetHeight:h,onLoad:g,onError:m,containerWidth:p,containerHeight:_,isInFirstFold:f,socialAttrs:T,skipMeasure:I,responsiveImageProps:E,zoomedImageResponsiveOverride:L,displayMode:b}=e,A=u||p,y=h||_,{fallbackSrc:M,srcset:w,sources:O,css:v}=E||{},{width:S,height:C,...R}=(null==E||null==(t=E.css)?void 0:t.img)||{},G="original_size"===b?null==E||null==(a=E.css)?void 0:a.img:R;var N;return M&&w&&v?r.createElement("img",i()({fetchpriority:f?"high":void 0,loading:!1===f?"lazy":void 0,sizes:A+"px",srcSet:I?null==L?void 0:L.srcset:null==E?void 0:E.srcset,id:n,src:M,alt:o||"",role:c,style:{...d,...I?{...null==L||null==(N=L.css)?void 0:N.img}:{...G}},onLoad:g,onError:m,className:s(l,rt),width:A,height:y},T)):M&&O&&v?r.createElement("picture",null,O.map((e=>{let{srcset:t,media:i,sizes:a}=e;return r.createElement("source",{key:i,srcSet:t,media:i,sizes:a})})),r.createElement("img",i()({fetchpriority:f?"high":void 0,loading:!1===f?"lazy":void 0,id:n,src:O[0].fallbackSrc,alt:o||"",role:c,style:{...d,objectFit:O[0].imgStyle.objectFit,objectPosition:O[0].imgStyle.objectPosition},onLoad:g,onError:m,className:s(l,rt),width:A,height:y},T))):r.createElement(nt,e)};var ct=e=>{var t,i,a;const{className:n,customIdPrefix:o,getPlaceholder:c,hasAnimation:s,...l}=e,d=r.useMemo((()=>JSON.stringify({containerId:l.containerId,alignType:l.alignType,fittingType:l.displayMode,hasAnimation:s,imageData:{width:l.width,height:l.height,uri:l.uri,name:l.name,...l.quality&&{quality:l.quality},displayMode:l.displayMode}})),[l,s]),u=r.useRef(null);u.current||(u.current=c?c({fittingType:l.displayMode,src:{id:l.uri,width:l.width,height:l.height,name:l.name},target:{width:l.containerWidth,height:l.containerHeight,alignment:l.alignType,htmlTag:"bg"},options:{hasAnimation:s,allowAnimatedTransform:!1}}):{uri:void 0,css:{img:{}},attr:{img:{},container:{}}});const h=u.current,g=null!=(t=null==h?void 0:h.uri)?t:"",m=null!=(i=null==(a=h.css)?void 0:a.container)?i:{},p=Object.assign(g?{backgroundImage:"url("+g+")"}:{},m);return r.createElement("wix-bg-image",{id:""+(o||"bgImg_")+l.containerId,class:n,style:p,"data-tiled-image-info":d,"data-has-bg-scroll-effect":l.hasBgScrollEffect||"","data-bg-effect-name":l.bgEffectName||"","data-motion-part":"BG_IMG "+l.containerId})};const st=new RegExp("<%= compId %>","g"),lt=(e,t)=>e.replace(st,t);var dt=e=>null==e?void 0:e.replace(":hover",""),ut="bX9O_S",ht="Z_wCwr",gt="Jxk_UL",mt="K8MSra",pt="YTb3b4";const _t={quality:{unsharpMask:{radius:.33,amount:1,threshold:0}},devicePixelRatio:1};var ft=e=>{const{id:t,videoRef:a,videoInfo:n,posterImageInfo:o,muted:c,preload:l,loop:d,alt:u,isVideoEnabled:h,getPlaceholder:g,extraClassName:m=""}=e;n.containerId=dt(n.containerId);const p=r.useMemo((()=>JSON.stringify(n)),[n]),_=r.createElement(r.Fragment,null,o.filterEffectSvgString&&r.createElement("svg",{id:"svg_"+n.containerId,className:pt},r.createElement("defs",{dangerouslySetInnerHTML:{__html:lt(o.filterEffectSvgString,n.containerId)}})),r.createElement(ot,i()({key:n.videoId+"_img",id:o.containerId+"_img",className:s(ht,gt,"bgVideoposter",m),imageStyles:{width:"100%",height:"100%"}},o,_t,{getPlaceholder:g})));return h?r.createElement("wix-video",{id:t,"data-video-info":p,"data-motion-part":"BG_IMG "+n.containerId,class:s(ut,"bgVideo",m)},r.createElement("video",{key:n.videoId+"_video",ref:a,id:n.containerId+"_video",className:mt,crossOrigin:"anonymous","aria-label":u,playsInline:!0,preload:l,muted:c,loop:d}),_):_},Tt="SUz0WK";var It=e=>{const{id:t,containerId:i,pageId:a,children:n,bgEffectName:o="",containerSize:c}=e;return r.createElement("wix-bg-media",{id:t,class:Tt,"data-container-id":i,"data-container-size":((null==c?void 0:c.width)||0)+", "+((null==c?void 0:c.height)||0),"data-page-id":a,"data-bg-effect-name":o,"data-motion-part":"BG_MEDIA "+i},n)};const Et="bgOverlay";var Lt="m4khSP",bt="FNxOn5";var At=e=>{const{imageOverlay:t}=e;return r.createElement("div",{"data-testid":Et,className:Lt},t&&r.createElement(ct,i()({customIdPrefix:"bgImgOverlay_",className:bt},t)))};const yt="bgLayers",Mt="colorUnderlay",wt="mediaPadding",Ot="canvas";var vt="MW5IWV",St="N3eg0s",Ct="Kv1aVt",Rt="dLPlxY",Gt="VgO9Yg",Nt="LWbAav",Pt="yK6aSC",Ft="K_YxMd",xt="NGjcJN",kt="mNGsUM",Ht="I8xA4L";const Bt="bgImage";var Yt=e=>{const{videoRef:t,canvasRef:a,hasBgFullscreenScrollEffect:n,image:o,backgroundImage:c,backgroundMedia:l,video:d,backgroundOverlay:u,shouldPadMedia:h,extraClass:g="",shouldRenderUnderlay:m=!d,reducedMotion:p=!1,getPlaceholder:_,hasCanvasAnimation:f,useWixMediaCanvas:T,onClick:I}=e,{onImageLoad:E}=(e=>{let{onReady:t,image:i}=e;return(0,r.useEffect)((()=>{t&&!i&&t()}),[t,i]),{onImageLoad:e=>{null!=i&&i.onLoad&&i.onLoad(e),t&&t()}}})(e),L=dt(e.containerId),b="img_"+dt(L),A=o&&r.createElement(ot,i()({id:b,className:s(Ct,Rt,kt,Bt),imageStyles:{width:"100%",height:"100%"},getPlaceholder:_},o,{onLoad:E})),y=c&&r.createElement(ct,i()({},c,{containerId:L,className:s(Ct,Rt,kt,Bt),getPlaceholder:_})),M=d&&r.createElement(ft,i()({id:"videoContainer_"+L},d,{extraClassName:Pt,reducedMotion:p,videoRef:t,getPlaceholder:_})),w=T&&a||f?r.createElement("wix-media-canvas",{"data-container-id":L,class:f?Ht:""},A,y,M,r.createElement("canvas",{id:L+"webglcanvas",className:s(Ft,"webglcanvas"),"aria-label":(null==d?void 0:d.alt)||"",role:"presentation","data-testid":Ot})):r.createElement(r.Fragment,null,A,y,M,a&&r.createElement("canvas",{id:L+"webglcanvas",ref:a,className:s(Ft,"webglcanvas"),"aria-label":(null==d?void 0:d.alt)||"",role:"presentation","data-testid":Ot})),O=l?r.createElement(It,i()({id:"bgMedia_"+L},l),w):r.createElement("div",{id:"bgMedia_"+L,"data-motion-part":"BG_MEDIA "+L,className:Gt},w),v=u&&r.createElement(At,u);return r.createElement("div",{id:yt+"_"+L,"data-hook":yt,"data-motion-part":"BG_LAYER "+L,className:s(vt,g,{[St]:n}),onClick:I},m&&r.createElement("div",{"data-testid":Mt,className:s(Nt,Ct)}),h?r.createElement("div",{"data-testid":wt,className:xt},O,v):r.createElement(r.Fragment,null,O,v))};var Ut=e=>r.createElement(Yt,e),$t="dkukWC",zt="FRCqDF",jt="xnZvZH",Dt="MBOSCN";const Vt=(e,t,i)=>{const a=((e,t)=>e?[...Array(1+(t||0)).keys()].reverse().map((e=>r.createElement("div",{key:"divider-layer-"+e,style:{"--divider-layer-i":e},className:zt,"data-testid":"divider-layer-"+e,"data-divider-layer":e}))):null)(!!t,i);return t?r.createElement("div",{className:s($t,{[jt]:"top"===e,[Dt]:"bottom"===e}),"data-testid":e+"-divider"},a):null};var Wt=e=>{var t,i;const a=r.useMemo((()=>{var t;return Vt("top",null==e?void 0:e.hasTopDivider,null==e||null==(t=e.topLayers)?void 0:t.size)}),[null==e?void 0:e.hasTopDivider,null==e||null==(t=e.topLayers)?void 0:t.size]),n=r.useMemo((()=>{var t;return Vt("bottom",null==e?void 0:e.hasBottomDivider,null==e||null==(t=e.bottomLayers)?void 0:t.size)}),[null==e?void 0:e.hasBottomDivider,null==e||null==(i=e.bottomLayers)?void 0:i.size]);return r.createElement(r.Fragment,null,a,n)};var Zt={root:"section"},qt={"bg-underlay":"LWbAav",bgUnderlay:"LWbAav","layers-container":"MW5IWV",layersContainer:"MW5IWV",animate:"V7OeEw",move:"BHIo43",fade:"UvF1nu",scrollUp:"YzrQFb",RectangleArea:"xuzjBY",rectangleArea:"xuzjBY",DefaultAreaSkin:"O7Ybkb",defaultAreaSkin:"O7Ybkb","full-screen-scroll-effect":"GeNLDt",fullScreenScrollEffect:"GeNLDt","fill-layer":"K8pHFh",fillLayer:"K8pHFh","image-fill-layer":"TaweqS",imageFillLayer:"TaweqS","bg-media":"yzHyNT",bgMedia:"yzHyNT",videoFillLayer:"zBFCpO","alpha-canvas":"xjgrS3",alphaCanvas:"xjgrS3","media-padding-layer":"b3zSS0",mediaPaddingLayer:"b3zSS0",transforms:"Wsv3ak","media-canvas":"IiJMfn",mediaCanvas:"IiJMfn",RectangleAreaAfterScroll:"KJgt14",rectangleAreaAfterScroll:"KJgt14",scrolled:"VTwcX7",section:"Gzsk0j","video-play-pause-button":"MdLl0h",videoPlayPauseButton:"MdLl0h",childrenContainer:"Gmmci1"};const Jt=({size:e,...t})=>r.createElement("svg",{viewBox:"0 0 18 18",fill:"currentColor",width:e||"18",height:e||"18",...t},r.createElement("path",{d:"M7.5,5 C8.32842712,5 9,5.67157288 9,6.5 L9,11.5 C9,12.3284271 8.32842712,13 7.5,13 C6.67157288,13 6,12.3284271 6,11.5 L6,6.5 C6,5.67157288 6.67157288,5 7.5,5 Z M11.5,5 C12.3284271,5 13,5.67157288 13,6.5 L13,11.5 C13,12.3284271 12.3284271,13 11.5,13 C10.6715729,13 10,12.3284271 10,11.5 L10,6.5 C10,5.67157288 10.6715729,5 11.5,5 Z M7.5,6 C7.22385763,6 7,6.22385763 7,6.5 L7,11.5 C7,11.7761424 7.22385763,12 7.5,12 C7.77614237,12 8,11.7761424 8,11.5 L8,6.5 C8,6.22385763 7.77614237,6 7.5,6 Z M11.5,6 C11.2238576,6 11,6.22385763 11,6.5 L11,11.5 C11,11.7761424 11.2238576,12 11.5,12 C11.7761424,12 12,11.7761424 12,11.5 L12,6.5 C12,6.22385763 11.7761424,6 11.5,6 Z"}));Jt.displayName="PauseSmall";var Kt=Jt;const Xt=({size:e,...t})=>r.createElement("svg",{viewBox:"0 0 18 18",fill:"currentColor",width:e||"18",height:e||"18",...t},r.createElement("path",{d:"M6.87468837,5.45041947 L12.7318793,8.46657119 C13.20163,8.68731241 13.20163,9.26940918 12.7318793,9.53342881 L6.87468837,12.5495805 C6.58008377,12.7012867 6.00070071,12.5801226 6,12.0161517 L6,5.98384828 C6,5.65247743 6.35266876,5.20682168 6.87468837,5.45041947 Z M7,11.3602529 L11.5834735,9 L7,6.63974714 L7,11.3602529 Z"}));Xt.displayName="PlaySmall";var Qt=Xt;const ei=(e,t)=>{var a;const{id:n,skin:c="RectangleArea",className:l,containerRootClassName:u="",customClassNames:h=[],containerProps:g,children:p,fillLayers:_=e.fillLayers||e.background,tagName:f,getPlaceholder:T,dividers:I,semanticClassNames:E,onStop:L,onClick:b,onDblClick:A,onMouseEnter:y,onMouseLeave:M,lang:w,translations:O,isPlayPauseSectionExperimentOn:v}=e,S=f||"section",{shouldOmitWrapperLayers:C}=g,R=s(qt[c],u,l,E?d(E.root,...h):d(Zt.root,...h),{[qt.shouldOmitWrapperLayers]:C}),G=!(null==_||!_.video),N=function(e,t,i){const a=o().useRef(null),n=o().useRef(null);return t?n.current||(n.current={play:()=>a.current?.play(),load:()=>a.current?.load(),pause:()=>a.current?.pause(),stop:()=>{a.current&&(a.current.pause(),a.current.currentTime=0,i&&i(a.current))}}):n.current=null,o().useImperativeHandle(e,(()=>n.current||{load(){},stop(){}})),a}(t,G,L),[P,F]=r.useState(!(null!=N&&null!=(a=N.current)&&a.paused));return r.createElement(S,i()({id:n},(e=>Object.entries(e).reduce(((e,[t,i])=>(t.includes("data-")&&(e[t]=i),e)),{}))(e),((e={})=>{const t=e.tabIndex??e.tabindex??void 0;return void 0!==t?{tabIndex:Number(t)}:{}})(e.a11y||{tabIndex:-1}),{"data-block-level-container":"Section",className:R+" "+qt.section,"data-testid":m,onClick:b,onDoubleClick:A,onMouseEnter:y,onMouseLeave:M,lang:w}),_&&r.createElement(Ut,i()({},_,{videoRef:N,getPlaceholder:T})),G&&v&&r.createElement("button",{className:""+qt.videoPlayPauseButton,onClick:()=>{const e=null==N?void 0:N.current;e&&(e.paused?e.play():e.pause(),F((t=>{const i=!t,a=document.getElementById(n);if(a){a.querySelectorAll("video").forEach((t=>{t!==e&&(i?t.play():t.pause())}))}return i})))},"aria-pressed":P,"aria-label":null==O?void 0:O.ariaLabel},P?r.createElement(Kt,null):r.createElement(Qt,null)),I&&r.createElement(Wt,I),p())};const ti=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`));var ii;const ai={Section:{component:r.forwardRef(ei),controller:(ii=e=>{let{mapperProps:t,stateValues:i}=e;const{experiments:a={}}=i,n=((e,t)=>!0===e[t]||"true"===e[t]||"new"===e[t])(a,"specs.thunderbolt.playPauseSection");return{...t,isPlayPauseSectionExperimentOn:n}},{useComponentProps:(e,t,i)=>{const a=(e=>({...e,updateStyles:t=>{const i=Object.entries(t).reduce(((e,[t,i])=>{return{...e,[(a=t,a.startsWith("--")?t:ti(t))]:void 0===i?null:i};var a}),{});e.updateStyles(i)}}))(i);return ii({mapperProps:e,stateValues:t,controllerUtils:a})}})}}}(),n}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[Section].77eae6d3.bundle.min.js.map