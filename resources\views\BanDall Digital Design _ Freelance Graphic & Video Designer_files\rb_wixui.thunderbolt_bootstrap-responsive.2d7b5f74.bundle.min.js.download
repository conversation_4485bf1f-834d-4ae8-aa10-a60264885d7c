!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("lodash"),require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt_bootstrap-responsive",["lodash","react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt_bootstrap-responsive"]=t(require("lodash"),require("react")):e["rb_wixui.thunderbolt_bootstrap-responsive"]=t(e._,e.React)}("undefined"!=typeof self?self:this,(function(e,t){return function(){var n={65549:function(e,t,n){var r=n(84457)(n(70441),"DataView");e.exports=r},64438:function(e,t,n){var r=n(84457)(n(70441),"Map");e.exports=r},87076:function(e,t,n){var r=n(84457)(n(70441),"Promise");e.exports=r},69902:function(e,t,n){var r=n(84457)(n(70441),"Set");e.exports=r},54690:function(e,t,n){var r=n(70441).Symbol;e.exports=r},18965:function(e,t,n){var r=n(84457)(n(70441),"WeakMap");e.exports=r},94318:function(e,t,n){var r=n(54690),o=n(47077),a=n(61954),i=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":i&&i in Object(e)?o(e):a(e)}},58520:function(e,t,n){var r=n(94318),o=n(3387);e.exports=function(e){return o(e)&&"[object Arguments]"==r(e)}},62987:function(e,t,n){var r=n(93839),o=n(47275),a=n(85973),i=n(76822),s=/^\[object .+?Constructor\]$/,l=Function.prototype,u=Object.prototype,c=l.toString,d=u.hasOwnProperty,p=RegExp("^"+c.call(d).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!a(e)||o(e))&&(r(e)?p:s).test(i(e))}},73749:function(e,t,n){var r=n(94318),o=n(99216),a=n(3387),i={};i["[object Float32Array]"]=i["[object Float64Array]"]=i["[object Int8Array]"]=i["[object Int16Array]"]=i["[object Int32Array]"]=i["[object Uint8Array]"]=i["[object Uint8ClampedArray]"]=i["[object Uint16Array]"]=i["[object Uint32Array]"]=!0,i["[object Arguments]"]=i["[object Array]"]=i["[object ArrayBuffer]"]=i["[object Boolean]"]=i["[object DataView]"]=i["[object Date]"]=i["[object Error]"]=i["[object Function]"]=i["[object Map]"]=i["[object Number]"]=i["[object Object]"]=i["[object RegExp]"]=i["[object Set]"]=i["[object String]"]=i["[object WeakMap]"]=!1,e.exports=function(e){return a(e)&&o(e.length)&&!!i[r(e)]}},78803:function(e,t,n){var r=n(65003),o=n(41466),a=Object.prototype.hasOwnProperty;e.exports=function(e){if(!r(e))return o(e);var t=[];for(var n in Object(e))a.call(e,n)&&"constructor"!=n&&t.push(n);return t}},76535:function(e){e.exports=function(e){return function(t){return e(t)}}},38507:function(e,t,n){var r=n(70441)["__core-js_shared__"];e.exports=r},34414:function(e,t,n){var r="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;e.exports=r},84457:function(e,t,n){var r=n(62987),o=n(79741);e.exports=function(e,t){var n=o(e,t);return r(n)?n:void 0}},47077:function(e,t,n){var r=n(54690),o=Object.prototype,a=o.hasOwnProperty,i=o.toString,s=r?r.toStringTag:void 0;e.exports=function(e){var t=a.call(e,s),n=e[s];try{e[s]=void 0;var r=!0}catch(e){}var o=i.call(e);return r&&(t?e[s]=n:delete e[s]),o}},26686:function(e,t,n){var r=n(65549),o=n(64438),a=n(87076),i=n(69902),s=n(18965),l=n(94318),u=n(76822),c="[object Map]",d="[object Promise]",p="[object Set]",f="[object WeakMap]",m="[object DataView]",b=u(r),v=u(o),h=u(a),y=u(i),g=u(s),x=l;(r&&x(new r(new ArrayBuffer(1)))!=m||o&&x(new o)!=c||a&&x(a.resolve())!=d||i&&x(new i)!=p||s&&x(new s)!=f)&&(x=function(e){var t=l(e),n="[object Object]"==t?e.constructor:void 0,r=n?u(n):"";if(r)switch(r){case b:return m;case v:return c;case h:return d;case y:return p;case g:return f}return t}),e.exports=x},79741:function(e){e.exports=function(e,t){return null==e?void 0:e[t]}},47275:function(e,t,n){var r,o=n(38507),a=(r=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"";e.exports=function(e){return!!a&&a in e}},65003:function(e){var t=Object.prototype;e.exports=function(e){var n=e&&e.constructor;return e===("function"==typeof n&&n.prototype||t)}},41466:function(e,t,n){var r=n(53717)(Object.keys,Object);e.exports=r},91782:function(e,t,n){e=n.nmd(e);var r=n(34414),o=t&&!t.nodeType&&t,a=o&&e&&!e.nodeType&&e,i=a&&a.exports===o&&r.process,s=function(){try{var e=a&&a.require&&a.require("util").types;return e||i&&i.binding&&i.binding("util")}catch(e){}}();e.exports=s},61954:function(e){var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},53717:function(e){e.exports=function(e,t){return function(n){return e(t(n))}}},70441:function(e,t,n){var r=n(34414),o="object"==typeof self&&self&&self.Object===Object&&self,a=r||o||Function("return this")();e.exports=a},76822:function(e){var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},86981:function(e,t,n){var r=n(58520),o=n(3387),a=Object.prototype,i=a.hasOwnProperty,s=a.propertyIsEnumerable,l=r(function(){return arguments}())?r:function(e){return o(e)&&i.call(e,"callee")&&!s.call(e,"callee")};e.exports=l},77236:function(e){var t=Array.isArray;e.exports=t},81580:function(e,t,n){var r=n(93839),o=n(99216);e.exports=function(e){return null!=e&&o(e.length)&&!r(e)}},98752:function(e,t,n){e=n.nmd(e);var r=n(70441),o=n(7149),a=t&&!t.nodeType&&t,i=a&&e&&!e.nodeType&&e,s=i&&i.exports===a?r.Buffer:void 0,l=(s?s.isBuffer:void 0)||o;e.exports=l},86834:function(e,t,n){var r=n(78803),o=n(26686),a=n(86981),i=n(77236),s=n(81580),l=n(98752),u=n(65003),c=n(14812),d=Object.prototype.hasOwnProperty;e.exports=function(e){if(null==e)return!0;if(s(e)&&(i(e)||"string"==typeof e||"function"==typeof e.splice||l(e)||c(e)||a(e)))return!e.length;var t=o(e);if("[object Map]"==t||"[object Set]"==t)return!e.size;if(u(e))return!r(e).length;for(var n in e)if(d.call(e,n))return!1;return!0}},93839:function(e,t,n){var r=n(94318),o=n(85973);e.exports=function(e){if(!o(e))return!1;var t=r(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},99216:function(e){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},85973:function(e){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},3387:function(e){e.exports=function(e){return null!=e&&"object"==typeof e}},14812:function(e,t,n){var r=n(73749),o=n(76535),a=n(91782),i=a&&a.isTypedArray,s=i?o(i):r;e.exports=s},7149:function(e){e.exports=function(){return!1}},60484:function(t){"use strict";t.exports=e},5329:function(e){"use strict";e.exports=t},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},r={};function o(e){var t=r[e];if(void 0!==t)return t.exports;var a=r[e]={id:e,loaded:!1,exports:{}};return n[e](a,a.exports,o),a.loaded=!0,a.exports}o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,{a:t}),t},o.d=function(e,t){for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e};var a={};return function(){"use strict";o.r(a),o.d(a,{components:function(){return xe}});var e=o(448),t=o.n(e),n=o(5329),r=o.n(n);function i(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=i(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}var s=function(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=i(e))&&(r&&(r+=" "),r+=t);return r};const l=(e,t)=>e?{"data-comp":t,"data-aid":t}:{},u="wixui-",c=(e,...t)=>{const n=[];return e&&n.push(`${u}${e}`),t.forEach((e=>{e&&(n.push(`${u}${e}`),n.push(e))})),n.join(" ")},d=new Set(["PointerMenuButtonHorizontalMenuAdaptationSkin","PointerMenuButtonSkin","VerticalRibbonsMenuButtonSkin","RibbonsMenuButtonSkin"]),p="data-dropdown-shown",f="__more__",m="SCROLL_TO_TOP",b="SCROLL_TO_BOTTOM";var v={root:"dropdown-menu",menuItem:"dropdown-menu__item",subMenu:"dropdown-menu__submenu"};const h=13,y=27;function g(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}const x=g(32),k=g(h),w=e=>{k(e),x(e)},I=(g(y),["aria-id","aria-metadata","aria-type"]),C=(e,t)=>Object.entries(e).reduce(((e,[n,r])=>(t.includes(n)||(e[n]=r),e)),{}),j=e=>{const{role:t,tabIndex:n,tabindex:r,screenReader:o,lang:a,ariaAttributes:i={}}=e,s=Object.entries(i).reduce(((e,[t,n])=>({...e,[`aria-${t}`.toLowerCase()]:n})),{});return{role:t,tabIndex:n??r,screenReader:o,ariaAttributes:C(s,I),lang:a}},M=e=>e.split("?")[0],E=(e,t)=>e.filter((e=>e.link&&e.link.linkPopupId&&e.link.linkPopupId===t)),N=(e,t,n)=>{const r=n.compId||n.dataId;let o=new Set;return r&&(o=new Set(e.filter((e=>((e,t,n)=>e.link&&(!e.link.href||M(e.link.href)===M(t))&&(e.link.anchorCompId&&e.link.anchorCompId===n.compId||e.link.anchorDataId&&e.link.anchorDataId===n.dataId&&e.link.anchorDataId!==m&&e.link.anchorDataId!==b))(e,t,n))))),o},O=function(e,t,n,r){void 0===e&&(e=[]),void 0===t&&(t=""),void 0===n&&(n={}),void 0===r&&(r="");const o=new Set([...Array.from(N(e,t,n)),...Array.from(E(e,r))]),a=o.size>0;return e.forEach((e=>{const i=e.items&&e.items.length?O(e.items,t,n,r):new Set;!1!==e.selected&&((e.selected||((e,t,n)=>!n&&!(e=>e.link&&(e.link.anchorDataId||e.link.anchorCompId))(e)&&e.link&&e.link.href&&decodeURIComponent(M(e.link.href))===M(t))(e,t,a)||e.link&&Object.keys(e.link).length>0&&i.size>0)&&o.add(e),i.forEach((e=>o.add(e))))})),o};function S(e,t){return""+e+t}function B(e,t){return e.filter(((e,n)=>{var r;const o=S(t,n.toString()),a=document.getElementById(o);return"hidden"===(null==a||null==(r=a.style)?void 0:r.visibility)}))}var P=o(86834),A=o.n(P),D=o(60484);const T=e=>{const r=n.useMemo((()=>O(e.items,e.currentUrl,e.activeAnchor,e.currentPopupId)),[e.items,e.currentUrl,e.activeAnchor,e.currentPopupId]),{ariaAttributes:o}=e,a=(e,t)=>{const{Button:r}=e,o={...{onMouseEnter:e.onItemMouseEnter,onMouseLeave:e.onItemMouseLeave,onDoubleClick:e.onItemDblClick,onClick:e.onItemClick,textAlign:e.alignText,translations:e.translations},...t};return n.createElement(r,o)};function i(e,t,n,r,o,a){return e===t-1?1===t?"dropLonely":n?"bottom":a||"right"===o?r?"left":"right":"center":0===e?n?"top":a||"left"===o?r?"right":"left":"center":n?"dropCenter":"center"}const l=(e,t)=>{let n=e,r=0;for(;t[n]&&r<100;)n+=t[n]++,r++;return t[n]=(t[n]||0)+1,n},u=(e,t)=>{let{items:n=[],compClassName:o,dropdown:s,rtl:u,buttonAlign:c,stretch:d}=t;const{hover:p}=e,f={};return n.map(((t,m)=>{var b,v,y,g;const x=null!=(b=t.hasPopup)?b:(null!=(v=null==t||null==(y=t.items)?void 0:y.length)?v:0)>0,k=(s?"moreContainer":"")+m,w={isContainer:s,isSelected:r.has(t),positionInList:t.positionInList||i(m,n.length,s,u,c,d),id:h(k),index:m,refInParent:k,isDropDownButton:s,...r.has(t)&&{"aria-current":"page"},...x&&{"aria-haspopup":"true","aria-expanded":!(0,D.isNil)(p)&&parseInt(p,10)===m},tagName:"li",direction:u?"rtl":"ltr",parentId:t.parent,dataId:t.id,label:t.label,link:t.link,compClassName:o,key:l(t.label,f),subItems:s||null==(g=t.items)?void 0:g.map((e=>({...e,...r.has(e)&&{"aria-current":"page"}}))),role:x&&A()(t.link)?"button":void 0};return a(e,w)}))};function m(e){const{styles:t,items:n,rtl:r,stretchButtonsToMenuWidth:o,alignButtons:i="center"}=e,l=u(e,{items:n,compClassName:s(t.menuItem,c(v.menuItem)),rtl:r,buttonAlign:i,stretch:o}),d=function(e){const{rtl:t,styles:n,stretchButtonsToMenuWidth:r,alignButtons:o="center",moreButtonLabel:i,onItemMouseEnter:s,onItemMouseLeave:l,onSubMenuKeyDown:u}=e,c=f;let d=t?"left":"right";r||"right"===o||(d="center");const p={label:i||"",isSelected:!1,positionInList:d,id:h(c),index:f,refInParent:c,key:c,onFocus:s,onBlur:l,"aria-haspopup":"true",tagName:"li",onKeyDown:u,isDropDownButton:!1,compClassName:n.moreButton,isMoreButton:!0};return a(e,p)}(e);return d&&l.push(d),l}function b(t){var r;const{alignButtons:o="center",onSubMenuKeyDown:a,hover:i,styles:l}=t,d=function(t){const{items:n,rtl:r,alignButtons:o="center",stretchButtonsToMenuWidth:a,hover:i,styles:s}=t;let l=null,c=[];if(n&&i){const t=parseInt(i,10);Number.isInteger(t)&&n[t]?l=n[t].items:i===f&&(l=function(e){return e.reduce(((e,t)=>{let n=[];return t.items&&(n=t.items.map((e=>({...e,parent:t.id})))),[...e,t,...n]}),[])}(B(n,e.id)))}return l&&(c=u(t,{items:l,compClassName:s.dropdownButton,dropdown:!0,rtl:r,buttonAlign:o,stretch:a})),c}(t),m=h("moreContainer"),b=h("dropWrapper"),y=(null!=(r=null==d?void 0:d.length)?r:0)>0,g=s(l.dropWrapper,{[l.showMore]:y}),x=y,k=function(e){const{hover:t,hoverListPosition:n}=e;return t?n:null}(t);return n.createElement("div",{className:g,id:b,"data-drophposition":k,"data-dropalign":o,[p]:x},n.createElement("ul",{className:s(l.moreContainer,c(v.subMenu)),"data-hover":i,id:m,onKeyDown:a},d))}const h=t=>S(e.id,t),y=j({role:e.role,ariaAttributes:o});return function(e){const{translations:r,styles:o}=e,a=function(e){const{styles:t,skin:r,alignButtons:o="center",marginAllChildren:a,onMenuKeyDown:i}=e,l=m(e),u=h("itemsContainer");let c=n.createElement("ul",{className:s(t.itemsContainer,t[""+o]),id:u,style:{textAlign:o},"data-marginallchildren":a,onKeyDown:i},l);if((()=>{switch(r){case"IndentedMenuButtonSkin":case"ShinyMenuIIButtonSkin":case"SloppyBorderMenuButtonSkin":return!0;default:return!1}})()){const e=h("wrapper");c=n.createElement("div",{className:s(t.itemsContainerWrapper),id:e},c)}return c}(e),i=function(e){const{skin:t,styles:r}=e;let o=null;return d.has(t)&&(o=n.createElement("div",{className:r.utility})),o}(e),l=b(e),u=h("navContainer");return n.createElement("nav",t()({className:s(o.navContainer),id:u,"aria-label":r.ariaLabel},y.ariaAttributes,{role:y.role,onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave}),i,a,l,(c=r.subMenuIndication,n.createElement("div",{style:{display:"none"},id:h("navContainer")+"-hiddenA11ySubMenuIndication"},c)));var c}(e)},L={hover:null,hoverListPosition:null};var _=e=>{const[r,o]=n.useState(L),a=n.useRef();let i;const u=t=>{var n;const{hover:o}=r,{id:a,items:i}=e,s=t.getAttribute("data-index")||"-1",l=parseInt(s,10);if((null==t||null==(n=t.parentNode)?void 0:n.id)!==a+"moreContainer")return i[l];if(!o)return null;if(o===f){return B(i,a)[l]}return i[parseInt(o,10)].items[l]},d=t=>{const{onItemMouseIn:n}=e,{currentTarget:r}=t;null==n||n(t,u(r)),p(t)},p=t=>{var n;const{hover:a}=r,{id:s}=e,{currentTarget:l}=t,u=l.getAttribute("data-listposition"),c=l.getAttribute("data-index")||"-1",d=parseInt(c,10);clearTimeout(i);(null==l||null==(n=l.parentNode)?void 0:n.id)!==s+"moreContainer"&&(Number.isInteger(d)&&-1!==d||c.startsWith("__"))&&c!==a&&o({hover:c,hoverListPosition:u})},m=t=>{const{onItemMouseOut:n}=e,{currentTarget:r}=t;null==n||n(t,u(r)),b(t)},b=e=>{e.nativeEvent instanceof MouseEvent?i=setTimeout((()=>{o({hover:null,hoverListPosition:null})}),1e3):o({hover:null,hoverListPosition:null})},h=t=>{const{onItemDblClick:n}=e,{currentTarget:r}=t;null==n||n(t,u(r))},y=t=>{const{hover:n}=r,{currentTarget:o}=t,{items:a,onItemClick:i,isTouchDevice:s}=e;if(null==i||i(t,u(o)),s){var l;const e=o.getAttribute("data-index")||"-1",r="true"===o.getAttribute("data-dropdown"),i=parseInt(e,10),s=a?a[i]:null,u=e===f||(null==s||null==(l=s.items)?void 0:l.length)>0;r?b(t):n?(b(t),u&&n!==e&&(t.preventDefault(),t.stopPropagation(),p(t))):u&&(p(t),t.preventDefault(),t.stopPropagation())}},g=function(t,n){if(void 0===n&&(n=!1),a.current){const{id:o}=e;let i=a.current.querySelector("#"+o+"itemsContainer > li:nth-child("+(t+1)+")");for(;i&&"true"===i.getAttribute("aria-hidden");)i=n?i.previousSibling:i.nextSibling;if(i){var r;const e=i.querySelector("button")||(null==(r=i.childNodes)?void 0:r[0]);if(e)return e.focus(),!0}}return!1},x=t=>{const{hover:n}=r,{items:o}=e,{key:i,shiftKey:s}=t;if(null!==n){const r=n?parseInt(n,10):-1;let l=!1;if("Tab"===i&&!s&&o){const t=o[r];t&&t.items&&(l=(t=>{const{id:n}=e;if(a.current){const e=a.current.querySelector("#"+n+"moreContainer li:nth-child("+(t+1)+") a");if(e)return e.focus(),!0}return!1})(0))}l&&(t.stopPropagation(),t.preventDefault())}},k=t=>{const{hover:n}=r,{items:o}=e,{shiftKey:a,key:i,target:s,currentTarget:l}=t;let u=s;if(s!==l&&"li"!==s.tagName.toLowerCase()&&(u=s.closest("li")),u){const e=u.getAttribute("data-index")||"";let r=!1;if(n){const s=((e,t)=>{const n=parseInt(e,10);return Number.isNaN(n)?t:n})(n,-1);if("Escape"===i&&(r=g(s,a)),"Tab"===i){const n=parseInt(e,10);if(s>=0)if(a)0===n&&(r=g(s,a),m(t));else if(o&&o[s]){const e=o[s];e&&e.items&&e.items.length===n+1&&(r=g(s+1),m(t))}}}r&&(t.stopPropagation(),t.preventDefault())}};function w(e,t){const{hover:n,hoverListPosition:r}=t,{stretchButtonsToMenuWidth:o,sameWidthButtons:a,skinExports:i,alignButtons:s="center",items:u,isQaMode:c,fullNameCompType:d}=e;return{"data-stretch-buttons-to-menu-width":o,"data-same-width-buttons":a,"data-num-items":null==u?void 0:u.length,"data-menuborder-y":i.menuBorderY,"data-menubtn-border":i.menuBtnBorder,"data-ribbon-els":i.ribbonEls,"data-label-pad":i.labelPad,"data-ribbon-extra":i.ribbonExtra,"data-drophposition":r,"data-dropalign":s,"data-hovered-item":n,...l(c,d)}}return function(r,o){const{id:i,className:l,customClassNames:u=[],skin:p,rtl:f,styles:b,lang:g}=r,I={id:i,class:s(b[p],b.wrapper,l,c(v.root,...u),"hidden-during-prewarmup"),ref:a,tabIndex:-1,dir:f?"rtl":"ltr",lang:g,...w(r,o)};return n.createElement("wix-dropdown-menu",I,n.createElement(T,t()({},r,o,{onItemMouseEnter:e.isTouchDevice?void 0:d,onItemMouseLeave:e.isTouchDevice?void 0:m,onItemDoubleClick:e.onItemDblClick?h:void 0,onItemClick:e.isTouchDevice||e.onItemClick?y:void 0,onMenuKeyDown:x,onSubMenuKeyDown:k})))}(e,r)};const R=e=>Object.entries(e).reduce(((e,[t,n])=>(t.includes("data-")&&(e[t]=n),e)),{});const W={root:"linkElement"},F=(e,r)=>{const{href:o,role:a,target:i,rel:s,className:l="",children:u,linkPopupId:c,anchorDataId:d,anchorCompId:p,tabIndex:f,dataTestId:m=W.root,title:b,onClick:v,onDoubleClick:h,onMouseEnter:y,onMouseLeave:g,onFocus:I,onFocusCapture:C,onBlurCapture:j,"aria-live":M,"aria-disabled":E,"aria-label":N,"aria-labelledby":O,"aria-pressed":S,"aria-expanded":B,"aria-describedby":P,"aria-haspopup":A,"aria-current":D,dataPreview:T,dataPart:L}=e,_=void 0!==e.activateByKey?e.activateByKey:(e=>e?"SpaceOrEnter":"Space")(c);let F;switch(_){case"Enter":F=k;break;case"Space":F=x;break;case"SpaceOrEnter":F=w;break;default:F=void 0}return void 0!==o||c?n.createElement("a",t()({},R(e),{"data-testid":m,"data-popupid":c,"data-anchor":d,"data-anchor-comp-id":p,"data-preview":T,"data-part":L,href:o||void 0,target:i,role:c?"button":a,rel:s,className:l,onKeyDown:F,"aria-live":M,"aria-disabled":E,"aria-label":N,"aria-labelledby":O,"aria-pressed":S,"aria-expanded":B,"aria-haspopup":A,"aria-describedby":P,"aria-current":D,title:b,onClick:v,onMouseEnter:y,onMouseLeave:g,onDoubleClick:h,onFocus:I,onFocusCapture:C,onBlurCapture:j,ref:r,tabIndex:c?0:f}),u):n.createElement("div",t()({},R(e),{"data-testid":m,"data-preview":T,"data-part":L,className:l,tabIndex:f,"aria-label":N,"aria-labelledby":O,"aria-haspopup":A,"aria-disabled":E,"aria-expanded":B,title:b,role:a,onClick:v,onDoubleClick:h,onMouseEnter:y,onMouseLeave:g,ref:r}),u)};var K=n.forwardRef(F);var U=e=>{let{wrapperProps:{ariaHasPopup:n,isMoreButton:o,ariaDescribedBy:a,ariaExpanded:i,ariaCurrent:s,role:l},className:u,children:c,link:d,tabIndex:p}=e;return r().createElement(K,t()({},d,{"aria-haspopup":n,"aria-describedby":a,"aria-current":s,"aria-expanded":i,tabIndex:p||(!o&&d&&d.href?void 0:0),className:u,role:l}),c)};const $=e=>{let{dir:t,textAlign:n,className:o,children:a,tagName:i="p",id:s}=e;return r().createElement(i,{className:o,style:{textAlign:n},dir:t,id:s+"label"},a)};var q=e=>{let{wrapperProps:{dir:t,textAlign:n,id:o},classNames:a,children:i}=e;return r().createElement("div",{className:s(a.bg),style:{textAlign:n}},r().createElement($,{dir:t,textAlign:n,className:a.label,id:o},i))},H=()=>r().createElement("svg",{width:"10",height:"10",viewBox:"0 0 16 11",fill:"black",xmlns:"http://www.w3.org/2000/svg"},r().createElement("path",{d:"M8 10.5L16 1.86193L14.7387 0.5L8 7.77613L1.26133 0.499999L-5.95321e-08 1.86193L8 10.5Z"})),V="_pfxlW",z="RG3k61";var G=e=>{var r;const{label:o,direction:a="ltr",positionInList:i,parentId:l,dataId:u,isContainer:c,isSelected:d,isHovered:p,link:f,tagName:m="div",id:b,className:v,compClassName:h,onClick:y,onDoubleClick:g,onMouseEnter:x,onMouseLeave:k,index:w,children:I,isDropDownButton:C,subItems:j}=e,[M,E]=n.useState(!1),N=j&&j.length>0,O=e=>e.nativeEvent instanceof MouseEvent,S=e=>{M||(E(!0),null==x||x(e))},B=e=>{M&&(E(!1),null==k||k(e))},P=[c?"drop":"menu",d&&"selected",p&&"over",f&&(f.hasOwnProperty("href")||f.hasOwnProperty("target")||f.hasOwnProperty("rel")||f.hasOwnProperty("linkPopupId"))?"link":"header"],A={...R(e),"data-direction":a,"data-listposition":i,"data-parent-id":l,"data-data-id":u,"data-state":P.join(" "),"data-index":w,"data-dropdown":C},D=e=>e?e.trim():"\xa0",T=N?n.createElement("ul",{"aria-hidden":!0,style:{display:"none"}},j.map(((e,t)=>{const{hasPopup:r,"aria-current":o}=e;return n.createElement("li",{key:e.id||t},n.createElement(U,{wrapperProps:{ariaHasPopup:r,ariaCurrent:o},link:e.link,tabIndex:-1,compClassName:h},D(e.label)))}))):null;return n.createElement(m,t()({id:b},A,{className:s(h,v),onClick:y,onDoubleClick:g,onMouseEnter:S,onMouseLeave:B,onFocus:e=>{O(e)&&S(e)},onBlur:e=>{O(e)?B(e):E(!1)},onKeyDown:e=>{"Escape"===e.key&&(M&&E(!1),null==k||k(e))}}),I(D(o)),N&&n.createElement("button",{className:s(V,{[z]:M}),onKeyDown:e=>{if("Enter"===e.key||" "===e.key){var t,n;if(!M)E(!0),null==x||x({...e,currentTarget:null==(t=e.currentTarget)?void 0:t.parentNode});if(M)E(!1),null==k||k({...e,currentTarget:null==(n=e.currentTarget)?void 0:n.parentNode})}},"aria-label":(null==e||null==(r=e.translations)||null==(r=r.dropdownButtonAriaLabel)?void 0:r.replace("<%= itemName %>",D(o)))||"More pages"},n.createElement(H,null)),T)};var X=e=>{const{id:r,"aria-haspopup":o,"aria-describedby":a,"aria-current":i,"aria-expanded":l,isMoreButton:u,dir:c,textAlign:d,positionInList:p,link:f,skinsStyle:m,skin:b,role:v}=e;return n.createElement(G,t()({},e,{className:s(e.className,m[b])}),(e=>n.createElement(U,{wrapperProps:{positionInList:p,ariaHasPopup:o,ariaDescribedBy:a,isMoreButton:u,ariaExpanded:l,ariaCurrent:i,role:v},link:f,className:m.linkElement},n.createElement("div",{className:m.wrapper},n.createElement(q,{wrapperProps:{dir:c,textAlign:d,id:r},classNames:{bg:m.bg,label:m.label}},e)))))},J={root:"r4OX7l",TextOnlyMenuButtonNSkin:"xTjc1A",textOnlyMenuButtonNSkin:"xTjc1A",linkElement:"UiHgGh",wrapper:"yRj2ms",label:"JS76Uv"};var Z=e=>n.createElement(X,t()({},e,{skinsStyle:J,skin:"TextOnlyMenuButtonNSkin"})),Y={wrapper:"NHM1d1",navContainer:"R_TAzU",itemsContainerWrapper:"aOF1ks",itemsContainer:"y7qwii",menuItem:"Tg1gOB",moreButton:"p90CkU",dropdownButton:"mvZ3NH",dropWrapper:"h3jCPd",moreContainer:"wkJ2fp",showMore:"DlGBN0",utility:"vh74Xw",TextOnlyMenuButtonSkin:"XwCBRN",textOnlyMenuButtonSkin:"XwCBRN"};const Q=(e,r)=>n.createElement(_,t()({},e,{ref:r,styles:Y,Button:Z}));var ee=n.forwardRef(Q);const te=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`));var ne,re=(ne=e=>{let{stateValues:t,mapperProps:n}=e;const{currentUrl:r}=t;return{...n,currentUrl:r}},{useComponentProps:(e,t,n)=>{const r=(e=>({...e,updateStyles:t=>{const n=Object.entries(t).reduce(((e,[t,n])=>{return{...e,[(r=t,r.startsWith("--")?t:te(t))]:void 0===n?null:n};var r}),{});e.updateStyles(n)}}))(n);return ne({mapperProps:e,stateValues:t,controllerUtils:r})}});const oe="page-bg";var ae={root:"page"},ie="P0dCOY",se="PJ4KCX";var le=e=>{let{id:t,className:n,customClassNames:o=[],pageDidMount:a,onClick:i,onDblClick:l,children:u,onMouseEnter:d,onMouseLeave:p}=e;return r().createElement("div",{id:t,className:s(ie,n),ref:a,onClick:i,onDoubleClick:l,onMouseEnter:d,onMouseLeave:p},r().createElement("div",{className:s(se,c(ae.root,...o)),"data-testid":oe}),r().createElement("div",null,u()))};const ue=13,ce=27;function de(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}de(32),de(ue),de(ce);const pe=["aria-id","aria-metadata","aria-type"],fe=(e,t)=>Object.entries(e).reduce(((e,n)=>{let[r,o]=n;return t.includes(r)||(e[r]=o),e}),{}),me=function(e){let{role:t,tabIndex:n,tabindex:r,...o}=void 0===e?{}:e;const a=Object.entries(o).reduce(((e,t)=>{let[n,r]=t;return{...e,[("aria-"+n).toLowerCase()]:r}}),{role:t,tabIndex:null!=n?n:r});return Object.keys(a).forEach((e=>{void 0!==a[e]&&null!==a[e]||delete a[e]})),fe(a,pe)},be="responsive-container-overflow",ve="responsive-container-content";var he="xpmKd_";const ye=r().forwardRef(((e,t)=>{let{children:n,className:o}=e;return r().createElement("div",{className:s(o,he),"data-testid":be,ref:t},n)})),ge=(e,t)=>{let{containerLayoutClassName:o,overlowWrapperClassName:a,hasOverflow:i,hasScrollOverflow:l,shouldOmitWrapperLayers:u,children:c,role:d,label:p,extraRootClass:f="",ariaLive:m,ariaAttributes:b,tabIndex:v,tagName:h="div"}=e;const y=!u&&i,g=y?l?v||0:-1:void 0,x=(0,n.useCallback)((e=>y?r().createElement(ye,{className:s(a,f)},e):e),[y,a,f]),k=i?o:s(o,f),w={ref:t,"data-testid":ve,tabIndex:g,...d?{role:d}:{},...p?{"aria-label":p}:{},...m?{"aria-live":m}:{},...me(b)};return"multi-column-layouter"===h?(w.class=k,w.style={visibility:"hidden"}):w.className=k,x(u?r().createElement(r().Fragment,null,c()):r().createElement(h||"div",w,c()))};const xe={DropDownMenu_TextOnlyMenuButtonSkin:{component:ee,controller:re},Page_ResponsivePageWithColorBG:{component:le},ResponsiveContainer:{component:r().forwardRef(ge)}}}(),a}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt_bootstrap-responsive.2d7b5f74.bundle.min.js.map