!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[SkipToContentButton]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[SkipToContentButton]"]=t(require("react")):e["rb_wixui.thunderbolt[SkipToContentButton]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},n={};function r(e){var o=n[e];if(void 0!==o)return o.exports;var u=n[e]={exports:{}};return t[e](u,u.exports,r),u.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};return function(){"use strict";r.r(o),r.d(o,{components:function(){return c}});var e=r(448),t=r.n(e),n=r(5329);function u(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=u(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}var i=function(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=u(e))&&(r&&(r+=" "),r+=t);return r};var a="LHrbPP";const c={SkipToContentButton:{component:e=>{const{id:r,className:o,translations:u}=e,[c,s]=n.useState(!1);n.useEffect((()=>{const e=document.querySelector("header");s(!!e&&Array.from(e.querySelectorAll('a[href], button, input, textarea, select, summary, details, iframe, object, embed, [contenteditable], [tabindex]:not([tabindex="-1"])')).some((e=>!(e.hasAttribute("disabled")||"true"===e.getAttribute("aria-hidden")||"true"===e.getAttribute("aria-disabled")))))}),[]);return c?n.createElement("button",t()({id:r},(e=>Object.entries(e).reduce(((e,[t,n])=>(t.includes("data-")&&(e[t]=n),e)),{}))(e),{key:r,className:i(o,a,"has-custom-focus"),tabIndex:0,onClick:()=>{const e=document.querySelector("[data-main-content]")||document.querySelector("[data-main-content-parent]>section:first-of-type");null==e||e.focus()}}),u.buttonLabel):null}}}}(),o}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[SkipToContentButton].69abe737.bundle.min.js.map