!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[Container_ResponsiveBox]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[Container_ResponsiveBox]"]=t(require("react")):e["rb_wixui.thunderbolt[Container_ResponsiveBox]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={exports:{}};return t[e](a,a.exports,n),a.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};return function(){"use strict";n.r(o),n.d(o,{components:function(){return I}});var e=n(448),t=n.n(e),r=n(5329);function a(e){var t,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(r=a(e[t]))&&(n&&(n+=" "),n+=r);else for(t in e)e[t]&&(n&&(n+=" "),n+=t);return n}var s=function(){for(var e,t,r=0,n="";r<arguments.length;)(e=arguments[r++])&&(t=a(e))&&(n&&(n+=" "),n+=t);return n};const i=13,u=27;function c(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}c(32),c(i),c(u);const l=["aria-id","aria-metadata","aria-type"],f=(e,t)=>Object.entries(e).reduce(((e,[r,n])=>(t.includes(r)||(e[r]=n),e)),{}),d="wixui-",p=(e,...t)=>{const r=[];return e&&r.push(`${d}${e}`),t.forEach((e=>{e&&(r.push(`${d}${e}`),r.push(e))})),r.join(" ")};var b="J6KGih";var v={root:"repeater",repeaterItem:"repeater__item"},x="rM7ckN",m="YJEKQk",y="InKIFF",h="YAf4Ti";const C=(e,n)=>{var o;const{id:a,className:i,containerRootClassName:u="",customClassNames:c=[],children:d,role:C,onClick:g,onKeyPress:_,onDblClick:j,onFocus:I,onBlur:O,onMouseEnter:k,onMouseLeave:N,hasPlatformClickHandler:w,translations:M,a11y:R={},ariaAttributes:E={},tabIndex:P,isRepeaterItem:L=!1,observeChildListChange:B,containerProps:D,shouldUseContainerLayoutClass:K,lang:S}=e,{"aria-label-interactions":$,tabindex:A,...F}=R;$&&(F["aria-label"]=(null==M?void 0:M.ariaLabel)||"Interactive element, focus to trigger content change");const T=r.useRef(null);r.useImperativeHandle(n,(()=>({focus:()=>{var e;null==(e=T.current)||e.focus()},blur:()=>{var e;null==(e=T.current)||e.blur()}})));const q=()=>L?v.repeaterItem:e.semanticClassNames?e.semanticClassNames.root:"";return r.useEffect((()=>{B&&null!=T&&T.current&&B(a,T.current)}),[]),r.createElement("div",t()({id:a},(e=>Object.entries(e).reduce(((e,[t,r])=>(t.includes("data-")&&(e[t]=r),e)),{}))(e),{ref:T},F,{lang:S},(({role:e,tabIndex:t,tabindex:r,...n}={})=>{const o=Object.entries(n).reduce(((e,[t,r])=>({...e,[`aria-${t}`.toLowerCase()]:r})),{role:e,tabIndex:t??r});return Object.keys(o).forEach((e=>{void 0!==o[e]&&null!==o[e]||delete o[e]})),f(o,l)})({...E,role:null!=(o=F.role)?o:C}),((e={})=>{const t=e.tabIndex??e.tabindex??void 0;return void 0!==t?{tabIndex:Number(t)}:{}})({tabIndex:P,tabindex:A}),{className:s(x,m,u,i,{[b]:w},p(q(),...c)),onDoubleClick:j,onClick:g,onKeyDown:e=>{_&&(" "===e.key&&e.preventDefault(),_(e))},onFocus:I,onBlur:O,onMouseEnter:k,onMouseLeave:N}),r.createElement("div",{className:s(h,y,p(q(),...c),{[D.containerLayoutClassName]:K})}),d())};var g=r.forwardRef(C);var _={root:"box"};const j=(e,n)=>r.createElement(g,t()({},e,{ref:n,semanticClassNames:_}));const I={Container_ResponsiveBox:{component:r.forwardRef(j)}}}(),o}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[Container_ResponsiveBox].8d9a34cd.bundle.min.js.map