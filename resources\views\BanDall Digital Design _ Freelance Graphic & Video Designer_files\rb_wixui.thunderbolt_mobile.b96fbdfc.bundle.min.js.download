!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt_mobile",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt_mobile"]=t(require("react")):e["rb_wixui.thunderbolt_mobile"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={96114:function(e,t,a){var n;!function(t){"use strict";var i=function(){},r=t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.msRequestAnimationFrame||function(e){return setTimeout(e,16)};function o(){var e=this;e.reads=[],e.writes=[],e.raf=r.bind(t),i("initialized",e)}function s(e){e.scheduled||(e.scheduled=!0,e.raf(c.bind(null,e)),i("flush scheduled"))}function c(e){i("flush");var t,a=e.writes,n=e.reads;try{i("flushing reads",n.length),e.runTasks(n),i("flushing writes",a.length),e.runTasks(a)}catch(e){t=e}if(e.scheduled=!1,(n.length||a.length)&&s(e),t){if(i("task errored",t.message),!e.catch)throw t;e.catch(t)}}function l(e,t){var a=e.indexOf(t);return!!~a&&!!e.splice(a,1)}o.prototype={constructor:o,runTasks:function(e){var t;for(i("run tasks");t=e.shift();)t()},measure:function(e,t){i("measure");var a=t?e.bind(t):e;return this.reads.push(a),s(this),a},mutate:function(e,t){i("mutate");var a=t?e.bind(t):e;return this.writes.push(a),s(this),a},clear:function(e){return i("clear",e),l(this.reads,e)||l(this.writes,e)},extend:function(e){if(i("extend",e),"object"!=typeof e)throw new Error("expected object");var t=Object.create(this);return function(e,t){for(var a in t)t.hasOwnProperty(a)&&(e[a]=t[a])}(t,e),t.fastdom=this,t.initialize&&t.initialize(),t},catch:null};var d=t.fastdom=t.fastdom||new o;void 0===(n=function(){return d}.call(d,a,d,e))||(e.exports=n)}("undefined"!=typeof window?window:void 0!==this?this:globalThis)},5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},a={};function n(e){var i=a[e];if(void 0!==i)return i.exports;var r=a[e]={exports:{}};return t[e].call(r.exports,r,r.exports,n),r.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};return function(){"use strict";n.r(i),n.d(i,{components:function(){return Li}});var e={};n.r(e),n.d(e,{STATIC_MEDIA_URL:function(){return xt},ph:function(){return Rt}});var t=n(448),a=n.n(t),r=n(5329),o=n.n(r);function s(e){var t,a,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(a=s(e[t]))&&(n&&(n+=" "),n+=a);else for(t in e)e[t]&&(n&&(n+=" "),n+=t);return n}var c=function(){for(var e,t,a=0,n="";a<arguments.length;)(e=arguments[a++])&&(t=s(e))&&(n&&(n+=" "),n+=t);return n};const l=(e,t=0,{leading:a=!1,trailing:n=!0}={})=>{let i=null;return function(...r){a&&null===i&&e.apply(this,r),i&&clearTimeout(i),i=n&&a&&!i?setTimeout((()=>{i=null}),t):setTimeout((()=>{n&&e.apply(this,r),i=null}),t)}},d=()=>"undefined"!=typeof window,u=e=>Object.entries(e).reduce(((e,[t,a])=>(t.includes("data-")&&(e[t]=a),e)),{});const m=13,h=27;function p(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}const g=p(32),f=p(m),E=e=>{f(e),g(e)},T=(p(h),["aria-id","aria-metadata","aria-type"]),I=(e,t)=>Object.entries(e).reduce(((e,[a,n])=>(t.includes(a)||(e[a]=n),e)),{}),_=e=>{const{role:t,tabIndex:a,tabindex:n,screenReader:i,lang:r,ariaAttributes:o={}}=e,s=Object.entries(o).reduce(((e,[t,a])=>({...e,[`aria-${t}`.toLowerCase()]:a})),{});return{role:t,tabIndex:a??n,screenReader:i,ariaAttributes:I(s,T),lang:r}},b="wixui-",v=(e,...t)=>{const a=[];return e&&a.push(`${b}${e}`),t.forEach((e=>{e&&(a.push(`${b}${e}`),a.push(e))})),a.join(" ")};var L={...{root:"vertical-menu",menuItem:"vertical-menu__item",subMenu:"vertical-menu__submenu",menuItemLabel:"vertical-menu__item-label"},arrow:"vertical-menu__arrow"},w={ExpandableMenuSkin:"ruTj7Z",expandableMenuSkin:"ruTj7Z",menuContainer:"NIXfHQ",ResponsiveExpandableMenuSkin:"cgGlGU",responsiveExpandableMenuSkin:"cgGlGU"};const M={root:"linkElement"},N=(e,t)=>{const{href:n,role:i,target:o,rel:s,className:c="",children:l,linkPopupId:d,anchorDataId:m,anchorCompId:h,tabIndex:p,dataTestId:T=M.root,title:I,onClick:_,onDoubleClick:b,onMouseEnter:v,onMouseLeave:L,onFocus:w,onFocusCapture:N,onBlurCapture:y,"aria-live":A,"aria-disabled":O,"aria-label":C,"aria-labelledby":k,"aria-pressed":S,"aria-expanded":R,"aria-describedby":P,"aria-haspopup":G,"aria-current":x,dataPreview:F,dataPart:B}=e,H=void 0!==e.activateByKey?e.activateByKey:(e=>e?"SpaceOrEnter":"Space")(d);let Y;switch(H){case"Enter":Y=f;break;case"Space":Y=g;break;case"SpaceOrEnter":Y=E;break;default:Y=void 0}return void 0!==n||d?r.createElement("a",a()({},u(e),{"data-testid":T,"data-popupid":d,"data-anchor":m,"data-anchor-comp-id":h,"data-preview":F,"data-part":B,href:n||void 0,target:o,role:d?"button":i,rel:s,className:c,onKeyDown:Y,"aria-live":A,"aria-disabled":O,"aria-label":C,"aria-labelledby":k,"aria-pressed":S,"aria-expanded":R,"aria-haspopup":G,"aria-describedby":P,"aria-current":x,title:I,onClick:_,onMouseEnter:v,onMouseLeave:L,onDoubleClick:b,onFocus:w,onFocusCapture:N,onBlurCapture:y,ref:t,tabIndex:d?0:p}),l):r.createElement("div",a()({},u(e),{"data-testid":T,"data-preview":F,"data-part":B,className:c,tabIndex:p,"aria-label":C,"aria-labelledby":k,"aria-haspopup":G,"aria-disabled":O,"aria-expanded":R,title:I,role:i,onClick:_,onDoubleClick:b,onMouseEnter:v,onMouseLeave:L,ref:t}),l)};var y=r.forwardRef(N);const A="itemWrapper",O="linkWrapper",C="expandablemenu-toggle";var k={ExpandableMenuSkin:"UNhuLu",expandableMenuSkin:"UNhuLu",itemWrapper:"fEGEM_",labelWrapper:"kGvnrc",label:"xfxJ27",arrowWrapper:"RJADXR",arrow:"DpIELp",hasSubList:"fqtSRp",subMenuOpen:"hGjOas",subMenu:"saNEb7",selected:"QqwXfj",applyTextSelectedColorExperiment:"dWouOY",item:"YLBS9j",itemCheckbox:"ZD5b14","item-depth0":"OZVMSN",itemDepth0:"OZVMSN","item-depth1":"zui1C4",itemDepth1:"zui1C4","item-depth2":"WJmop7",itemDepth2:"WJmop7",ResponsiveExpandableMenuSkin:"u4cNtA",responsiveExpandableMenuSkin:"u4cNtA"};const S=e=>k["item-depth"+e],R=e=>{let{label:t,link:n,onClick:i,onDoubleClick:o}=e;return r.createElement("span",{"data-testid":O,className:k.labelWrapper},r.createElement(y,a()({},n,{className:c(k.label,v(L.menuItemLabel)),onClick:i,onDoubleClick:o}),t))},P=e=>e?{"aria-current":"page"}:{},G=e=>e.items.length?(e=>{let{id:t,label:n,link:i,items:o=[],skin:s,isSelected:l,isCurrent:d,idPrefix:u,onItemClick:m,onItemDblClick:h,onItemMouseIn:p,onItemMouseOut:g,isForceOpened:f=!1,applyTextSelectedColorExperiment:E,depth:T}=e;const I=o.findIndex((e=>e.isSelected)),_=-1!==I,[b,w]=r.useState(_);r.useEffect((()=>{w(-1!==I||f)}),[I,f]);const M=p?e=>{null==p||p(e,{id:t,label:n,link:i,items:o,selected:l})}:void 0,N=g?e=>{null==g||g(e,{id:t,label:n,link:i,items:o,selected:l})}:void 0,y=m?e=>{null==m||m(e,{id:t,label:n,link:i,items:o,selected:l})}:void 0,O=h?e=>{null==h||h(e,{id:t,label:n,link:i,items:o,selected:l})}:void 0,x=e=>{null==y||y(e),w(!b)},F={"aria-expanded":b,"aria-haspopup":"true"};return r.createElement("li",a()({"data-testid":u},P(d),{className:c(k[s],k.item,S(T),k.hasSubList,v(L.menuItem),{[k.selected]:l,[k.subMenuOpen]:b}),onMouseEnter:M,onMouseLeave:N}),r.createElement("div",{"data-testid":A,className:k.itemWrapper},r.createElement(R,{label:n,link:i,onClick:x,onDoubleClick:O}),r.createElement("button",a()({},F,{"aria-label":n,className:k.arrowWrapper,"data-testid":C,onClick:x,onDoubleClick:O}),r.createElement("div",{className:c(k.arrow,v(L.arrow))},r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 9.2828 4.89817"},r.createElement("path",{d:"M4.64116,4.89817a.5001.5001,0,0,1-.34277-.13574L.15727.86448A.50018.50018,0,0,1,.84282.136L4.64116,3.71165,8.44.136a.50018.50018,0,0,1,.68555.72852L4.98393,4.76243A.5001.5001,0,0,1,4.64116,4.89817Z"}))))),r.createElement("ul",{className:c(k.subMenu,v(L.subMenu))},o.map(((e,t)=>r.createElement(G,a()({key:t},e,{onItemClick:m,onItemDblClick:h,onItemMouseIn:p,onItemMouseOut:g,idPrefix:u+"-"+t,skin:s,applyTextSelectedColorExperiment:E,depth:T+1}))))))})(e):(e=>{let{id:t,label:n,link:i,skin:o,isSelected:s,isCurrent:l,idPrefix:d,onItemClick:u,onItemDblClick:m,onItemMouseIn:h,onItemMouseOut:p,applyTextSelectedColorExperiment:g,depth:f}=e;const E=h?e=>{null==h||h(e,{id:t,label:n,link:i,items:[],selected:s})}:void 0,T=p?e=>{null==p||p(e,{id:t,label:n,link:i,items:[],selected:s})}:void 0,I=u?e=>{null==u||u(e,{id:t,label:n,link:i,items:[],selected:s})}:void 0,_=m?e=>{null==m||m(e,{id:t,label:n,link:i,items:[],selected:s})}:void 0;return r.createElement("li",a()({"data-testid":d},P(l),{className:c(k[o],k.item,S(f),v(L.menuItem),{[k.selected]:s}),onMouseEnter:E,onMouseLeave:T}),r.createElement("div",{"data-testid":A,className:c(k.itemWrapper,{[k.applyTextSelectedColorExperiment]:g})},r.createElement(R,{onClick:I,onDoubleClick:_,label:n,link:i})))})(e);var x=G;const F=e=>{return!(t=e)||0===Object.keys(t).length&&t.constructor===Object;var t},B=function(e,t){return void 0===t&&(t=""),e.map((e=>{const a=!1!==e.selected&&(e.selected||((e,t)=>{return!(!e.link||(a=e.link,a.anchorDataId||a.anchorCompId)||e.link.href!==t);var a})(e,t)),n=e.items||[],i=B(n,t),r=i.some((e=>e.isSelected)),o=!1!==e.selected&&(a||r&&!F(e.link));return{...e,isSelected:o,items:i,isCurrent:a}}))};var H=e=>{var t;const{id:n,className:i,customClassNames:o=[],items:s,currentUrl:l,translations:d,onItemClick:m,onItemDblClick:h,onItemMouseIn:p,onItemMouseOut:g,onMouseEnter:f,onMouseLeave:E,reportBiOnMenuItemClick:T,skin:I="ExpandableMenuSkin",applyTextSelectedColorExperiment:b,a11y:M,ariaAttributes:N,lang:y}=e,A=r.useMemo((()=>B(s,l)),[s,l]),O=function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];null==T||T(...t),null==m||m(...t)},C=_({ariaAttributes:{label:null==M?void 0:M.label,...N}});return r.createElement("nav",a()({id:n},u(e),{"aria-label":null!=(t=C.ariaAttributes["aria-label"])?t:d.ariaLabel,onMouseEnter:f,onMouseLeave:E,className:c(w[I],i,v(L.root,...o)),lang:y}),r.createElement("ul",{className:w.menuContainer},A.map(((e,t)=>r.createElement(x,a()({key:t},e,{onItemClick:O,onItemDblClick:h,onItemMouseIn:p,onItemMouseOut:g,idPrefix:n+"-"+t,skin:I,applyTextSelectedColorExperiment:b,depth:0}))))))};const Y=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)),U=e=>({useComponentProps:(t,a,n)=>{const i=(e=>({...e,updateStyles:t=>{const a=Object.entries(t).reduce(((e,[t,a])=>{return{...e,[(n=t,n.startsWith("--")?t:Y(t))]:void 0===a?null:a};var n}),{});e.updateStyles(a)}}))(n);return e({mapperProps:t,stateValues:a,controllerUtils:i})}}),D=e=>"linkPopupId"in e,V=(e,t)=>{if(D(e))return e.linkPopupId;{const{pagesMap:a,mainPageId:n}=t||{};if(!a)return;const i=new URL(e.href??"");let r=Object.values(a).find((({pageUriSEO:e})=>!!e&&i.pathname?.includes(e)));return r||(r=n?a[n]:void 0),r?.pageId}},j=e=>{if(void 0!==e)return null===e?"None":e.type},$=(e,t)=>{if(!e?.type)return;const{type:a}=e;switch(a){case"AnchorLink":return e.anchorDataId;case"DocumentLink":return e.docInfo?.name;case"PageLink":const a=V(e,t);return a&&t?.pagesMap?.[a]?.title;default:return e.href}},z=(e,t,a)=>{const{link:n,value:i,details:r,actionName:o,elementType:s,trackClicksAnalytics:c,pagesMetadata:l,...d}=t;if(!c)return;const u=l&&{...l,pagesMap:window.viewerModel?.siteFeaturesConfigs?.router?.pagesMap},m=((e,t)=>{if(!e?.type)return;const{type:a}=e;switch(a){case"AnchorLink":return(e=>"anchorDataId"in e&&("SCROLL_TO_TOP"===e.anchorDataId||"SCROLL_TO_BOTTOM"===e.anchorDataId))(e)?void 0:{id:e.anchorDataId};case"DocumentLink":return{id:e.docInfo?.docId};case"PageLink":return{id:V(e,t),isLightbox:D(e)};default:return}})(n,u),h=r||m?JSON.stringify({...m,...r}):void 0;e({src:76,evid:1113,...{...d,bl:navigator.language,url:window.location.href,details:h,elementType:s??"Unknown",actionName:o??j(n),value:i??$(n,u)}},{endpoint:"pa",...a})};var W;!function(e){e.Text="Text",e.Menu="Menu",e.Image="Image",e.Input="Input",e.Login="Login",e.Button="Button",e.Social="Social",e.Gallery="Gallery",e.Community="Community",e.Decorative="Decorative",e.MenuAndSearch="MenuAndSearch",e.MenuAndAnchor="MenuAndAnchor"}(W||(W={}));var Z=U((e=>{let{stateValues:t,mapperProps:a}=e;const{currentUrl:n,reportBi:i}=t,{compId:r,language:o,mainPageId:s,fullNameCompType:c,trackClicksAnalytics:l,...d}=a;return{...d,currentUrl:n,reportBiOnMenuItemClick:(e,t)=>{const{label:a,link:n}=null!=t?t:{};z(i,{link:n,language:o,trackClicksAnalytics:l,elementTitle:a,elementType:c,pagesMetadata:{mainPageId:s},elementGroup:W.Menu,element_id:null!=r?r:null==e?void 0:e.currentTarget.id})}}}));var q={root:"mobile-menu"},J={menuContainer:"EmyVop",visible:"two32l",inlineContent:"vMwwq3",container:"vnTKrr",overlay:"YppmB_",horizontallyDocked:"RmiF1m",verticallyDocked:"dqZerU",inlineContentParent:"Zcgm3P"},X="jhxvbR";const K="v1",Q=2,ee=1920,te=1920,ae=1e3,ne=1e3,ie={SCALE_TO_FILL:"fill",SCALE_TO_FIT:"fit",STRETCH:"stretch",ORIGINAL_SIZE:"original_size",TILE:"tile",TILE_HORIZONTAL:"tile_horizontal",TILE_VERTICAL:"tile_vertical",FIT_AND_TILE:"fit_and_tile",LEGACY_STRIP_TILE:"legacy_strip_tile",LEGACY_STRIP_TILE_HORIZONTAL:"legacy_strip_tile_horizontal",LEGACY_STRIP_TILE_VERTICAL:"legacy_strip_tile_vertical",LEGACY_STRIP_SCALE_TO_FILL:"legacy_strip_fill",LEGACY_STRIP_SCALE_TO_FIT:"legacy_strip_fit",LEGACY_STRIP_FIT_AND_TILE:"legacy_strip_fit_and_tile",LEGACY_STRIP_ORIGINAL_SIZE:"legacy_strip_original_size",LEGACY_ORIGINAL_SIZE:"actual_size",LEGACY_FIT_WIDTH:"fitWidth",LEGACY_FIT_HEIGHT:"fitHeight",LEGACY_FULL:"full",LEGACY_BG_FIT_AND_TILE:"legacy_tile",LEGACY_BG_FIT_AND_TILE_HORIZONTAL:"legacy_tile_horizontal",LEGACY_BG_FIT_AND_TILE_VERTICAL:"legacy_tile_vertical",LEGACY_BG_NORMAL:"legacy_normal"},re={FIT:"fit",FILL:"fill",FILL_FOCAL:"fill_focal",CROP:"crop",LEGACY_CROP:"legacy_crop",LEGACY_FILL:"legacy_fill"},oe={CENTER:"center",TOP:"top",TOP_LEFT:"top_left",TOP_RIGHT:"top_right",BOTTOM:"bottom",BOTTOM_LEFT:"bottom_left",BOTTOM_RIGHT:"bottom_right",LEFT:"left",RIGHT:"right"},se={[oe.CENTER]:{x:.5,y:.5},[oe.TOP_LEFT]:{x:0,y:0},[oe.TOP_RIGHT]:{x:1,y:0},[oe.TOP]:{x:.5,y:0},[oe.BOTTOM_LEFT]:{x:0,y:1},[oe.BOTTOM_RIGHT]:{x:1,y:1},[oe.BOTTOM]:{x:.5,y:1},[oe.RIGHT]:{x:1,y:.5},[oe.LEFT]:{x:0,y:.5}},ce={center:"c",top:"t",top_left:"tl",top_right:"tr",bottom:"b",bottom_left:"bl",bottom_right:"br",left:"l",right:"r"},le={BG:"bg",IMG:"img",SVG:"svg"},de={AUTO:"auto",CLASSIC:"classic",SUPER:"super"},ue={classic:1,super:2},me={radius:"0.66",amount:"1.00",threshold:"0.01"},he={uri:"",css:{img:{},container:{}},attr:{img:{},container:{}},transformed:!1},pe=25e6,ge=[1.5,2,4],fe={HIGH:{size:196e4,quality:90,maxUpscale:1},MEDIUM:{size:36e4,quality:85,maxUpscale:1},LOW:{size:16e4,quality:80,maxUpscale:1.2},TINY:{size:0,quality:80,maxUpscale:1.4}},Ee={HIGH:"HIGH",MEDIUM:"MEDIUM",LOW:"LOW",TINY:"TINY"},Te={CONTRAST:"contrast",BRIGHTNESS:"brightness",SATURATION:"saturation",HUE:"hue",BLUR:"blur"},Ie={JPG:"jpg",JPEG:"jpeg",JPE:"jpe",PNG:"png",WEBP:"webp",WIX_ICO_MP:"wix_ico_mp",WIX_MP:"wix_mp",GIF:"gif",SVG:"svg",AVIF:"avif",UNRECOGNIZED:"unrecognized"},_e={AVIF:"AVIF",PAVIF:"PAVIF"};Ie.JPG,Ie.JPEG,Ie.JPE,Ie.PNG,Ie.GIF,Ie.WEBP;function be(e,...t){return function(...a){const n=a[a.length-1]||{},i=[e[0]];return t.forEach((function(t,r){const o=Number.isInteger(t)?a[t]:n[t];i.push(o,e[r+1])})),i.join("")}}function ve(e){return e[e.length-1]}const Le=[Ie.PNG,Ie.JPEG,Ie.JPG,Ie.JPE,Ie.WIX_ICO_MP,Ie.WIX_MP,Ie.WEBP,Ie.AVIF],we=[Ie.JPEG,Ie.JPG,Ie.JPE];function Me(e,t,a){return a&&t&&!(!(n=t.id)||!n.trim()||"none"===n.toLowerCase())&&Object.values(ie).includes(e);var n}function Ne(e,t,a){return function(e,t,a=!1){return!((Ae(e)||Ce(e))&&t&&!a)}(e,t,a)&&(function(e){return Le.includes(Ge(e))}(e)||function(e,t=!1){return Oe(e)&&t}(e,a))&&!/(^https?)|(^data)|(^\/\/)/.test(e)}function ye(e){return Ge(e)===Ie.PNG}function Ae(e){return Ge(e)===Ie.WEBP}function Oe(e){return Ge(e)===Ie.GIF}function Ce(e){return Ge(e)===Ie.AVIF}const ke=["/","\\","?","<",">","|","\u201c",":",'"'].map(encodeURIComponent),Se=["\\.","\\*"],Re="_";function Pe(e){return function(e){return we.includes(Ge(e))}(e)?Ie.JPG:ye(e)?Ie.PNG:Ae(e)?Ie.WEBP:Oe(e)?Ie.GIF:Ce(e)?Ie.AVIF:Ie.UNRECOGNIZED}function Ge(e){return(/[.]([^.]+)$/.exec(e)&&/[.]([^.]+)$/.exec(e)[1]||"").toLowerCase()}function xe(e,t,a,n,i){let r;return r=i===re.FILL?function(e,t,a,n){return Math.max(a/e,n/t)}(e,t,a,n):i===re.FIT?function(e,t,a,n){return Math.min(a/e,n/t)}(e,t,a,n):1,r}function Fe(e,t,a,n,i,r){e=e||n.width,t=t||n.height;const{scaleFactor:o,width:s,height:c}=function(e,t,a,n,i){let r,o=a,s=n;if(r=xe(e,t,a,n,i),i===re.FIT&&(o=e*r,s=t*r),o&&s&&o*s>pe){const a=Math.sqrt(pe/(o*s));o*=a,s*=a,r=xe(e,t,o,s,i)}return{scaleFactor:r,width:o,height:s}}(e,t,n.width*i,n.height*i,a);return function(e,t,a,n,i,r,o){const{optimizedScaleFactor:s,upscaleMethodValue:c,forceUSM:l}=function(e,t,a,n){if("auto"===n)return function(e,t){const a=Ue(e,t);return{optimizedScaleFactor:fe[a].maxUpscale,upscaleMethodValue:ue.classic,forceUSM:!1}}(e,t);if("super"===n)return function(e){return{optimizedScaleFactor:ve(ge),upscaleMethodValue:ue.super,forceUSM:!(ge.includes(e)||e>ve(ge))}}(a);return function(e,t){const a=Ue(e,t);return{optimizedScaleFactor:fe[a].maxUpscale,upscaleMethodValue:ue.classic,forceUSM:!1}}(e,t)}(e,t,r,i);let d=a,u=n;if(r<=s)return{width:d,height:u,scaleFactor:r,upscaleMethodValue:c,forceUSM:l,cssUpscaleNeeded:!1};switch(o){case re.FILL:d=a*(s/r),u=n*(s/r);break;case re.FIT:d=e*s,u=t*s}return{width:d,height:u,scaleFactor:s,upscaleMethodValue:c,forceUSM:l,cssUpscaleNeeded:!0}}(e,t,s,c,r,o,a)}function Be(e,t,a,n){const i=Ye(a)||function(e=oe.CENTER){return se[e]}(n);return{x:Math.max(0,Math.min(e.width-t.width,i.x*e.width-t.width/2)),y:Math.max(0,Math.min(e.height-t.height,i.y*e.height-t.height/2)),width:Math.min(e.width,t.width),height:Math.min(e.height,t.height)}}function He(e){return e.alignment&&ce[e.alignment]||ce[oe.CENTER]}function Ye(e){let t;return!e||"number"!=typeof e.x||isNaN(e.x)||"number"!=typeof e.y||isNaN(e.y)||(t={x:De(Math.max(0,Math.min(100,e.x))/100,2),y:De(Math.max(0,Math.min(100,e.y))/100,2)}),t}function Ue(e,t){const a=e*t;return a>fe[Ee.HIGH].size?Ee.HIGH:a>fe[Ee.MEDIUM].size?Ee.MEDIUM:a>fe[Ee.LOW].size?Ee.LOW:Ee.TINY}function De(e,t){const a=Math.pow(10,t||0);return(e*a/a).toFixed(t)}function Ve(e){return e&&e.upscaleMethod&&de[e.upscaleMethod.toUpperCase()]||de.AUTO}function je(e,t){const a=Ae(e)||Ce(e);return Ge(e)===Ie.GIF||a&&t}const $e={isMobile:!1},ze=function(e){return $e[e]};function We(){if("undefined"!=typeof window&&"undefined"!=typeof navigator){const t=window.matchMedia&&window.matchMedia("(max-width: 767px)").matches,a=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);e=t&&a,$e["isMobile"]=e}var e}function Ze(e,t){const a={css:{container:{}}},{css:n}=a,{fittingType:i}=e;switch(i){case ie.ORIGINAL_SIZE:case ie.LEGACY_ORIGINAL_SIZE:case ie.LEGACY_STRIP_ORIGINAL_SIZE:n.container.backgroundSize="auto",n.container.backgroundRepeat="no-repeat";break;case ie.SCALE_TO_FIT:case ie.LEGACY_STRIP_SCALE_TO_FIT:n.container.backgroundSize="contain",n.container.backgroundRepeat="no-repeat";break;case ie.STRETCH:n.container.backgroundSize="100% 100%",n.container.backgroundRepeat="no-repeat";break;case ie.SCALE_TO_FILL:case ie.LEGACY_STRIP_SCALE_TO_FILL:n.container.backgroundSize="cover",n.container.backgroundRepeat="no-repeat";break;case ie.TILE_HORIZONTAL:case ie.LEGACY_STRIP_TILE_HORIZONTAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat-x";break;case ie.TILE_VERTICAL:case ie.LEGACY_STRIP_TILE_VERTICAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat-y";break;case ie.TILE:case ie.LEGACY_STRIP_TILE:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat";break;case ie.LEGACY_STRIP_FIT_AND_TILE:n.container.backgroundSize="contain",n.container.backgroundRepeat="repeat";break;case ie.FIT_AND_TILE:case ie.LEGACY_BG_FIT_AND_TILE:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat";break;case ie.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat-x";break;case ie.LEGACY_BG_FIT_AND_TILE_VERTICAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat-y";break;case ie.LEGACY_BG_NORMAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="no-repeat"}switch(t.alignment){case oe.CENTER:n.container.backgroundPosition="center center";break;case oe.LEFT:n.container.backgroundPosition="left center";break;case oe.RIGHT:n.container.backgroundPosition="right center";break;case oe.TOP:n.container.backgroundPosition="center top";break;case oe.BOTTOM:n.container.backgroundPosition="center bottom";break;case oe.TOP_RIGHT:n.container.backgroundPosition="right top";break;case oe.TOP_LEFT:n.container.backgroundPosition="left top";break;case oe.BOTTOM_RIGHT:n.container.backgroundPosition="right bottom";break;case oe.BOTTOM_LEFT:n.container.backgroundPosition="left bottom"}return a}const qe={[oe.CENTER]:"center",[oe.TOP]:"top",[oe.TOP_LEFT]:"top left",[oe.TOP_RIGHT]:"top right",[oe.BOTTOM]:"bottom",[oe.BOTTOM_LEFT]:"bottom left",[oe.BOTTOM_RIGHT]:"bottom right",[oe.LEFT]:"left",[oe.RIGHT]:"right"},Je={position:"absolute",top:"auto",right:"auto",bottom:"auto",left:"auto"};function Xe(e,t){const a={css:{container:{},img:{}}},{css:n}=a,{fittingType:i}=e,r=t.alignment;switch(n.container.position="relative",i){case ie.ORIGINAL_SIZE:case ie.LEGACY_ORIGINAL_SIZE:e.parts&&e.parts.length?(n.img.width=e.parts[0].width,n.img.height=e.parts[0].height):(n.img.width=e.src.width,n.img.height=e.src.height);break;case ie.SCALE_TO_FIT:case ie.LEGACY_FIT_WIDTH:case ie.LEGACY_FIT_HEIGHT:case ie.LEGACY_FULL:n.img.width=t.width,n.img.height=t.height,n.img.objectFit="contain",n.img.objectPosition=qe[r]||"unset";break;case ie.LEGACY_BG_NORMAL:n.img.width="100%",n.img.height="100%",n.img.objectFit="none",n.img.objectPosition=qe[r]||"unset";break;case ie.STRETCH:n.img.width=t.width,n.img.height=t.height,n.img.objectFit="fill";break;case ie.SCALE_TO_FILL:n.img.width=t.width,n.img.height=t.height,n.img.objectFit="cover"}if("number"==typeof n.img.width&&"number"==typeof n.img.height&&(n.img.width!==t.width||n.img.height!==t.height)){const e=Math.round((t.height-n.img.height)/2),a=Math.round((t.width-n.img.width)/2);Object.assign(n.img,Je,function(e,t,a){return{[oe.TOP_LEFT]:{top:0,left:0},[oe.TOP_RIGHT]:{top:0,right:0},[oe.TOP]:{top:0,left:t},[oe.BOTTOM_LEFT]:{bottom:0,left:0},[oe.BOTTOM_RIGHT]:{bottom:0,right:0},[oe.BOTTOM]:{bottom:0,left:t},[oe.RIGHT]:{top:e,right:0},[oe.LEFT]:{top:e,left:0},[oe.CENTER]:{width:a.width,height:a.height,objectFit:"none"}}}(e,a,t)[r])}return a}function Ke(e,t){const a={css:{container:{}},attr:{container:{},img:{}}},{css:n,attr:i}=a,{fittingType:r}=e,o=t.alignment,{width:s,height:c}=e.src;let l;switch(n.container.position="relative",r){case ie.ORIGINAL_SIZE:case ie.LEGACY_ORIGINAL_SIZE:case ie.TILE:e.parts&&e.parts.length?(i.img.width=e.parts[0].width,i.img.height=e.parts[0].height):(i.img.width=s,i.img.height=c),i.img.preserveAspectRatio="xMidYMid slice";break;case ie.SCALE_TO_FIT:case ie.LEGACY_FIT_WIDTH:case ie.LEGACY_FIT_HEIGHT:case ie.LEGACY_FULL:i.img.width="100%",i.img.height="100%",i.img.transform="",i.img.preserveAspectRatio="";break;case ie.STRETCH:i.img.width=t.width,i.img.height=t.height,i.img.x=0,i.img.y=0,i.img.transform="",i.img.preserveAspectRatio="none";break;case ie.SCALE_TO_FILL:Ne(e.src.id)?(i.img.width=t.width,i.img.height=t.height):(l=function(e,t,a,n,i){const r=xe(e,t,a,n,i);return{width:Math.round(e*r),height:Math.round(t*r)}}(s,c,t.width,t.height,re.FILL),i.img.width=l.width,i.img.height=l.height),i.img.x=0,i.img.y=0,i.img.transform="",i.img.preserveAspectRatio="xMidYMid slice"}if("number"==typeof i.img.width&&"number"==typeof i.img.height&&(i.img.width!==t.width||i.img.height!==t.height)){let e,a,n=0,s=0;r===ie.TILE?(e=t.width%i.img.width,a=t.height%i.img.height):(e=t.width-i.img.width,a=t.height-i.img.height);const c=Math.round(e/2),l=Math.round(a/2);switch(o){case oe.TOP_LEFT:n=0,s=0;break;case oe.TOP:n=c,s=0;break;case oe.TOP_RIGHT:n=e,s=0;break;case oe.LEFT:n=0,s=l;break;case oe.CENTER:n=c,s=l;break;case oe.RIGHT:n=e,s=l;break;case oe.BOTTOM_LEFT:n=0,s=a;break;case oe.BOTTOM:n=c,s=a;break;case oe.BOTTOM_RIGHT:n=e,s=a}i.img.x=n,i.img.y=s}return i.container.width=t.width,i.container.height=t.height,i.container.viewBox=[0,0,t.width,t.height].join(" "),a}function Qe(e,t,a){let n;switch(t.crop&&(n=function(e,t){const a=Math.max(0,Math.min(e.width,t.x+t.width)-Math.max(0,t.x)),n=Math.max(0,Math.min(e.height,t.y+t.height)-Math.max(0,t.y));return a&&n&&(e.width!==a||e.height!==n)?{x:Math.max(0,t.x),y:Math.max(0,t.y),width:a,height:n}:null}(t,t.crop),n&&(e.src.width=n.width,e.src.height=n.height,e.src.isCropped=!0,e.parts.push(tt(n)))),e.fittingType){case ie.SCALE_TO_FIT:case ie.LEGACY_FIT_WIDTH:case ie.LEGACY_FIT_HEIGHT:case ie.LEGACY_FULL:case ie.FIT_AND_TILE:case ie.LEGACY_BG_FIT_AND_TILE:case ie.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case ie.LEGACY_BG_FIT_AND_TILE_VERTICAL:case ie.LEGACY_BG_NORMAL:e.parts.push(et(e,a));break;case ie.SCALE_TO_FILL:e.parts.push(function(e,t){const a=Fe(e.src.width,e.src.height,re.FILL,t,e.devicePixelRatio,e.upscaleMethod),n=Ye(e.focalPoint);return{transformType:n?re.FILL_FOCAL:re.FILL,width:Math.round(a.width),height:Math.round(a.height),alignment:He(t),focalPointX:n&&n.x,focalPointY:n&&n.y,upscale:a.scaleFactor>1,forceUSM:a.forceUSM,scaleFactor:a.scaleFactor,cssUpscaleNeeded:a.cssUpscaleNeeded,upscaleMethodValue:a.upscaleMethodValue}}(e,a));break;case ie.STRETCH:e.parts.push(function(e,t){const a=xe(e.src.width,e.src.height,t.width,t.height,re.FILL),n={...t};return n.width=e.src.width*a,n.height=e.src.height*a,et(e,n)}(e,a));break;case ie.TILE_HORIZONTAL:case ie.TILE_VERTICAL:case ie.TILE:case ie.LEGACY_ORIGINAL_SIZE:case ie.ORIGINAL_SIZE:n=Be(e.src,a,e.focalPoint,a.alignment),e.src.isCropped?(Object.assign(e.parts[0],n),e.src.width=n.width,e.src.height=n.height):e.parts.push(tt(n));break;case ie.LEGACY_STRIP_TILE_HORIZONTAL:case ie.LEGACY_STRIP_TILE_VERTICAL:case ie.LEGACY_STRIP_TILE:case ie.LEGACY_STRIP_ORIGINAL_SIZE:e.parts.push(function(e){return{transformType:re.LEGACY_CROP,width:Math.round(e.width),height:Math.round(e.height),alignment:He(e),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}(a));break;case ie.LEGACY_STRIP_SCALE_TO_FIT:case ie.LEGACY_STRIP_FIT_AND_TILE:e.parts.push(function(e){return{transformType:re.FIT,width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(a));break;case ie.LEGACY_STRIP_SCALE_TO_FILL:e.parts.push(function(e){return{transformType:re.LEGACY_FILL,width:Math.round(e.width),height:Math.round(e.height),alignment:He(e),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(a))}}function et(e,t){const a=Fe(e.src.width,e.src.height,re.FIT,t,e.devicePixelRatio,e.upscaleMethod);return{transformType:!e.src.width||!e.src.height?re.FIT:re.FILL,width:Math.round(a.width),height:Math.round(a.height),alignment:ce.center,upscale:a.scaleFactor>1,forceUSM:a.forceUSM,scaleFactor:a.scaleFactor,cssUpscaleNeeded:a.cssUpscaleNeeded,upscaleMethodValue:a.upscaleMethodValue}}function tt(e){return{transformType:re.CROP,x:Math.round(e.x),y:Math.round(e.y),width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}function at(e,t){t=t||{},e.quality=function(e,t){const a=e.fileType===Ie.PNG,n=e.fileType===Ie.JPG,i=e.fileType===Ie.WEBP,r=e.fileType===Ie.AVIF,o=n||a||i||r;if(o){const n=ve(e.parts),i=(s=n.width,c=n.height,fe[Ue(s,c)].quality);let r=t.quality&&t.quality>=5&&t.quality<=90?t.quality:i;return r=a?r+5:r,r}var s,c;return 0}(e,t),e.progressive=function(e){return!1!==e.progressive}(t),e.watermark=function(e){return e.watermark}(t),e.autoEncode=t.autoEncode??!0,e.encoding=t?.encoding,e.unsharpMask=function(e,t){if(function(e){const t="number"==typeof(e=e||{}).radius&&!isNaN(e.radius)&&e.radius>=.1&&e.radius<=500,a="number"==typeof e.amount&&!isNaN(e.amount)&&e.amount>=0&&e.amount<=10,n="number"==typeof e.threshold&&!isNaN(e.threshold)&&e.threshold>=0&&e.threshold<=255;return t&&a&&n}(t.unsharpMask))return{radius:De(t.unsharpMask?.radius,2),amount:De(t.unsharpMask?.amount,2),threshold:De(t.unsharpMask?.threshold,2)};if(("number"!=typeof(a=(a=t.unsharpMask)||{}).radius||isNaN(a.radius)||0!==a.radius||"number"!=typeof a.amount||isNaN(a.amount)||0!==a.amount||"number"!=typeof a.threshold||isNaN(a.threshold)||0!==a.threshold)&&function(e){const t=ve(e.parts);return!(t.scaleFactor>=1)||t.forceUSM||t.transformType===re.FIT}(e))return me;var a;return}(e,t),e.filters=function(e){const t=e.filters||{},a={};nt(t[Te.CONTRAST],-100,100)&&(a[Te.CONTRAST]=t[Te.CONTRAST]);nt(t[Te.BRIGHTNESS],-100,100)&&(a[Te.BRIGHTNESS]=t[Te.BRIGHTNESS]);nt(t[Te.SATURATION],-100,100)&&(a[Te.SATURATION]=t[Te.SATURATION]);nt(t[Te.HUE],-180,180)&&(a[Te.HUE]=t[Te.HUE]);nt(t[Te.BLUR],0,100)&&(a[Te.BLUR]=t[Te.BLUR]);return a}(t)}function nt(e,t,a){return"number"==typeof e&&!isNaN(e)&&0!==e&&e>=t&&e<=a}function it(e,t,a,n){const i=function(e){return e?.isSEOBot??!1}(n),r=Pe(t.id),o=function(e,t){const a=/\.([^.]*)$/,n=new RegExp(`(${ke.concat(Se).join("|")})`,"g");if(t&&t.length){let e=t;const i=t.match(a);return i&&Le.includes(i[1])&&(e=t.replace(a,"")),encodeURIComponent(e).replace(n,Re)}const i=e.match(/\/(.*?)$/);return(i?i[1]:e).replace(a,"")}(t.id,t.name),s=i?1:function(e){return Math.min(e.pixelAspectRatio||1,Q)}(a),c=Ge(t.id),l=c,d=Ne(t.id,n?.hasAnimation,n?.allowAnimatedTransform),u={fileName:o,fileExtension:c,fileType:r,fittingType:e,preferredExtension:l,src:{id:t.id,width:t.width,height:t.height,isCropped:!1,isAnimated:je(t.id,n?.hasAnimation)},focalPoint:{x:t.focalPoint&&t.focalPoint.x,y:t.focalPoint&&t.focalPoint.y},parts:[],devicePixelRatio:s,quality:0,upscaleMethod:Ve(n),progressive:!0,watermark:"",unsharpMask:{},filters:{},transformed:d};return d&&(Qe(u,t,a),at(u,n)),u}function rt(e,t,a){const n={...a},i=ze("isMobile");switch(e){case ie.LEGACY_BG_FIT_AND_TILE:case ie.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case ie.LEGACY_BG_FIT_AND_TILE_VERTICAL:case ie.LEGACY_BG_NORMAL:const e=i?ae:ee,a=i?ne:te;n.width=Math.min(e,t.width),n.height=Math.min(a,Math.round(n.width/(t.width/t.height))),n.pixelAspectRatio=1}return n}const ot=be`fit/w_${"width"},h_${"height"}`,st=be`fill/w_${"width"},h_${"height"},al_${"alignment"}`,ct=be`fill/w_${"width"},h_${"height"},fp_${"focalPointX"}_${"focalPointY"}`,lt=be`crop/x_${"x"},y_${"y"},w_${"width"},h_${"height"}`,dt=be`crop/w_${"width"},h_${"height"},al_${"alignment"}`,ut=be`fill/w_${"width"},h_${"height"},al_${"alignment"}`,mt=be`,lg_${"upscaleMethodValue"}`,ht=be`,q_${"quality"}`,pt=be`,quality_auto`,gt=be`,usm_${"radius"}_${"amount"}_${"threshold"}`,ft=be`,bl`,Et=be`,wm_${"watermark"}`,Tt={[Te.CONTRAST]:be`,con_${"contrast"}`,[Te.BRIGHTNESS]:be`,br_${"brightness"}`,[Te.SATURATION]:be`,sat_${"saturation"}`,[Te.HUE]:be`,hue_${"hue"}`,[Te.BLUR]:be`,blur_${"blur"}`},It=be`,enc_auto`,_t=be`,enc_avif`,bt=be`,enc_pavif`,vt=be`,pstr`;function Lt(e,t,a,n={},i){if(Ne(t.id,n?.hasAnimation,n?.allowAnimatedTransform)){if(Ae(t.id)||Ce(t.id)){const{alignment:r,...o}=a;t.focalPoint={x:void 0,y:void 0},delete t?.crop,i=it(e,t,o,n)}else i=i||it(e,t,a,n);return function(e){const t=[];e.parts.forEach((e=>{switch(e.transformType){case re.CROP:t.push(lt(e));break;case re.LEGACY_CROP:t.push(dt(e));break;case re.LEGACY_FILL:let a=ut(e);e.upscale&&(a+=mt(e)),t.push(a);break;case re.FIT:let n=ot(e);e.upscale&&(n+=mt(e)),t.push(n);break;case re.FILL:let i=st(e);e.upscale&&(i+=mt(e)),t.push(i);break;case re.FILL_FOCAL:let r=ct(e);e.upscale&&(r+=mt(e)),t.push(r)}}));let a=t.join("/");return e.quality&&(a+=ht(e)),e.unsharpMask&&(a+=gt(e.unsharpMask)),e.progressive||(a+=ft(e)),e.watermark&&(a+=Et(e)),e.filters&&(a+=Object.keys(e.filters).map((t=>Tt[t](e.filters))).join("")),e.fileType!==Ie.GIF&&(e.encoding===_e.AVIF?(a+=_t(e),a+=pt(e)):e.encoding===_e.PAVIF?(a+=bt(e),a+=pt(e)):e.autoEncode&&(a+=It(e))),e.src?.isAnimated&&e.transformed&&(a+=vt(e)),`${e.src.id}/${K}/${a}/${e.fileName}.${e.preferredExtension}`}(i)}return t.id}const wt={[oe.CENTER]:"50% 50%",[oe.TOP_LEFT]:"0% 0%",[oe.TOP_RIGHT]:"100% 0%",[oe.TOP]:"50% 0%",[oe.BOTTOM_LEFT]:"0% 100%",[oe.BOTTOM_RIGHT]:"100% 100%",[oe.BOTTOM]:"50% 100%",[oe.RIGHT]:"100% 50%",[oe.LEFT]:"0% 50%"},Mt=Object.entries(wt).reduce(((e,[t,a])=>(e[a]=t,e)),{}),Nt=[ie.TILE,ie.TILE_HORIZONTAL,ie.TILE_VERTICAL,ie.LEGACY_BG_FIT_AND_TILE,ie.LEGACY_BG_FIT_AND_TILE_HORIZONTAL,ie.LEGACY_BG_FIT_AND_TILE_VERTICAL],yt=[ie.LEGACY_ORIGINAL_SIZE,ie.ORIGINAL_SIZE,ie.LEGACY_BG_NORMAL];function At(e,t,{width:a,height:n}){return e===ie.TILE&&t.width>a&&t.height>n}function Ot(e,{width:t,height:a}){if(!t||!a){const n=t||Math.min(980,e.width),i=n/e.width;return{width:n,height:a||e.height*i}}return{width:t,height:a}}function Ct(e,t,a,n="center"){const i={img:{},container:{}};if(e===ie.SCALE_TO_FILL){const e=t.focalPoint&&function(e){const t=`${e.x}% ${e.y}%`;return Mt[t]||""}(t.focalPoint),r=e||n;t.focalPoint&&!e?i.img={objectPosition:kt(t,a,t.focalPoint)}:i.img={objectPosition:wt[r]}}else[ie.LEGACY_ORIGINAL_SIZE,ie.ORIGINAL_SIZE].includes(e)?i.img={objectFit:"none",top:"auto",left:"auto",right:"auto",bottom:"auto"}:Nt.includes(e)&&(i.container={backgroundSize:`${t.width}px ${t.height}px`});return i}function kt(e,t,a){const{width:n,height:i}=e,{width:r,height:o}=t,{x:s,y:c}=a;if(!r||!o)return`${s}% ${c}%`;const l=Math.max(r/n,o/i),d=n*l,u=i*l,m=Math.max(0,Math.min(d-r,d*(s/100)-r/2)),h=Math.max(0,Math.min(u-o,u*(c/100)-o/2));return`${m&&Math.floor(m/(d-r)*100)}% ${h&&Math.floor(h/(u-o)*100)}%`}const St={width:"100%",height:"100%"};function Rt(e,t,a,n={}){const{autoEncode:i=!0,isSEOBot:r,shouldLoadHQImage:o,hasAnimation:s,allowAnimatedTransform:c,encoding:l}=n;if(!Me(e,t,a))return he;const d=void 0===c||c,u=Ne(t.id,s,d);if(!u||o)return Pt(e,t,a,{...n,autoEncode:i,useSrcset:u});const m={...a,...Ot(t,a)},{alignment:h,htmlTag:p}=m,g=At(e,t,m),f=function(e,t,{width:a,height:n},i=!1){if(i)return{width:a,height:n};const r=!yt.includes(e),o=At(e,t,{width:a,height:n}),s=!o&&Nt.includes(e),c=s?t.width:a,l=s?t.height:n,d=r?function(e,t){return e>900?t?.05:.15:e>500?t?.1:.18:e>200?.25:1}(c,ye(t.id)):1;return{width:o?1920:c*d,height:l*d}}(e,t,m,r),E=function(e,t,a){return a?0:Nt.includes(t)?1:e>200?2:3}(m.width,e,r),T=function(e,t){const a=Nt.includes(e)&&!t;return e===ie.SCALE_TO_FILL||a?ie.SCALE_TO_FIT:e}(e,g),I=Ct(e,t,a,h),{uri:_}=Pt(T,t,{...f,alignment:h,htmlTag:p},{autoEncode:i,filters:E?{blur:E}:{},hasAnimation:s,allowAnimatedTransform:d,encoding:l}),{attr:b={},css:v}=Pt(e,t,{...m,alignment:h,htmlTag:p},{});return v.img=v.img||{},v.container=v.container||{},Object.assign(v.img,I.img,St),Object.assign(v.container,I.container),{uri:_,css:v,attr:b,transformed:!0}}function Pt(e,t,a,n){let i={};if(Me(e,t,a)){const r=rt(e,t,a),o=it(e,t,r,n);i.uri=Lt(e,t,r,n,o),n?.useSrcset&&(i.srcset=function(e,t,a,n,i){const r=a.pixelAspectRatio||1;return{dpr:[`${1===r?i.uri:Lt(e,t,{...a,pixelAspectRatio:1},n)} 1x`,`${2===r?i.uri:Lt(e,t,{...a,pixelAspectRatio:2},n)} 2x`]}}(e,t,r,n,i)),Object.assign(i,function(e,t){let a;return a=t.htmlTag===le.BG?Ze:t.htmlTag===le.SVG?Ke:Xe,a(e,t)}(o,r),{transformed:o.transformed})}else i=he;return i}const Gt="https://static.wixstatic.com/media/";"undefined"!=typeof window&&window.devicePixelRatio;We();We();const xt=Gt,{STATIC_MEDIA_URL:Ft}=e,Bt=({fittingType:e,src:t,target:a,options:n})=>{const i=Rt(e,t,a,{...n,autoEncode:!0});return i?.uri&&!/^[a-z]+:/.test(i.uri)&&(i.uri=`${Ft}${i.uri}`),i},Ht=/^[a-z]+:/,Yt=e=>{const{id:t,containerId:a,uri:n,alt:i,name:o="",role:s,width:c,height:l,displayMode:d,devicePixelRatio:u,quality:m,alignType:h,bgEffectName:p="",focalPoint:g,upscaleMethod:f,className:E="",crop:T,imageStyles:I={},targetWidth:_,targetHeight:b,targetScale:v,onLoad:L=()=>{},onError:w=()=>{},shouldUseLQIP:M,containerWidth:N,containerHeight:y,getPlaceholder:A,isInFirstFold:O,placeholderTransition:C,socialAttrs:k,isSEOBot:S,skipMeasure:R,hasAnimation:P,encoding:G}=e,x=r.useRef(null);let F="";const B="blur"===C,H=r.useRef(null);if(!H.current)if(A||M||O||S){const e={upscaleMethod:f,...m||{},shouldLoadHQImage:O,isSEOBot:S,hasAnimation:P,encoding:G};H.current=(A||Bt)({fittingType:d,src:{id:n,width:c,height:l,crop:T,name:o,focalPoint:g},target:{width:N,height:y,alignment:h,htmlTag:"img"},options:e}),F=!H.current.transformed||O||S?"":"true"}else H.current={uri:void 0,css:{img:{}},attr:{img:{},container:{}},transformed:!1};const Y=!S&&(A||M)&&!O&&H.current.transformed,U=r.useMemo((()=>JSON.stringify({containerId:a,...a&&{containerId:a},...h&&{alignType:h},...R&&{skipMeasure:!0},displayMode:d,...N&&{targetWidth:N},...y&&{targetHeight:y},..._&&{targetWidth:_},...b&&{targetHeight:b},...v&&{targetScale:v},isLQIP:Y,isSEOBot:S,lqipTransition:C,encoding:G,imageData:{width:c,height:l,uri:n,name:o,displayMode:d,hasAnimation:P,...m&&{quality:m},...u&&{devicePixelRatio:u},...g&&{focalPoint:g},...T&&{crop:T},...f&&{upscaleMethod:f}}})),[a,h,R,d,N,y,_,b,v,Y,S,C,G,c,l,n,o,P,m,u,g,T,f]),D=H.current,V=D?.uri,j=D?.srcset,$=D.css?.img,z=`${X} ${E}`;r.useEffect((()=>{const e=x.current;L&&e?.currentSrc&&e?.complete&&L({target:e})}),[]);const W=D&&!D?.transformed?`max(${c}px, 100%)`:_?`${_}px`:null;return r.createElement("wow-image",{id:t,class:z,"data-image-info":U,"data-motion-part":`BG_IMG ${a}`,"data-bg-effect-name":p,"data-has-ssr-src":F,"data-animate-blur":!S&&Y&&B?"":void 0,style:W?{"--wix-img-max-width":W}:{}},r.createElement("img",{src:V,ref:x,alt:i||"",role:s,style:{...$,...I},onLoad:L,onError:w,width:N||void 0,height:y||void 0,...k,srcSet:O?j?.dpr?.map((e=>Ht.test(e)?e:`${Ft}${e}`)).join(", "):void 0,fetchpriority:O?"high":void 0,loading:!1===O?"lazy":void 0,suppressHydrationWarning:!0}))};var Ut="Tj01hh";var Dt=e=>{var t,n;const{id:i,alt:o,role:s,className:l,imageStyles:d={},targetWidth:u,targetHeight:m,onLoad:h,onError:p,containerWidth:g,containerHeight:f,isInFirstFold:E,socialAttrs:T,skipMeasure:I,responsiveImageProps:_,zoomedImageResponsiveOverride:b,displayMode:v}=e,L=u||g,w=m||f,{fallbackSrc:M,srcset:N,sources:y,css:A}=_||{},{width:O,height:C,...k}=(null==_||null==(t=_.css)?void 0:t.img)||{},S="original_size"===v?null==_||null==(n=_.css)?void 0:n.img:k;var R;return M&&N&&A?r.createElement("img",a()({fetchpriority:E?"high":void 0,loading:!1===E?"lazy":void 0,sizes:L+"px",srcSet:I?null==b?void 0:b.srcset:null==_?void 0:_.srcset,id:i,src:M,alt:o||"",role:s,style:{...d,...I?{...null==b||null==(R=b.css)?void 0:R.img}:{...S}},onLoad:h,onError:p,className:c(l,Ut),width:L,height:w},T)):M&&y&&A?r.createElement("picture",null,y.map((e=>{let{srcset:t,media:a,sizes:n}=e;return r.createElement("source",{key:a,srcSet:t,media:a,sizes:n})})),r.createElement("img",a()({fetchpriority:E?"high":void 0,loading:!1===E?"lazy":void 0,id:i,src:y[0].fallbackSrc,alt:o||"",role:s,style:{...d,objectFit:y[0].imgStyle.objectFit,objectPosition:y[0].imgStyle.objectPosition},onLoad:h,onError:p,className:c(l,Ut),width:L,height:w},T))):r.createElement(Yt,e)};var Vt=e=>{var t,a,n;const{className:i,customIdPrefix:o,getPlaceholder:s,hasAnimation:c,...l}=e,d=r.useMemo((()=>JSON.stringify({containerId:l.containerId,alignType:l.alignType,fittingType:l.displayMode,hasAnimation:c,imageData:{width:l.width,height:l.height,uri:l.uri,name:l.name,...l.quality&&{quality:l.quality},displayMode:l.displayMode}})),[l,c]),u=r.useRef(null);u.current||(u.current=s?s({fittingType:l.displayMode,src:{id:l.uri,width:l.width,height:l.height,name:l.name},target:{width:l.containerWidth,height:l.containerHeight,alignment:l.alignType,htmlTag:"bg"},options:{hasAnimation:c,allowAnimatedTransform:!1}}):{uri:void 0,css:{img:{}},attr:{img:{},container:{}}});const m=u.current,h=null!=(t=null==m?void 0:m.uri)?t:"",p=null!=(a=null==(n=m.css)?void 0:n.container)?a:{},g=Object.assign(h?{backgroundImage:"url("+h+")"}:{},p);return r.createElement("wix-bg-image",{id:""+(o||"bgImg_")+l.containerId,class:i,style:g,"data-tiled-image-info":d,"data-has-bg-scroll-effect":l.hasBgScrollEffect||"","data-bg-effect-name":l.bgEffectName||"","data-motion-part":"BG_IMG "+l.containerId})};const jt=new RegExp("<%= compId %>","g"),$t=(e,t)=>e.replace(jt,t);var zt=e=>null==e?void 0:e.replace(":hover",""),Wt="bX9O_S",Zt="Z_wCwr",qt="Jxk_UL",Jt="K8MSra",Xt="YTb3b4";const Kt={quality:{unsharpMask:{radius:.33,amount:1,threshold:0}},devicePixelRatio:1};var Qt=e=>{const{id:t,videoRef:n,videoInfo:i,posterImageInfo:o,muted:s,preload:l,loop:d,alt:u,isVideoEnabled:m,getPlaceholder:h,extraClassName:p=""}=e;i.containerId=zt(i.containerId);const g=r.useMemo((()=>JSON.stringify(i)),[i]),f=r.createElement(r.Fragment,null,o.filterEffectSvgString&&r.createElement("svg",{id:"svg_"+i.containerId,className:Xt},r.createElement("defs",{dangerouslySetInnerHTML:{__html:$t(o.filterEffectSvgString,i.containerId)}})),r.createElement(Dt,a()({key:i.videoId+"_img",id:o.containerId+"_img",className:c(Zt,qt,"bgVideoposter",p),imageStyles:{width:"100%",height:"100%"}},o,Kt,{getPlaceholder:h})));return m?r.createElement("wix-video",{id:t,"data-video-info":g,"data-motion-part":"BG_IMG "+i.containerId,class:c(Wt,"bgVideo",p)},r.createElement("video",{key:i.videoId+"_video",ref:n,id:i.containerId+"_video",className:Jt,crossOrigin:"anonymous","aria-label":u,playsInline:!0,preload:l,muted:s,loop:d}),f):f},ea="SUz0WK";var ta=e=>{const{id:t,containerId:a,pageId:n,children:i,bgEffectName:o="",containerSize:s}=e;return r.createElement("wix-bg-media",{id:t,class:ea,"data-container-id":a,"data-container-size":((null==s?void 0:s.width)||0)+", "+((null==s?void 0:s.height)||0),"data-page-id":n,"data-bg-effect-name":o,"data-motion-part":"BG_MEDIA "+a},i)};const aa="bgOverlay";var na="m4khSP",ia="FNxOn5";var ra=e=>{const{imageOverlay:t}=e;return r.createElement("div",{"data-testid":aa,className:na},t&&r.createElement(Vt,a()({customIdPrefix:"bgImgOverlay_",className:ia},t)))};const oa="bgLayers",sa="colorUnderlay",ca="mediaPadding",la="canvas";var da="MW5IWV",ua="N3eg0s",ma="Kv1aVt",ha="dLPlxY",pa="VgO9Yg",ga="LWbAav",fa="yK6aSC",Ea="K_YxMd",Ta="NGjcJN",Ia="mNGsUM",_a="I8xA4L";const ba="bgImage";var va=e=>{const{videoRef:t,canvasRef:n,hasBgFullscreenScrollEffect:i,image:o,backgroundImage:s,backgroundMedia:l,video:d,backgroundOverlay:u,shouldPadMedia:m,extraClass:h="",shouldRenderUnderlay:p=!d,reducedMotion:g=!1,getPlaceholder:f,hasCanvasAnimation:E,useWixMediaCanvas:T,onClick:I}=e,{onImageLoad:_}=(e=>{let{onReady:t,image:a}=e;return(0,r.useEffect)((()=>{t&&!a&&t()}),[t,a]),{onImageLoad:e=>{null!=a&&a.onLoad&&a.onLoad(e),t&&t()}}})(e),b=zt(e.containerId),v="img_"+zt(b),L=o&&r.createElement(Dt,a()({id:v,className:c(ma,ha,Ia,ba),imageStyles:{width:"100%",height:"100%"},getPlaceholder:f},o,{onLoad:_})),w=s&&r.createElement(Vt,a()({},s,{containerId:b,className:c(ma,ha,Ia,ba),getPlaceholder:f})),M=d&&r.createElement(Qt,a()({id:"videoContainer_"+b},d,{extraClassName:fa,reducedMotion:g,videoRef:t,getPlaceholder:f})),N=T&&n||E?r.createElement("wix-media-canvas",{"data-container-id":b,class:E?_a:""},L,w,M,r.createElement("canvas",{id:b+"webglcanvas",className:c(Ea,"webglcanvas"),"aria-label":(null==d?void 0:d.alt)||"",role:"presentation","data-testid":la})):r.createElement(r.Fragment,null,L,w,M,n&&r.createElement("canvas",{id:b+"webglcanvas",ref:n,className:c(Ea,"webglcanvas"),"aria-label":(null==d?void 0:d.alt)||"",role:"presentation","data-testid":la})),y=l?r.createElement(ta,a()({id:"bgMedia_"+b},l),N):r.createElement("div",{id:"bgMedia_"+b,"data-motion-part":"BG_MEDIA "+b,className:pa},N),A=u&&r.createElement(ra,u);return r.createElement("div",{id:oa+"_"+b,"data-hook":oa,"data-motion-part":"BG_LAYER "+b,className:c(da,h,{[ua]:i}),onClick:I},p&&r.createElement("div",{"data-testid":sa,className:c(ga,ma)}),m?r.createElement("div",{"data-testid":ca,className:Ta},y,A):r.createElement(r.Fragment,null,y,A))};const La="mesh-container-content",wa="inline-content",Ma=e=>o().createElement("div",{key:e.props.id+"-rotated-wrapper","data-mesh-id":e.props.id+"-rotated-wrapper"},e),Na=(e,t)=>{const{id:n,className:i,wedges:r=[],rotatedComponents:s=[],children:l,fixedComponents:d=[],extraClassName:m="",renderRotatedComponents:h=Ma}=e,p=o().Children.toArray(l()),g=[],f=[];p.forEach((e=>d.includes(e.props.id)?g.push(e):f.push(e)));const E=(e=>{const{wedges:t,rotatedComponents:a,childrenArray:n,renderRotatedComponents:i}=e,r=a.reduce(((e,t)=>({...e,[t]:!0})),{});return[...n.map((e=>{return r[(t=e,t.props.id.split("__")[0])]?i(e):e;var t})),...t.map((e=>o().createElement("div",{key:e,"data-mesh-id":e})))]})({childrenArray:f,rotatedComponents:s,wedges:r,renderRotatedComponents:h});return o().createElement("div",a()({},u(e),{"data-mesh-id":n+"inlineContent","data-testid":wa,className:c(i,m),ref:t}),o().createElement("div",{"data-mesh-id":n+"inlineContent-gridContainer","data-testid":La},E),g)};var ya=o().forwardRef(Na),Aa="jpeeyX",Oa="PbIVkr",Ca="YN_fLB",ka="jFliG2",Sa="n74Psq";var Ra=e=>{let{classNames:t,layerIds:n,children:i,fillLayers:r,meshProps:s,id:l}=e;return o().createElement(o().Fragment,null,o().createElement("div",{id:n.overlay,className:c(Oa,{[Ca]:t.includes("horizontallyDocked")})}),o().createElement("div",{id:n.container,className:Aa,"data-block-level-container":"MenuContainer"},o().createElement(va,r),o().createElement("div",{id:n.inlineContentParent,className:c(Sa,{[ka]:t.includes("verticallyDocked")})},o().createElement(ya,a()({id:l},s,{"aria-label":"navigation dialog"}),i))))};const Pa=(e,t)=>{const{id:n,customClassNames:i=[],isOpen:o,fillLayers:s,children:l,classNames:d,meshProps:m,className:h,onClick:p,onMouseEnter:g,onMouseLeave:f,open:E,close:T,animate:I}=e;let{isVisible:_}=e;o&&!_&&(_=!0);const b={overlay:"overlay-"+n,container:"container-"+n,inlineContentParent:"inlineContentParent-"+n};return r.useImperativeHandle(t,(()=>({open:E,close:T,animate:I}))),r.useEffect((()=>{if(o){const e=document.querySelector("#"+b.inlineContentParent+" ."+k.label);null==e||e.focus()}}),[o]),r.createElement("div",a()({id:n,role:"dialog","aria-modal":"true"},u(e),{tabIndex:0,onClick:p,onMouseEnter:g,onMouseLeave:f,"data-undisplayed":!_,className:c(J.menuContainer,h,v(q.root,...i),d.map((e=>J[e])),{[J.visible]:_,[J.open]:o}),"data-block-level-container":"MenuContainer"}),r.createElement(Ra,{id:n,layerIds:b,fillLayers:s,classNames:d,meshProps:m},l))};var Ga=r.forwardRef(Pa);var xa=U((e=>{let{mapperProps:t,stateValues:a,controllerUtils:{updateStyles:n}}=e;const{isMobileView:i,compId:r,...o}=t,{toggle:s,...c}=a;return!0===a.isOpen&&n({"--menu-height":d()&&i?window.getComputedStyle(document.body).height:"100vh"}),{...o,...(l=c,Object.entries(l).reduce(((e,t)=>{let[a,n]=t;return void 0===n?e:{...e,[a]:n}}),{})),onClick:e=>{e.target.closest("a , #overlay-"+r)&&s&&(null==s||s(!0))}};var l}));var Fa={root:"mobile-menu__icon"},Ba="MyaVaS",Ha="gIn6Wf",Ya="ZhsSZ_",Ua="P4my70",Da="oX7wPA",Va="enVN3p";var ja="JJHA1E",$a="XLAVDo",za="obddOt",Wa="SRM_9q",Za="YQwk0a",qa="eyQVgg",Ja="BU_Jm5";var Xa="MEduRo",Ka="B1Ti_C",Qa="naeVRo",en="HUKlBY",tn="_W7Xw0",an="RWu2Qu",nn="plHZxT",rn="oo_S7a",on="dQWoWx";var sn="PztR8I",cn="baZw44",ln="T8_LA1",dn="dbDvn7",un="iu4m2t",mn="HZW1lN";var hn="hSKHso",pn="aoIbio",gn="tbJLVX",fn="YB05pm",En="SofRfX",Tn="XOaVd6";var In="VaMfZ_",_n="MsXubo",bn="L8RJUm";var vn="Ez9DzT",Ln="Klh_2G";var wn="XEQtWV",Mn="muO8XP";var Nn="unAAcL",yn="SACRXu",An="Jzi8Yx",On="qTZmEh",Cn="IOOdvm",kn="JDwVHd";var Sn="jJdFnD",Rn="ROpKdV",Pn="Ic34Nb";var Gn="Kx2Lb_",xn="kLibfv",Fn="b2zCu2",Bn="GYW_Vv",Hn="fIrRrT";var Yn="fNnJyG",Un="O240SO",Dn="dm9Pzg";var Vn="TR1lP5",jn="JOBrqh",$n="KKTZfd",zn="TpQgIG",Wn="PGlfJM",Zn="ja83fW";var qn="HoyfNC",Jn="BCbKRj";var Xn="vXxIVB",Kn="l0AfMo",Qn="kFhkeL",ei="rBz8fx",ti="FkbwVM",ai="CYMuk3",ni="uWZnSw",ii="sNnUw0";var ri="H801ob",oi="kpM1s0",si="ZBaDfe",ci="LZBOca",li="HSYWnj",di="r8m7zZ";const ui={inlinePopupToggleSkin1:e=>{let{isOpen:t}=e;return o().createElement("span",{className:c(Va,t&&Da)})},inlinePopupToggleSkin2:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c($a,t&&ja)},o().createElement("div",{className:c(za,Wa,Za)}),o().createElement("div",{className:c(za,Ja)}),o().createElement("div",{className:c(za,Wa,qa)}))},inlinePopupToggleSkin3:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c(Ka,t&&Xa)},o().createElement("div",{className:c(en,Qa,rn)}),o().createElement("div",{className:c(en,Qa,on)}),o().createElement("div",{className:c(tn,Qa)}),o().createElement("div",{className:c(an,Qa)}),o().createElement("div",{className:c(nn,Qa,rn)}),o().createElement("div",{className:c(nn,Qa,on)}))},inlinePopupToggleSkin4:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c(cn,t&&sn)},o().createElement("span",{className:c(ln,mn)}),o().createElement("span",{className:c(ln,dn)}),o().createElement("span",{className:c(ln,un)}))},inlinePopupToggleSkin5:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c(pn,t&&hn)},o().createElement("div",{className:c(gn)},o().createElement("span",{className:c(fn)},"ME",o().createElement("br",null),"NU")),o().createElement("div",{className:c(En)}),o().createElement("div",{className:c(Tn)}))},inlinePopupToggleSkin6:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c(_n,t&&In)},o().createElement("div",{className:c(bn)}),o().createElement("div",{className:c(bn)}),o().createElement("div",{className:c(bn)}),o().createElement("div",{className:c(bn)}),o().createElement("div",{className:c(bn)}),o().createElement("div",{className:c(bn)}),o().createElement("div",{className:c(bn)}),o().createElement("div",{className:c(bn)}),o().createElement("div",{className:c(bn)}))},inlinePopupToggleSkin7:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c(Ln,t&&vn)},o().createElement("span",null),o().createElement("span",null),o().createElement("span",null))},inlinePopupToggleSkin8:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c(Mn,t&&wn)},o().createElement("span",null))},inlinePopupToggleSkin9:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c(yn,t&&Nn)},o().createElement("div",{className:c(On,Cn)}),o().createElement("div",{className:c(An)}),o().createElement("div",{className:c(On,kn)}))},inlinePopupToggleSkin10:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c(Rn,Pn,t&&Sn)},o().createElement("span",null),o().createElement("span",null))},inlinePopupToggleSkin11:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c(xn,t&&Gn)},o().createElement("div",{className:c(Bn)}),o().createElement("div",{className:c(Fn)}),o().createElement("div",{className:c(Hn)}))},inlinePopupToggleSkin12:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c(Un,t&&Yn)},o().createElement("div",{className:c(Dn)}),o().createElement("div",{className:c(Dn)}),o().createElement("div",{className:c(Dn)}))},inlinePopupToggleSkin13:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c(jn,t&&Vn)},o().createElement("div",{className:c($n,zn)}),o().createElement("div",{className:c($n,Wn)}),o().createElement("div",{className:c($n,Zn)}))},inlinePopupToggleSkin14:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c(Jn,t&&qn)},o().createElement("span",null),o().createElement("span",null))},inlinePopupToggleSkin15:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c(Kn,t&&Xn)},o().createElement("div",{className:c(ei,Qn,ni)}),o().createElement("div",{className:c(ei,Qn,ii)}),o().createElement("div",{className:c(ti,Qn)}),o().createElement("div",{className:c(ai,Qn,ni)}),o().createElement("div",{className:c(ai,Qn,ii)}))},inlinePopupToggleSkin16:e=>{let{isOpen:t}=e;return o().createElement("div",{className:c(oi,t&&ri)},o().createElement("div",{className:c(ci,si)}),o().createElement("div",{className:c(li,si)}),o().createElement("div",{className:c(di,si)}))}};var mi=e=>{const{id:t,customClassNames:n=[],skin:i="inlinePopupToggleSkin1",isOpen:o=!1,className:s,onClick:l,onKeyDown:d,onMouseEnter:m,onMouseLeave:h,translations:p,wrapToggleWithNav:g=!1}=e,f=r.useRef(null);var E;o||(null==(E=f.current)||E.focus());const T=p.buttonAriaLabelOpen||"Open navigation menu",I=p.buttonAriaLabelClose||"Close",_=ui[i];return g?r.createElement("nav",{id:t,"aria-label":p.navAriaLabel,className:c(Ba,{[Ya]:o})},r.createElement("div",a()({},u(e),{className:c(Ha,o&&Ya,s,v(Fa.root,...n)),ref:f,role:"button","aria-haspopup":"true","aria-expanded":o,"aria-label":""+(o?I:T),tabIndex:0,onClick:l,onKeyDown:d,onMouseEnter:m,onMouseLeave:h}),r.createElement("div",{className:c(Ua,o&&Ya)},r.createElement(_,{isOpen:o})))):r.createElement("div",a()({id:t},u(e),{className:c(Ha,o&&Ya,s,v(Fa.root,...n)),ref:f,role:"button","aria-label":""+(o?I:T),tabIndex:0,onClick:l,onKeyDown:d,onMouseEnter:m,onMouseLeave:h}),r.createElement("div",{className:c(Ua,o&&Ya)},r.createElement(_,{isOpen:o})))};var hi=U((e=>{var t;let{stateValues:a,mapperProps:n}=e;return{...n,isOpen:null!=(t=a.isOpen)?t:n.isOpen,onClick:()=>null==a.toggle?void 0:a.toggle(!1),onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||null==a.toggle||a.toggle(!1)}}})),pi=n(96114),gi=n.n(pi);function fi(){if(!d())return{x:0,y:0,isAtPageBottom:!1};const{left:e,top:t}=document.body.getBoundingClientRect();return{x:e,y:t,isAtPageBottom:window.innerHeight+window.scrollY===document.body.scrollHeight}}const Ei="backToTopRoot",Ti="backToTopSvg";var Ii="rG7wwD",_i="tjpQKX",bi="wnbU2e";const vi=()=>{window.scroll({top:0,left:0,behavior:"smooth"})};const Li={ExpandableMenu:{component:H,controller:Z},MenuContainer:{component:Ga,controller:xa},MenuToggle:{component:mi,controller:hi},BackToTopButton:{component:e=>{const{id:t,svgContent:n,isVisible:i,onHide:s,onShow:m,scrollBreakpoint:h,className:p}=e,g=l((()=>{s()}),3e3);!function(e,t,a){void 0===a&&(a={}),a={waitFor:100,disabled:!1,...a};const n=(0,r.useRef)(fi());let i=null;const o=()=>{gi().measure((()=>{const t=fi(),a=n.current;n.current=t,i=null,gi().mutate((()=>e({prevPos:a,currPos:t})))}))};(d()?r.useLayoutEffect:r.useEffect)((()=>{if(!d())return;const e=()=>{null===i&&(i=window.setTimeout(o,a.waitFor))};return a.disabled?()=>{}:(window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e),i&&window.clearTimeout(i)})}),t)}((e=>{let{currPos:t}=e;return a=Math.abs(t.y),void(!i&&a>h&&(m(),g()));var a}),[i]);const f=c(Ii,p,{[bi]:i});return o().createElement("div",a()({id:t},u(e),{className:f,onClick:vi,"data-testid":Ei}),o().createElement("div",{dangerouslySetInnerHTML:{__html:n&&$t(n,t)},className:_i,"data-testid":Ti}))},controller:U((e=>{let{mapperProps:t,controllerUtils:a}=e;return{...t,onShow:()=>{a.updateProps({isVisible:!0})},onHide:()=>{a.updateProps({isVisible:!1})}}}))}}}(),i}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt_mobile.b96fbdfc.bundle.min.js.map