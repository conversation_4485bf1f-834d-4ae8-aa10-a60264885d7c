<?php $attributes = $attributes->exceptProps([
    'number' => '01',
    'title' => 'Service Title',
    'href' => '#',
    'description' => null,
    'features' => [],
    'animated' => true
]); ?>
<?php foreach (array_filter(([
    'number' => '01',
    'title' => 'Service Title',
    'href' => '#',
    'description' => null,
    'features' => [],
    'animated' => true
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php
$cardClasses = 'service-item group relative overflow-hidden bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out';
if ($animated) {
    $cardClasses .= ' hover:transform hover:-translate-y-2';
}
?>

<div <?php echo e($attributes->merge(['class' => $cardClasses])); ?> data-number="<?php echo e($number); ?>">
    <div class="service-content p-6">
        <!-- Service Number -->
        <div class="service-number absolute top-4 right-4 text-2xl font-bold text-gray-200 group-hover:text-accent-color transition-colors duration-300">
            <?php echo e($number); ?>

        </div>
        
        <!-- Service Link -->
        <a href="<?php echo e($href); ?>" class="service-link block">
            <div class="flex items-center justify-between">
                <h3 class="service-title text-xl font-semibold text-gray-900 group-hover:text-accent-color transition-colors duration-300">
                    <?php echo e($title); ?>

                </h3>
                
                <div class="service-arrow text-2xl text-gray-400 group-hover:text-accent-color group-hover:transform group-hover:translate-x-1 transition-all duration-300">
                    →
                </div>
            </div>
            
            <?php if($description): ?>
                <p class="service-description mt-3 text-gray-600 text-sm leading-relaxed">
                    <?php echo e($description); ?>

                </p>
            <?php endif; ?>
            
            <?php if(!empty($features)): ?>
                <ul class="service-features mt-4 space-y-1">
                    <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="text-xs text-gray-500 flex items-center">
                            <span class="w-1 h-1 bg-accent-color rounded-full mr-2"></span>
                            <?php echo e($feature); ?>

                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            <?php endif; ?>
        </a>
        
        <!-- Hover Effect Overlay -->
        <div class="absolute inset-0 bg-gradient-to-r from-accent-color/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
    </div>
    
    <!-- Bottom Border Animation -->
    <div class="absolute bottom-0 left-0 w-0 h-1 bg-accent-color group-hover:w-full transition-all duration-500 ease-out"></div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.service-item {
    position: relative;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.1), transparent);
    transition: left 0.5s ease;
}

.service-item:hover::before {
    left: 100%;
}

.service-link {
    display: block;
    text-decoration: none;
    color: inherit;
    height: 100%;
}

.service-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    transition: var(--transition);
    margin-bottom: 0.5rem;
}

.service-arrow {
    font-size: 1.5rem;
    color: var(--gray-medium);
    transition: var(--transition);
}

.service-item:hover .service-title {
    color: var(--accent-color);
}

.service-item:hover .service-arrow {
    color: var(--accent-color);
    transform: translateX(5px);
}

.service-number {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-light);
    transition: var(--transition);
}

.service-item:hover .service-number {
    color: var(--accent-color);
}

@media (max-width: 768px) {
    .service-item {
        margin-bottom: 1rem;
    }
}
</style>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\laragon\www\codibu\resources\views/components/service-card.blade.php ENDPATH**/ ?>