<?php $attributes = $attributes->exceptProps([
    'type' => 'button',
    'variant' => 'primary',
    'size' => 'md',
    'href' => null,
    'target' => '_self',
    'disabled' => false,
    'loading' => false,
    'icon' => null,
    'iconPosition' => 'left'
]); ?>
<?php foreach (array_filter(([
    'type' => 'button',
    'variant' => 'primary',
    'size' => 'md',
    'href' => null,
    'target' => '_self',
    'disabled' => false,
    'loading' => false,
    'icon' => null,
    'iconPosition' => 'left'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php
$baseClasses = 'inline-flex items-center justify-center font-semibold transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

$variantClasses = [
    'primary' => 'bg-accent-color text-white hover:bg-opacity-90 focus:ring-accent-color border-2 border-accent-color',
    'secondary' => 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500 border-2 border-gray-200',
    'outline' => 'bg-transparent text-accent-color border-2 border-accent-color hover:bg-accent-color hover:text-white focus:ring-accent-color',
    'ghost' => 'bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500 border-2 border-transparent',
    'danger' => 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 border-2 border-red-600'
];

$sizeClasses = [
    'sm' => 'px-3 py-1.5 text-sm rounded-md',
    'md' => 'px-4 py-2 text-base rounded-lg',
    'lg' => 'px-6 py-3 text-lg rounded-lg',
    'xl' => 'px-8 py-4 text-xl rounded-xl'
];

$classes = $baseClasses . ' ' . ($variantClasses[$variant] ?? $variantClasses['primary']) . ' ' . ($sizeClasses[$size] ?? $sizeClasses['md']);

if ($loading) {
    $classes .= ' cursor-wait';
}
?>

<?php if($href): ?>
    <a 
        href="<?php echo e($href); ?>" 
        target="<?php echo e($target); ?>"
        <?php echo e($attributes->merge(['class' => $classes])); ?>

        <?php if($disabled): ?> aria-disabled="true" <?php endif; ?>
    >
        <?php if($loading): ?>
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        <?php elseif($icon && $iconPosition === 'left'): ?>
            <span class="mr-2"><?php echo $icon; ?></span>
        <?php endif; ?>
        
        <?php echo e($slot); ?>

        
        <?php if($icon && $iconPosition === 'right'): ?>
            <span class="ml-2"><?php echo $icon; ?></span>
        <?php endif; ?>
    </a>
<?php else: ?>
    <button 
        type="<?php echo e($type); ?>"
        <?php echo e($attributes->merge(['class' => $classes])); ?>

        <?php if($disabled || $loading): ?> disabled <?php endif; ?>
    >
        <?php if($loading): ?>
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        <?php elseif($icon && $iconPosition === 'left'): ?>
            <span class="mr-2"><?php echo $icon; ?></span>
        <?php endif; ?>
        
        <?php echo e($slot); ?>

        
        <?php if($icon && $iconPosition === 'right'): ?>
            <span class="ml-2"><?php echo $icon; ?></span>
        <?php endif; ?>
    </button>
<?php endif; ?>
<?php /**PATH C:\laragon\www\codibu\resources\views/components/button.blade.php ENDPATH**/ ?>