!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react"),require("react-dom")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt_menu",["react","reactDOM"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt_menu"]=t(require("react"),require("react-dom")):e["rb_wixui.thunderbolt_menu"]=t(e.<PERSON><PERSON>,e.ReactDOM)}("undefined"!=typeof self?self:this,(function(e,t){return function(){var n={91003:function(e,t,n){var r=n(82016);e.exports=r.create("HamburgerCloseButton872037521",{classes:{root:"HamburgerCloseButton872037521__root"},vars:{},stVars:{},keyframes:{},layers:{}},"",-1,e.id)},15076:function(e,t,n){var r=n(82016);e.exports=r.create("HamburgerMenuContainer502174924",{classes:{root:"HamburgerMenuContainer502174924__root"},vars:{},stVars:{},keyframes:{},layers:{}},"",-1,e.id)},2901:function(e,t,n){var r=n(82016);e.exports=r.create("HamburgerOpenButton3537389287",{classes:{root:"HamburgerOpenButton3537389287__root",nav:"HamburgerOpenButton3537389287__nav"},vars:{},stVars:{},keyframes:{},layers:{}},"",-1,e.id)},15535:function(e,t,n){var r=n(82016);e.exports=r.create("HamburgerOverlay547129737",{classes:{root:"HamburgerOverlay547129737__root",overlay:"HamburgerOverlay547129737__overlay",scrollContent:"HamburgerOverlay547129737__scrollContent"},vars:{"above-all-z-index":"--above-all-z-index","wix-ads-height":"--wix-ads-height"},stVars:{},keyframes:{},layers:{}},"",-1,e.id)},27232:function(e,t,n){var r=n(82016);e.exports=r.create("StylableButton2545352419",{classes:{root:"StylableButton2545352419__root",label:"StylableButton2545352419__label",link:"StylableButton2545352419__link",container:"StylableButton2545352419__container",icon:"StylableButton2545352419__icon"},vars:{},stVars:{},keyframes:{},layers:{}},"",-1,e.id)},97798:function(e,t,n){var r=n(82016);e.exports=r.create("StylableHorizontalMenu3372578893",{classes:{root:"StylableHorizontalMenu3372578893__root",scrollButton:"StylableHorizontalMenu3372578893__scrollButton",menu:"StylableHorizontalMenu3372578893__menu",menuItem:"StylableHorizontalMenu3372578893__menuItem",columnsLayout:"StylableHorizontalMenu3372578893__columnsLayout",megaMenuWrapper:"StylableHorizontalMenu3372578893__megaMenuWrapper",positionBox:"StylableHorizontalMenu3372578893__positionBox",containerPositionBox:"StylableHorizontalMenu3372578893__containerPositionBox"},vars:{"focus-ring-box-shadow":"--focus-ring-box-shadow"},stVars:{wrap:"var(--menu-flex-wrap, wrap)","container-margin-top":"var(--containerMarginTop)"},keyframes:{},layers:{}},"",-1,e.id)},93260:function(e,t,n){var r=n(82016);e.exports=r.create("itemDepth02233374943",{classes:{root:"itemDepth02233374943__root",container:"itemDepth02233374943__container",label:"itemDepth02233374943__label",itemWrapper:"itemDepth02233374943__itemWrapper",positionBox:"itemDepth02233374943__positionBox",animationBox:"itemDepth02233374943__animationBox",megaMenuComp:"itemDepth02233374943__megaMenuComp",alignBox:"itemDepth02233374943__alignBox",list:"itemDepth02233374943__list"},vars:{},stVars:{"columns-count":"var(--columnsAmount)","horizontal-spacing":"var(--horizontalSpacing)","white-space":"var(--white-space)","is-animated":"var(--is-animated)","animation-box-max-height":"var(--max-height, none)","animation-box-overflow-y":"var(--overflow-y, visible)","position-box-z-index":"var(--position-box-z-index, 47)"},keyframes:{fadeIn:"itemDepth02233374943__fadeIn"},layers:{}},"",-1,e.id)},29359:function(e,t,n){var r=n(82016);e.exports=r.create("itemDepth12472627565",{classes:{root:"itemDepth12472627565__root",container:"itemDepth12472627565__container",label:"itemDepth12472627565__label",itemWrapper:"itemDepth12472627565__itemWrapper",list:"itemDepth12472627565__list",positionBox:"itemDepth12472627565__positionBox",animationBox:"itemDepth12472627565__animationBox"},vars:{},stVars:{"subsubmenu-box-display":"var(--subsubmenu-box-display)","subsubmenu-box-position":"var(--subsubmenu-box-position)","subsubmenu-box-left":"var(--subsubmenu-box-left)","subsubmenu-box-right":"var(--subsubmenu-box-right)","white-space":"var(--white-space)","label-word-wrap":"var(--label-word-wrap)","columns-count":"var(--columns-count)","is-animated":"var(--is-animated)"},keyframes:{fadeIn:"itemDepth12472627565__fadeIn"},layers:{}},"",-1,e.id)},93067:function(e,t,n){var r=n(82016);e.exports=r.create("itemShared2352141355",{classes:{root:"itemShared2352141355__root",rootContainer:"itemShared2352141355__rootContainer",menuItem:"itemShared2352141355__menuItem",accessibilityIconWrapper:"itemShared2352141355__accessibilityIconWrapper",accessibilityIcon:"itemShared2352141355__accessibilityIcon"},vars:{},stVars:{},keyframes:{},layers:{}},"",-1,e.id)},20435:function(e,t,n){var r=n(82016);e.exports=r.create("submenu815198092",{classes:{root:"submenu815198092__root",menuItem:"submenu815198092__menuItem",heading:"submenu815198092__heading",listWrapper:"submenu815198092__listWrapper",pageWrapper:"submenu815198092__pageWrapper",pageStretchWrapper:"submenu815198092__pageStretchWrapper",containerPageStretchWrapper:"submenu815198092__containerPageStretchWrapper",overrideWidth:"submenu815198092__overrideWidth",rowItem:"submenu815198092__rowItem"},vars:{},stVars:{},keyframes:{},layers:{}},"",-1,e.id)},57683:function(e,t,n){var r=n(82016);e.exports=r.create("ScrollButton2305195801",{classes:{root:"ScrollButton2305195801__root",icon:"ScrollButton2305195801__icon"},vars:{},stVars:{},keyframes:{},layers:{}},"",-1,e.id)},23161:function(e,t,n){var r=n(82016);e.exports=r.create("ScrollControls2015960785",{classes:{root:"ScrollControls2015960785__root"},vars:{},stVars:{"display-controls":"var(--scroll-controls-display, flex)"},keyframes:{},layers:{}},"",-1,e.id)},82016:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createRenderable=t.create=void 0;t.create=function(e,t,n,r,o,a){const l={namespace:e,classes:t.classes,keyframes:t.keyframes,layers:t.layers,vars:t.vars,stVars:t.stVars,cssStates:function(e){const t=[];for(const n in e){const r=i(n,e[n]);r&&t.push(r)}return t.join(" ")},style:s,st:s,$id:o,$depth:r,$css:n};function i(t,n){if(!1===n||null==n||n!=n)return"";if(!0===n)return function(t){return`${e}--${t}`}(t);return function(t,n){return`${e}---${t}-${n.length}-${n.replace(/\s/gm,"_")}`}(t,n.toString())}function s(){const e=[];for(let t=0;t<arguments.length;t++){const n=arguments[t];if(n)if("string"==typeof n)e[e.length]=n;else if(1===t)for(const t in n){const r=i(t,n[t]);r&&(e[e.length]=r)}}return e.join(" ")}return a&&a.register(l),l},t.createRenderable=function(e,t,n){return{$css:e,$depth:t,$id:n,$theme:!0}}},52319:function(e){"use strict";e.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},53203:function(e,t,n){var r=n(52319),o=n(99429),a=Object.hasOwnProperty,l=Object.create(null);for(var i in r)a.call(r,i)&&(l[r[i]]=i);var s=e.exports={to:{},get:{}};function c(e,t,n){return Math.min(Math.max(t,e),n)}function u(e){var t=Math.round(e).toString(16).toUpperCase();return t.length<2?"0"+t:t}s.get=function(e){var t,n;switch(e.substring(0,3).toLowerCase()){case"hsl":t=s.get.hsl(e),n="hsl";break;case"hwb":t=s.get.hwb(e),n="hwb";break;default:t=s.get.rgb(e),n="rgb"}return t?{model:n,value:t}:null},s.get.rgb=function(e){if(!e)return null;var t,n,o,l=[0,0,0,1];if(t=e.match(/^#([a-f0-9]{6})([a-f0-9]{2})?$/i)){for(o=t[2],t=t[1],n=0;n<3;n++){var i=2*n;l[n]=parseInt(t.slice(i,i+2),16)}o&&(l[3]=parseInt(o,16)/255)}else if(t=e.match(/^#([a-f0-9]{3,4})$/i)){for(o=(t=t[1])[3],n=0;n<3;n++)l[n]=parseInt(t[n]+t[n],16);o&&(l[3]=parseInt(o+o,16)/255)}else if(t=e.match(/^rgba?\(\s*([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/)){for(n=0;n<3;n++)l[n]=parseInt(t[n+1],0);t[4]&&(t[5]?l[3]=.01*parseFloat(t[4]):l[3]=parseFloat(t[4]))}else{if(!(t=e.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/)))return(t=e.match(/^(\w+)$/))?"transparent"===t[1]?[0,0,0,0]:a.call(r,t[1])?((l=r[t[1]])[3]=1,l):null:null;for(n=0;n<3;n++)l[n]=Math.round(2.55*parseFloat(t[n+1]));t[4]&&(t[5]?l[3]=.01*parseFloat(t[4]):l[3]=parseFloat(t[4]))}for(n=0;n<3;n++)l[n]=c(l[n],0,255);return l[3]=c(l[3],0,1),l},s.get.hsl=function(e){if(!e)return null;var t=e.match(/^hsla?\(\s*([+-]?(?:\d{0,3}\.)?\d+)(?:deg)?\s*,?\s*([+-]?[\d\.]+)%\s*,?\s*([+-]?[\d\.]+)%\s*(?:[,|\/]\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/);if(t){var n=parseFloat(t[4]);return[(parseFloat(t[1])%360+360)%360,c(parseFloat(t[2]),0,100),c(parseFloat(t[3]),0,100),c(isNaN(n)?1:n,0,1)]}return null},s.get.hwb=function(e){if(!e)return null;var t=e.match(/^hwb\(\s*([+-]?\d{0,3}(?:\.\d+)?)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/);if(t){var n=parseFloat(t[4]);return[(parseFloat(t[1])%360+360)%360,c(parseFloat(t[2]),0,100),c(parseFloat(t[3]),0,100),c(isNaN(n)?1:n,0,1)]}return null},s.to.hex=function(){var e=o(arguments);return"#"+u(e[0])+u(e[1])+u(e[2])+(e[3]<1?u(Math.round(255*e[3])):"")},s.to.rgb=function(){var e=o(arguments);return e.length<4||1===e[3]?"rgb("+Math.round(e[0])+", "+Math.round(e[1])+", "+Math.round(e[2])+")":"rgba("+Math.round(e[0])+", "+Math.round(e[1])+", "+Math.round(e[2])+", "+e[3]+")"},s.to.rgb.percent=function(){var e=o(arguments),t=Math.round(e[0]/255*100),n=Math.round(e[1]/255*100),r=Math.round(e[2]/255*100);return e.length<4||1===e[3]?"rgb("+t+"%, "+n+"%, "+r+"%)":"rgba("+t+"%, "+n+"%, "+r+"%, "+e[3]+")"},s.to.hsl=function(){var e=o(arguments);return e.length<4||1===e[3]?"hsl("+e[0]+", "+e[1]+"%, "+e[2]+"%)":"hsla("+e[0]+", "+e[1]+"%, "+e[2]+"%, "+e[3]+")"},s.to.hwb=function(){var e=o(arguments),t="";return e.length>=4&&1!==e[3]&&(t=", "+e[3]),"hwb("+e[0]+", "+e[1]+"%, "+e[2]+"%"+t+")"},s.to.keyword=function(e){return l[e.slice(0,3)]}},99025:function(e,t,n){const r=n(53203),o=n(52830),a=["keyword","gray","hex"],l={};for(const e of Object.keys(o))l[[...o[e].labels].sort().join("")]=e;const i={};function s(e,t){if(!(this instanceof s))return new s(e,t);if(t&&t in a&&(t=null),t&&!(t in o))throw new Error("Unknown model: "+t);let n,c;if(null==e)this.model="rgb",this.color=[0,0,0],this.valpha=1;else if(e instanceof s)this.model=e.model,this.color=[...e.color],this.valpha=e.valpha;else if("string"==typeof e){const t=r.get(e);if(null===t)throw new Error("Unable to parse color from string: "+e);this.model=t.model,c=o[this.model].channels,this.color=t.value.slice(0,c),this.valpha="number"==typeof t.value[c]?t.value[c]:1}else if(e.length>0){this.model=t||"rgb",c=o[this.model].channels;const n=Array.prototype.slice.call(e,0,c);this.color=m(n,c),this.valpha="number"==typeof e[c]?e[c]:1}else if("number"==typeof e)this.model="rgb",this.color=[e>>16&255,e>>8&255,255&e],this.valpha=1;else{this.valpha=1;const t=Object.keys(e);"alpha"in e&&(t.splice(t.indexOf("alpha"),1),this.valpha="number"==typeof e.alpha?e.alpha:0);const r=t.sort().join("");if(!(r in l))throw new Error("Unable to parse color from object: "+JSON.stringify(e));this.model=l[r];const{labels:a}=o[this.model],i=[];for(n=0;n<a.length;n++)i.push(e[a[n]]);this.color=m(i)}if(i[this.model])for(c=o[this.model].channels,n=0;n<c;n++){const e=i[this.model][n];e&&(this.color[n]=e(this.color[n]))}this.valpha=Math.max(0,Math.min(1,this.valpha)),Object.freeze&&Object.freeze(this)}s.prototype={toString(){return this.string()},toJSON(){return this[this.model]()},string(e){let t=this.model in r.to?this:this.rgb();t=t.round("number"==typeof e?e:1);const n=1===t.valpha?t.color:[...t.color,this.valpha];return r.to[t.model](n)},percentString(e){const t=this.rgb().round("number"==typeof e?e:1),n=1===t.valpha?t.color:[...t.color,this.valpha];return r.to.rgb.percent(n)},array(){return 1===this.valpha?[...this.color]:[...this.color,this.valpha]},object(){const e={},{channels:t}=o[this.model],{labels:n}=o[this.model];for(let r=0;r<t;r++)e[n[r]]=this.color[r];return 1!==this.valpha&&(e.alpha=this.valpha),e},unitArray(){const e=this.rgb().color;return e[0]/=255,e[1]/=255,e[2]/=255,1!==this.valpha&&e.push(this.valpha),e},unitObject(){const e=this.rgb().object();return e.r/=255,e.g/=255,e.b/=255,1!==this.valpha&&(e.alpha=this.valpha),e},round(e){return e=Math.max(e||0,0),new s([...this.color.map(c(e)),this.valpha],this.model)},alpha(e){return void 0!==e?new s([...this.color,Math.max(0,Math.min(1,e))],this.model):this.valpha},red:u("rgb",0,d(255)),green:u("rgb",1,d(255)),blue:u("rgb",2,d(255)),hue:u(["hsl","hsv","hsl","hwb","hcg"],0,(e=>(e%360+360)%360)),saturationl:u("hsl",1,d(100)),lightness:u("hsl",2,d(100)),saturationv:u("hsv",1,d(100)),value:u("hsv",2,d(100)),chroma:u("hcg",1,d(100)),gray:u("hcg",2,d(100)),white:u("hwb",1,d(100)),wblack:u("hwb",2,d(100)),cyan:u("cmyk",0,d(100)),magenta:u("cmyk",1,d(100)),yellow:u("cmyk",2,d(100)),black:u("cmyk",3,d(100)),x:u("xyz",0,d(95.047)),y:u("xyz",1,d(100)),z:u("xyz",2,d(108.833)),l:u("lab",0,d(100)),a:u("lab",1),b:u("lab",2),keyword(e){return void 0!==e?new s(e):o[this.model].keyword(this.color)},hex(e){return void 0!==e?new s(e):r.to.hex(this.rgb().round().color)},hexa(e){if(void 0!==e)return new s(e);const t=this.rgb().round().color;let n=Math.round(255*this.valpha).toString(16).toUpperCase();return 1===n.length&&(n="0"+n),r.to.hex(t)+n},rgbNumber(){const e=this.rgb().color;return(255&e[0])<<16|(255&e[1])<<8|255&e[2]},luminosity(){const e=this.rgb().color,t=[];for(const[n,r]of e.entries()){const e=r/255;t[n]=e<=.04045?e/12.92:((e+.055)/1.055)**2.4}return.2126*t[0]+.7152*t[1]+.0722*t[2]},contrast(e){const t=this.luminosity(),n=e.luminosity();return t>n?(t+.05)/(n+.05):(n+.05)/(t+.05)},level(e){const t=this.contrast(e);return t>=7?"AAA":t>=4.5?"AA":""},isDark(){const e=this.rgb().color;return(2126*e[0]+7152*e[1]+722*e[2])/1e4<128},isLight(){return!this.isDark()},negate(){const e=this.rgb();for(let t=0;t<3;t++)e.color[t]=255-e.color[t];return e},lighten(e){const t=this.hsl();return t.color[2]+=t.color[2]*e,t},darken(e){const t=this.hsl();return t.color[2]-=t.color[2]*e,t},saturate(e){const t=this.hsl();return t.color[1]+=t.color[1]*e,t},desaturate(e){const t=this.hsl();return t.color[1]-=t.color[1]*e,t},whiten(e){const t=this.hwb();return t.color[1]+=t.color[1]*e,t},blacken(e){const t=this.hwb();return t.color[2]+=t.color[2]*e,t},grayscale(){const e=this.rgb().color,t=.3*e[0]+.59*e[1]+.11*e[2];return s.rgb(t,t,t)},fade(e){return this.alpha(this.valpha-this.valpha*e)},opaquer(e){return this.alpha(this.valpha+this.valpha*e)},rotate(e){const t=this.hsl();let n=t.color[0];return n=(n+e)%360,n=n<0?360+n:n,t.color[0]=n,t},mix(e,t){if(!e||!e.rgb)throw new Error('Argument to "mix" was not a Color instance, but rather an instance of '+typeof e);const n=e.rgb(),r=this.rgb(),o=void 0===t?.5:t,a=2*o-1,l=n.alpha()-r.alpha(),i=((a*l==-1?a:(a+l)/(1+a*l))+1)/2,c=1-i;return s.rgb(i*n.red()+c*r.red(),i*n.green()+c*r.green(),i*n.blue()+c*r.blue(),n.alpha()*o+r.alpha()*(1-o))}};for(const e of Object.keys(o)){if(a.includes(e))continue;const{channels:t}=o[e];s.prototype[e]=function(...t){return this.model===e?new s(this):t.length>0?new s(t,e):new s([...(n=o[this.model][e].raw(this.color),Array.isArray(n)?n:[n]),this.valpha],e);var n},s[e]=function(...n){let r=n[0];return"number"==typeof r&&(r=m(n,t)),new s(r,e)}}function c(e){return function(t){return function(e,t){return Number(e.toFixed(t))}(t,e)}}function u(e,t,n){e=Array.isArray(e)?e:[e];for(const r of e)(i[r]||(i[r]=[]))[t]=n;return e=e[0],function(r){let o;return void 0!==r?(n&&(r=n(r)),o=this[e](),o.color[t]=r,o):(o=this[e]().color[t],n&&(o=n(o)),o)}}function d(e){return function(t){return Math.max(0,Math.min(e,t))}}function m(e,t){for(let n=0;n<t;n++)"number"!=typeof e[n]&&(e[n]=0);return e}e.exports=s},51636:function(e,t,n){const r=n(52319),o={};for(const e of Object.keys(r))o[r[e]]=e;const a={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};e.exports=a;for(const e of Object.keys(a)){if(!("channels"in a[e]))throw new Error("missing channels property: "+e);if(!("labels"in a[e]))throw new Error("missing channel labels property: "+e);if(a[e].labels.length!==a[e].channels)throw new Error("channel and label counts mismatch: "+e);const{channels:t,labels:n}=a[e];delete a[e].channels,delete a[e].labels,Object.defineProperty(a[e],"channels",{value:t}),Object.defineProperty(a[e],"labels",{value:n})}a.rgb.hsl=function(e){const t=e[0]/255,n=e[1]/255,r=e[2]/255,o=Math.min(t,n,r),a=Math.max(t,n,r),l=a-o;let i,s;a===o?i=0:t===a?i=(n-r)/l:n===a?i=2+(r-t)/l:r===a&&(i=4+(t-n)/l),i=Math.min(60*i,360),i<0&&(i+=360);const c=(o+a)/2;return s=a===o?0:c<=.5?l/(a+o):l/(2-a-o),[i,100*s,100*c]},a.rgb.hsv=function(e){let t,n,r,o,a;const l=e[0]/255,i=e[1]/255,s=e[2]/255,c=Math.max(l,i,s),u=c-Math.min(l,i,s),d=function(e){return(c-e)/6/u+.5};return 0===u?(o=0,a=0):(a=u/c,t=d(l),n=d(i),r=d(s),l===c?o=r-n:i===c?o=1/3+t-r:s===c&&(o=2/3+n-t),o<0?o+=1:o>1&&(o-=1)),[360*o,100*a,100*c]},a.rgb.hwb=function(e){const t=e[0],n=e[1];let r=e[2];const o=a.rgb.hsl(e)[0],l=1/255*Math.min(t,Math.min(n,r));return r=1-1/255*Math.max(t,Math.max(n,r)),[o,100*l,100*r]},a.rgb.cmyk=function(e){const t=e[0]/255,n=e[1]/255,r=e[2]/255,o=Math.min(1-t,1-n,1-r);return[100*((1-t-o)/(1-o)||0),100*((1-n-o)/(1-o)||0),100*((1-r-o)/(1-o)||0),100*o]},a.rgb.keyword=function(e){const t=o[e];if(t)return t;let n,a=1/0;for(const t of Object.keys(r)){const o=r[t],s=(i=o,((l=e)[0]-i[0])**2+(l[1]-i[1])**2+(l[2]-i[2])**2);s<a&&(a=s,n=t)}var l,i;return n},a.keyword.rgb=function(e){return r[e]},a.rgb.xyz=function(e){let t=e[0]/255,n=e[1]/255,r=e[2]/255;t=t>.04045?((t+.055)/1.055)**2.4:t/12.92,n=n>.04045?((n+.055)/1.055)**2.4:n/12.92,r=r>.04045?((r+.055)/1.055)**2.4:r/12.92;return[100*(.4124*t+.3576*n+.1805*r),100*(.2126*t+.7152*n+.0722*r),100*(.0193*t+.1192*n+.9505*r)]},a.rgb.lab=function(e){const t=a.rgb.xyz(e);let n=t[0],r=t[1],o=t[2];n/=95.047,r/=100,o/=108.883,n=n>.008856?n**(1/3):7.787*n+16/116,r=r>.008856?r**(1/3):7.787*r+16/116,o=o>.008856?o**(1/3):7.787*o+16/116;return[116*r-16,500*(n-r),200*(r-o)]},a.hsl.rgb=function(e){const t=e[0]/360,n=e[1]/100,r=e[2]/100;let o,a,l;if(0===n)return l=255*r,[l,l,l];o=r<.5?r*(1+n):r+n-r*n;const i=2*r-o,s=[0,0,0];for(let e=0;e<3;e++)a=t+1/3*-(e-1),a<0&&a++,a>1&&a--,l=6*a<1?i+6*(o-i)*a:2*a<1?o:3*a<2?i+(o-i)*(2/3-a)*6:i,s[e]=255*l;return s},a.hsl.hsv=function(e){const t=e[0];let n=e[1]/100,r=e[2]/100,o=n;const a=Math.max(r,.01);r*=2,n*=r<=1?r:2-r,o*=a<=1?a:2-a;return[t,100*(0===r?2*o/(a+o):2*n/(r+n)),100*((r+n)/2)]},a.hsv.rgb=function(e){const t=e[0]/60,n=e[1]/100;let r=e[2]/100;const o=Math.floor(t)%6,a=t-Math.floor(t),l=255*r*(1-n),i=255*r*(1-n*a),s=255*r*(1-n*(1-a));switch(r*=255,o){case 0:return[r,s,l];case 1:return[i,r,l];case 2:return[l,r,s];case 3:return[l,i,r];case 4:return[s,l,r];case 5:return[r,l,i]}},a.hsv.hsl=function(e){const t=e[0],n=e[1]/100,r=e[2]/100,o=Math.max(r,.01);let a,l;l=(2-n)*r;const i=(2-n)*o;return a=n*o,a/=i<=1?i:2-i,a=a||0,l/=2,[t,100*a,100*l]},a.hwb.rgb=function(e){const t=e[0]/360;let n=e[1]/100,r=e[2]/100;const o=n+r;let a;o>1&&(n/=o,r/=o);const l=Math.floor(6*t),i=1-r;a=6*t-l,1&l&&(a=1-a);const s=n+a*(i-n);let c,u,d;switch(l){default:case 6:case 0:c=i,u=s,d=n;break;case 1:c=s,u=i,d=n;break;case 2:c=n,u=i,d=s;break;case 3:c=n,u=s,d=i;break;case 4:c=s,u=n,d=i;break;case 5:c=i,u=n,d=s}return[255*c,255*u,255*d]},a.cmyk.rgb=function(e){const t=e[0]/100,n=e[1]/100,r=e[2]/100,o=e[3]/100;return[255*(1-Math.min(1,t*(1-o)+o)),255*(1-Math.min(1,n*(1-o)+o)),255*(1-Math.min(1,r*(1-o)+o))]},a.xyz.rgb=function(e){const t=e[0]/100,n=e[1]/100,r=e[2]/100;let o,a,l;return o=3.2406*t+-1.5372*n+-.4986*r,a=-.9689*t+1.8758*n+.0415*r,l=.0557*t+-.204*n+1.057*r,o=o>.0031308?1.055*o**(1/2.4)-.055:12.92*o,a=a>.0031308?1.055*a**(1/2.4)-.055:12.92*a,l=l>.0031308?1.055*l**(1/2.4)-.055:12.92*l,o=Math.min(Math.max(0,o),1),a=Math.min(Math.max(0,a),1),l=Math.min(Math.max(0,l),1),[255*o,255*a,255*l]},a.xyz.lab=function(e){let t=e[0],n=e[1],r=e[2];t/=95.047,n/=100,r/=108.883,t=t>.008856?t**(1/3):7.787*t+16/116,n=n>.008856?n**(1/3):7.787*n+16/116,r=r>.008856?r**(1/3):7.787*r+16/116;return[116*n-16,500*(t-n),200*(n-r)]},a.lab.xyz=function(e){let t,n,r;n=(e[0]+16)/116,t=e[1]/500+n,r=n-e[2]/200;const o=n**3,a=t**3,l=r**3;return n=o>.008856?o:(n-16/116)/7.787,t=a>.008856?a:(t-16/116)/7.787,r=l>.008856?l:(r-16/116)/7.787,t*=95.047,n*=100,r*=108.883,[t,n,r]},a.lab.lch=function(e){const t=e[0],n=e[1],r=e[2];let o;o=360*Math.atan2(r,n)/2/Math.PI,o<0&&(o+=360);return[t,Math.sqrt(n*n+r*r),o]},a.lch.lab=function(e){const t=e[0],n=e[1],r=e[2]/360*2*Math.PI;return[t,n*Math.cos(r),n*Math.sin(r)]},a.rgb.ansi16=function(e,t=null){const[n,r,o]=e;let l=null===t?a.rgb.hsv(e)[2]:t;if(l=Math.round(l/50),0===l)return 30;let i=30+(Math.round(o/255)<<2|Math.round(r/255)<<1|Math.round(n/255));return 2===l&&(i+=60),i},a.hsv.ansi16=function(e){return a.rgb.ansi16(a.hsv.rgb(e),e[2])},a.rgb.ansi256=function(e){const t=e[0],n=e[1],r=e[2];if(t===n&&n===r)return t<8?16:t>248?231:Math.round((t-8)/247*24)+232;return 16+36*Math.round(t/255*5)+6*Math.round(n/255*5)+Math.round(r/255*5)},a.ansi16.rgb=function(e){let t=e%10;if(0===t||7===t)return e>50&&(t+=3.5),t=t/10.5*255,[t,t,t];const n=.5*(1+~~(e>50));return[(1&t)*n*255,(t>>1&1)*n*255,(t>>2&1)*n*255]},a.ansi256.rgb=function(e){if(e>=232){const t=10*(e-232)+8;return[t,t,t]}let t;e-=16;return[Math.floor(e/36)/5*255,Math.floor((t=e%36)/6)/5*255,t%6/5*255]},a.rgb.hex=function(e){const t=(((255&Math.round(e[0]))<<16)+((255&Math.round(e[1]))<<8)+(255&Math.round(e[2]))).toString(16).toUpperCase();return"000000".substring(t.length)+t},a.hex.rgb=function(e){const t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t)return[0,0,0];let n=t[0];3===t[0].length&&(n=n.split("").map((e=>e+e)).join(""));const r=parseInt(n,16);return[r>>16&255,r>>8&255,255&r]},a.rgb.hcg=function(e){const t=e[0]/255,n=e[1]/255,r=e[2]/255,o=Math.max(Math.max(t,n),r),a=Math.min(Math.min(t,n),r),l=o-a;let i,s;return i=l<1?a/(1-l):0,s=l<=0?0:o===t?(n-r)/l%6:o===n?2+(r-t)/l:4+(t-n)/l,s/=6,s%=1,[360*s,100*l,100*i]},a.hsl.hcg=function(e){const t=e[1]/100,n=e[2]/100,r=n<.5?2*t*n:2*t*(1-n);let o=0;return r<1&&(o=(n-.5*r)/(1-r)),[e[0],100*r,100*o]},a.hsv.hcg=function(e){const t=e[1]/100,n=e[2]/100,r=t*n;let o=0;return r<1&&(o=(n-r)/(1-r)),[e[0],100*r,100*o]},a.hcg.rgb=function(e){const t=e[0]/360,n=e[1]/100,r=e[2]/100;if(0===n)return[255*r,255*r,255*r];const o=[0,0,0],a=t%1*6,l=a%1,i=1-l;let s=0;switch(Math.floor(a)){case 0:o[0]=1,o[1]=l,o[2]=0;break;case 1:o[0]=i,o[1]=1,o[2]=0;break;case 2:o[0]=0,o[1]=1,o[2]=l;break;case 3:o[0]=0,o[1]=i,o[2]=1;break;case 4:o[0]=l,o[1]=0,o[2]=1;break;default:o[0]=1,o[1]=0,o[2]=i}return s=(1-n)*r,[255*(n*o[0]+s),255*(n*o[1]+s),255*(n*o[2]+s)]},a.hcg.hsv=function(e){const t=e[1]/100,n=t+e[2]/100*(1-t);let r=0;return n>0&&(r=t/n),[e[0],100*r,100*n]},a.hcg.hsl=function(e){const t=e[1]/100,n=e[2]/100*(1-t)+.5*t;let r=0;return n>0&&n<.5?r=t/(2*n):n>=.5&&n<1&&(r=t/(2*(1-n))),[e[0],100*r,100*n]},a.hcg.hwb=function(e){const t=e[1]/100,n=t+e[2]/100*(1-t);return[e[0],100*(n-t),100*(1-n)]},a.hwb.hcg=function(e){const t=e[1]/100,n=1-e[2]/100,r=n-t;let o=0;return r<1&&(o=(n-r)/(1-r)),[e[0],100*r,100*o]},a.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]},a.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]},a.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]},a.gray.hsl=function(e){return[0,0,e[0]]},a.gray.hsv=a.gray.hsl,a.gray.hwb=function(e){return[0,100,e[0]]},a.gray.cmyk=function(e){return[0,0,0,e[0]]},a.gray.lab=function(e){return[e[0],0,0]},a.gray.hex=function(e){const t=255&Math.round(e[0]/100*255),n=((t<<16)+(t<<8)+t).toString(16).toUpperCase();return"000000".substring(n.length)+n},a.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]}},52830:function(e,t,n){const r=n(51636),o=n(12036),a={};Object.keys(r).forEach((e=>{a[e]={},Object.defineProperty(a[e],"channels",{value:r[e].channels}),Object.defineProperty(a[e],"labels",{value:r[e].labels});const t=o(e);Object.keys(t).forEach((n=>{const r=t[n];a[e][n]=function(e){const t=function(...t){const n=t[0];if(null==n)return n;n.length>1&&(t=n);const r=e(t);if("object"==typeof r)for(let e=r.length,t=0;t<e;t++)r[t]=Math.round(r[t]);return r};return"conversion"in e&&(t.conversion=e.conversion),t}(r),a[e][n].raw=function(e){const t=function(...t){const n=t[0];return null==n?n:(n.length>1&&(t=n),e(t))};return"conversion"in e&&(t.conversion=e.conversion),t}(r)}))})),e.exports=a},12036:function(e,t,n){const r=n(51636);function o(e){const t=function(){const e={},t=Object.keys(r);for(let n=t.length,r=0;r<n;r++)e[t[r]]={distance:-1,parent:null};return e}(),n=[e];for(t[e].distance=0;n.length;){const e=n.pop(),o=Object.keys(r[e]);for(let r=o.length,a=0;a<r;a++){const r=o[a],l=t[r];-1===l.distance&&(l.distance=t[e].distance+1,l.parent=e,n.unshift(r))}}return t}function a(e,t){return function(n){return t(e(n))}}function l(e,t){const n=[t[e].parent,e];let o=r[t[e].parent][e],l=t[e].parent;for(;t[l].parent;)n.unshift(t[l].parent),o=a(r[t[l].parent][l],o),l=t[l].parent;return o.conversion=n,o}e.exports=function(e){const t=o(e),n={},r=Object.keys(t);for(let e=r.length,o=0;o<e;o++){const e=r[o];null!==t[e].parent&&(n[e]=l(e,t))}return n}},99429:function(e,t,n){"use strict";var r=n(15631),o=Array.prototype.concat,a=Array.prototype.slice,l=e.exports=function(e){for(var t=[],n=0,l=e.length;n<l;n++){var i=e[n];r(i)?t=o.call(t,a.call(i)):t.push(i)}return t};l.wrap=function(e){return function(){return e(l(arguments))}}},15631:function(e){e.exports=function(e){return!(!e||"string"==typeof e)&&(e instanceof Array||Array.isArray(e)||e.length>=0&&(e.splice instanceof Function||Object.getOwnPropertyDescriptor(e,e.length-1)&&"String"!==e.constructor.name))}},18805:function(e,t,n){"use strict";t.d=void 0;var r=n(69244);Object.defineProperty(t,"d",{enumerable:!0,get:function(){return r.useLatest}})},69244:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useLatest=void 0;const r=n(5329);t.useLatest=e=>{const t=(0,r.useRef)(e);return t.current=e,t}},5329:function(t){"use strict";t.exports=e},95561:function(e){"use strict";e.exports=t},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},40126:function(e,t,n){var r=n(93494);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},93494:function(e){function t(n,r){return e.exports=t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n,r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},95685:function(e,t){var n;
/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=l(e,a(n)))}return e}function a(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=l(t,n));return t}function l(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()}},r={};function o(e){var t=r[e];if(void 0!==t)return t.exports;var a=r[e]={id:e,exports:{}};return n[e](a,a.exports,o),a.exports}o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,{a:t}),t},o.d=function(e,t){for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var a={};return function(){"use strict";o.r(a),o.d(a,{components:function(){return Jl}});var e=o(448),t=o.n(e),n=o(5329),r=o.n(n);const l="showOverlay",i="hideOverlay",s="none",c={Enter:"enter",EnterActive:"enterActive",EnterDone:"enterDone",Exit:"exit",ExitActive:"exitActive",ExitDone:"exitDone"},u=(e,t)=>{const r=(0,n.useRef)(null),o=(0,n.useRef)(null),[a,l]=(0,n.useState)(t),i=()=>{r.current=null,o.current=null};return(0,n.useEffect)((()=>{const{name:t}=a,{duration:n}=e[t],l=a.phase===c.Enter||a.phase===c.Exit,i=a.phase===c.EnterActive||a.phase===c.ExitActive;l?window.requestAnimationFrame((()=>{null==r.current||r.current(),r.current=null})):i&&setTimeout((()=>{null==o.current||o.current(),o.current=null}),n)}),[a.phase]),{animationState:a,initEnterAnimation:(e,t)=>{if(i(),e===s)return null==t?void 0:t();l({name:e,phase:c.Enter}),r.current=()=>{l({name:e,phase:c.EnterActive})},o.current=()=>{l({name:e,phase:c.EnterDone}),null==t||t()}},initExitAnimation:(e,t)=>{if(i(),e===s)return null==t?void 0:t();l({name:e,phase:c.Exit}),r.current=()=>{l({name:e,phase:c.ExitActive})},o.current=()=>{l({name:e,phase:c.ExitDone}),null==t||t()}}}},d="horizontal",m="vertical",p="alwaysOpen",b=s,h="calm",f="underline",g="wash",v="bullet",y="wave",w="bounce",C="tint",k="textWash",x="wobble",E="brackets",S="float",M="shape",I="expand",N="circle",_="shadow",L="lineRise",O="overline",A="centerLine",D="skew",B="point",R="blur",T=s,P="revealFromTop",z="fadeIn",H=s,F="revealFromRight",V="fadeIn",W="menu-root",j={[b]:{name:b,duration:0},[h]:{name:h,duration:0},[f]:{name:f,duration:300},[g]:{name:g,duration:400},[v]:{name:v,duration:300},[y]:{name:y,duration:400},[N]:{name:N,duration:400},[w]:{name:w,duration:400},[C]:{name:C,duration:400},[k]:{name:k,duration:400},[x]:{name:x,duration:400},[E]:{name:E,duration:400},[S]:{name:S,duration:400},[M]:{name:M,duration:400},[I]:{name:I,duration:400},[_]:{name:_,duration:400},[L]:{name:L,duration:400},[O]:{name:O,duration:400},[A]:{name:A,duration:400},[D]:{name:D,duration:400},[B]:{name:B,duration:400},[R]:{name:R,duration:400}},Z={[T]:{name:T,duration:0},[P]:{name:P,duration:400},[z]:{name:z,duration:400}},U={[H]:{name:H,duration:0},[F]:{name:F,duration:400},[V]:{name:V,duration:400}},K={[b]:{menuItemAnimationName:b,dropdownAnimationName:T,hamburgerMenuAnimationName:H},[f]:{menuItemAnimationName:f,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[g]:{menuItemAnimationName:g,dropdownAnimationName:z,hamburgerMenuAnimationName:V},[v]:{menuItemAnimationName:v,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[y]:{menuItemAnimationName:y,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[N]:{menuItemAnimationName:N,dropdownAnimationName:z,hamburgerMenuAnimationName:V},[w]:{menuItemAnimationName:w,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[C]:{menuItemAnimationName:C,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[k]:{menuItemAnimationName:k,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[x]:{menuItemAnimationName:x,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[E]:{menuItemAnimationName:E,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[S]:{menuItemAnimationName:S,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[M]:{menuItemAnimationName:M,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[I]:{menuItemAnimationName:I,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[_]:{menuItemAnimationName:_,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[L]:{menuItemAnimationName:L,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[O]:{menuItemAnimationName:O,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[A]:{menuItemAnimationName:A,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[D]:{menuItemAnimationName:D,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[B]:{menuItemAnimationName:B,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[R]:{menuItemAnimationName:R,dropdownAnimationName:P,hamburgerMenuAnimationName:F},[h]:{menuItemAnimationName:h,dropdownAnimationName:P,hamburgerMenuAnimationName:V}},q={name:b},$={name:T},X={name:H},G="navbar",J="dropdown-container",Q="dropdown-icon",Y="label",ee="dropdown-item",te="dropdown-item-label",ne="dropdown-subitem",re="dropdown-subitem-label",oe="menu-item",ae="menu-item-content",le="menu-item-link",ie="hamburger-overlay",se="scroll-backward-button",ce="scroll-forward-button",ue=(0,n.createContext)({isMenuOpen:void 0,isMenuContainerFullscreen:!1,shouldFocus:!0,setIsMenuOpen:()=>{},setIsMenuContainerFullscreen:()=>{},menuContainerRef:void 0,animationState:X}),de=()=>(0,n.useContext)(ue),me=e=>{let{children:t,compPreviewState:o,shouldFocus:a,isMenuOpen:s,setIsMenuOpen:c,animationState:u}=e;const d=(0,n.useRef)(null),[m,p]=(0,n.useState)(!1);(0,n.useEffect)((()=>{o===l?c(!0):o===i&&c(!1)}),[o,c]);const b=(0,n.useMemo)((()=>({isMenuOpen:s,setIsMenuOpen:c,isMenuContainerFullscreen:m,setIsMenuContainerFullscreen:p,shouldFocus:a,menuContainerRef:d,animationState:u})),[s,c,m,p,a,d,u]);return r().createElement(ue.Provider,{value:b},t)};var pe={root:"hamburger-close-button",buttonLabel:"hamburger-close-button__label",buttonIcon:"hamburger-close-button__icon"},be=o(95685),he=o.n(be);const fe=(e,o)=>{let{id:a,a11y:l={},className:i,classes:s,StylableButton:c,onClick:u,...d}=e;const{isMenuOpen:m,setIsMenuOpen:p,menuContainerRef:b}=de(),[h,f]=(0,n.useState)([]);(0,n.useEffect)((()=>{m||h.forEach((e=>{e.style.display=""}))}),[m,h]);return r().createElement(c,t()({},d,{id:a,className:he()(s.hamburgerCloseButtonRoot,s.stylableButtonRoot,i),ref:o,a11y:l,onClick:e=>{const t=(e=>{var t;return null!=e&&e.current?Array.from(null==e||null==(t=e.current)?void 0:t.querySelectorAll("*")).filter((e=>{const{transitionProperty:t,transitionDuration:n,transitionDelay:r}=window.getComputedStyle(e);return!("all"!==t&&"visibility"!==t||"0s"===r&&"0s"===n)})):[]})(b);f(t),t.forEach((e=>{e.style.display="none"})),p(!1),null==u||u(e)},semanticClassNames:pe}))};var ge=r().forwardRef(fe);const ve=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)),ye=e=>({useComponentProps:(t,n,r)=>{const o=(e=>({...e,updateStyles:t=>{const n=Object.entries(t).reduce(((e,[t,n])=>{return{...e,[(r=t,r.startsWith("--")?t:ve(t))]:void 0===n?null:n};var r}),{});e.updateStyles(n)}}))(r);return e({mapperProps:t,stateValues:n,controllerUtils:o})}}),we=e=>"linkPopupId"in e,Ce=(e,t)=>{if(we(e))return e.linkPopupId;{const{pagesMap:n,mainPageId:r}=t||{};if(!n)return;const o=new URL(e.href??"");let a=Object.values(n).find((({pageUriSEO:e})=>!!e&&o.pathname?.includes(e)));return a||(a=r?n[r]:void 0),a?.pageId}},ke=e=>{if(void 0!==e)return null===e?"None":e.type},xe=(e,t)=>{if(!e?.type)return;const{type:n}=e;switch(n){case"AnchorLink":return e.anchorDataId;case"DocumentLink":return e.docInfo?.name;case"PageLink":const n=Ce(e,t);return n&&t?.pagesMap?.[n]?.title;default:return e.href}},Ee=(e,t,n)=>{const{link:r,value:o,details:a,actionName:l,elementType:i,trackClicksAnalytics:s,pagesMetadata:c,...u}=t;if(!s)return;const d=c&&{...c,pagesMap:window.viewerModel?.siteFeaturesConfigs?.router?.pagesMap},m=((e,t)=>{if(!e?.type)return;const{type:n}=e;switch(n){case"AnchorLink":return(e=>"anchorDataId"in e&&("SCROLL_TO_TOP"===e.anchorDataId||"SCROLL_TO_BOTTOM"===e.anchorDataId))(e)?void 0:{id:e.anchorDataId};case"DocumentLink":return{id:e.docInfo?.docId};case"PageLink":return{id:Ce(e,t),isLightbox:we(e)};default:return}})(r,d),p=a||m?JSON.stringify({...m,...a}):void 0;e({src:76,evid:1113,...{...u,bl:navigator.language,url:window.location.href,details:p,elementType:i??"Unknown",actionName:l??ke(r),value:o??xe(r,d)}},{endpoint:"pa",...n})};var Se;!function(e){e.Text="Text",e.Menu="Menu",e.Image="Image",e.Input="Input",e.Login="Login",e.Button="Button",e.Social="Social",e.Gallery="Gallery",e.Community="Community",e.Decorative="Decorative",e.MenuAndSearch="MenuAndSearch",e.MenuAndAnchor="MenuAndAnchor"}(Se||(Se={}));var Me=ye((e=>{let{mapperProps:t,stateValues:n}=e;const{trackClicksAnalytics:r,compId:o,language:a,mainPageId:l,...i}=t,{reportBi:s,reducedMotion:c}=n;return{...i,reportBiOnClick:e=>{const{fullNameCompType:t,label:n,link:c,isDisabled:u}=i;Ee(s,{link:c,language:a,trackClicksAnalytics:r,elementTitle:null!=n?n:"",elementType:t,pagesMetadata:{mainPageId:l},elementGroup:Se.Button,details:{isDisabled:null!=u&&u},element_id:null!=o?o:e.currentTarget.id})},reducedMotion:c}})),Ie=Me,Ne=o(91003),_e=o(27232);const Le="wixui-",Oe=(e,...t)=>{const n=[];return e&&n.push(`${Le}${e}`),t.forEach((e=>{e&&(n.push(`${Le}${e}`),n.push(e))})),n.join(" ")},Ae=13,De=27;function Be(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}const Re=Be(32),Te=Be(Ae),Pe=e=>{Te(e),Re(e)},ze=(Be(De),["aria-id","aria-metadata","aria-type"]),He=(e,t)=>Object.entries(e).reduce(((e,[n,r])=>(t.includes(n)||(e[n]=r),e)),{}),Fe=e=>{const{role:t,tabIndex:n,tabindex:r,screenReader:o,lang:a,ariaAttributes:l={}}=e,i=Object.entries(l).reduce(((e,[t,n])=>({...e,[`aria-${t}`.toLowerCase()]:n})),{});return{role:t,tabIndex:n??r,screenReader:o,ariaAttributes:He(i,ze),lang:a}},Ve=e=>{const t=Fe(e);return{...t.ariaAttributes,tabIndex:t.tabIndex,screenReader:t.screenReader,lang:t.lang,role:t.role}},We=({reportBiOnClick:e,onClick:t})=>(0,n.useCallback)((n=>{e?.(n),t?.(n)}),[e,t]),je=(e,t=0,{leading:n=!1,trailing:r=!0}={})=>{let o=null;return function(...a){n&&null===o&&e.apply(this,a),o&&clearTimeout(o),o=r&&n&&!o?setTimeout((()=>{o=null}),t):setTimeout((()=>{r&&e.apply(this,a),o=null}),t)}},Ze=(e,t=0)=>{let n,r=!1;return function o(...a){r?n=a:(e.apply(this,a),r=!0,setTimeout((()=>{r=!1,n&&(o.apply(this,n),n=null)}),t))}},Ue=e=>!e||0===Object.keys(e).length&&e.constructor===Object,Ke=()=>/^((?!chrome|android).)*safari/i.test(navigator?.userAgent),qe=e=>Object.entries(e).reduce(((e,[t,n])=>(t.includes("data-")&&(e[t]=n),e)),{});const $e=(e,t)=>e?{"data-comp":t,"data-aid":t}:{},Xe={root:"linkElement"};var Ge=n.forwardRef(((e,t)=>{const{href:r,role:o,target:a,rel:l,className:i="",children:s,linkPopupId:c,anchorDataId:u,anchorCompId:d,tabIndex:m,dataTestId:p=Xe.root,title:b,onClick:h,onDoubleClick:f,onMouseEnter:g,onMouseLeave:v,onFocus:y,onFocusCapture:w,onBlurCapture:C,"aria-live":k,"aria-disabled":x,"aria-label":E,"aria-labelledby":S,"aria-pressed":M,"aria-expanded":I,"aria-describedby":N,"aria-haspopup":_,"aria-current":L,dataPreview:O,dataPart:A}=e,D=void 0!==e.activateByKey?e.activateByKey:(e=>e?"SpaceOrEnter":"Space")(c);let B;switch(D){case"Enter":B=Te;break;case"Space":B=Re;break;case"SpaceOrEnter":B=Pe;break;default:B=void 0}return void 0!==r||c?n.createElement("a",{...qe(e),"data-testid":p,"data-popupid":c,"data-anchor":u,"data-anchor-comp-id":d,"data-preview":O,"data-part":A,href:r||void 0,target:a,role:c?"button":o,rel:l,className:i,onKeyDown:B,"aria-live":k,"aria-disabled":x,"aria-label":E,"aria-labelledby":S,"aria-pressed":M,"aria-expanded":I,"aria-haspopup":_,"aria-describedby":N,"aria-current":L,title:b,onClick:h,onMouseEnter:g,onMouseLeave:v,onDoubleClick:f,onFocus:y,onFocusCapture:w,onBlurCapture:C,ref:t,tabIndex:c?0:m},s):n.createElement("div",{...qe(e),"data-testid":p,"data-preview":O,"data-part":A,className:i,tabIndex:m,"aria-label":E,"aria-labelledby":S,"aria-haspopup":_,"aria-disabled":x,"aria-expanded":I,title:b,role:o,onClick:h,onDoubleClick:f,onMouseEnter:g,onMouseLeave:v,ref:t},s)}));const Je=e=>Boolean(e&&(e.href||e.linkPopupId));let Qe=function(e){return e.HOVER="hover",e}({});const Ye="buttonContent",et="stylablebutton-label",tt="stylablebutton-icon",nt=(Object.keys({width:{type:"maxContent"}}),{left:"flex-start",right:"flex-end",center:"center","space-between":"space-between"}),rt={start:"flex-start",end:"flex-end",center:"center",justify:"space-between"},ot={"flex-start":"left","flex-end":"right",center:"center","space-between":"space-between"},at={"flex-start":"start","flex-end":"end",center:"center","space-between":"justify"};var lt={root:"button",buttonLabel:"button__label",buttonIcon:"button__icon"};var it=e=>{const{label:t,icon:n,classNames:o}=e;return r().createElement("span",{className:o.container},t&&r().createElement("span",{className:o.label,"data-testid":et},t),n&&r().createElement("span",{className:o.icon,"aria-hidden":"true","data-testid":tt},n))};const st=e=>n.createElement("span",{dangerouslySetInnerHTML:{__html:e||""}}),ct=(e,t)=>e?e.replace(/(id="|url\(#|href="#)([^"]+)(?=[")])/g,((e,n,r)=>""+n+(t+r))):e,ut=(e,t)=>["has",t,...e.split("has").slice(1)].join("");function dt(e){let{hover:t={},disabled:n={},...r}=e;return{...r,...Object.fromEntries([...Object.entries(t).map((e=>{let[t,n]=e;return[ut(t,"Hover"),n]})),...Object.entries(n).map((e=>{let[t,n]=e;return[ut(t,"Disabled"),n]}))])}}const mt={animatedSvg:"animatedSvg",animatedTagPath:"animatedPath",animateTagForward:"animateForward",animateTagBackward:"animateBackward"};let pt=function(e){return e.FORWARD="Forward",e.BACKWARD="Backward",e}({});const bt={[pt.FORWARD]:"data-animated-end-path",[pt.BACKWARD]:"data-animated-start-path"};var ht=(0,n.forwardRef)(((e,t)=>{let{svgContent:o,reducedMotion:a=!1}=e;const l=(0,n.useRef)(null);(0,n.useImperativeHandle)(t,(()=>({runAnimationForward:s,runAnimationBackward:c})));const i=(0,n.useCallback)((e=>{if(!l.current)return;if(!a){const t="animateTag"+e;l.current.querySelectorAll("[data-animate-id="+mt[t]+"]").forEach((e=>e.beginElement()))}l.current.querySelectorAll("path[data-animate-id="+mt.animatedTagPath+"]").forEach((t=>{const n=t.getAttribute("d"),r=t.getAttribute(bt[e]);if(r){const o=e===pt.FORWARD?pt.BACKWARD:pt.FORWARD;t.getAttribute(bt[o])||t.setAttribute(bt[o],n||""),t.setAttribute("d",r)}}))}),[a]),s=(0,n.useCallback)((()=>{i(pt.FORWARD)}),[i]),c=(0,n.useCallback)((()=>{i(pt.BACKWARD)}),[i]);return r().createElement("div",{ref:l,dangerouslySetInnerHTML:{__html:o}})}));const ft=(e,r)=>{const{id:o,link:a,type:l="button",svgString:i,isIconAnimated:s=!1,svgAnimatedIcon:c,label:u,isDisabled:d,className:m,isQaMode:p,fullNameCompType:b,reportBiOnClick:h,a11y:f,corvid:g,onDblClick:v,onMouseEnter:y,onMouseLeave:w,onFocus:C,onBlur:k,ariaAttributes:x,onClick:E,preventLinkNavigation:S,classNames:M,compPreviewState:I,reducedMotion:N,lang:_,direction:L}=e,O=Je(a),A=S&&O,D=!d&&E||A,{iconSvgString:B,iconCollapsed:R,iconAnimationTriggers:T=[Qe.HOVER]}=g||{},P=e.semanticClassNames||lt,z=n.useRef(null),H=(0,n.useRef)(!1),F=(0,n.useRef)(null);n.useImperativeHandle(r,(()=>({focus:()=>{var e;null==(e=z.current)||e.focus()},blur:()=>{var e;null==(e=z.current)||e.blur()},animateIconForward:()=>{V()},animateIconBackward:()=>{W()}})));const V=(0,n.useCallback)((()=>{var e;!d&&s&&(null==(e=F.current)||e.runAnimationForward())}),[d,s]),W=(0,n.useCallback)((()=>{var e;!d&&s&&(null==(e=F.current)||e.runAnimationBackward())}),[d,s]),j=n.useMemo((()=>{var e,t;return Ve({ariaAttributes:{...x,...f,label:null!=(e=null!=(t=null==x?void 0:x.label)?t:f.label)?e:u},tabindex:null==f?void 0:f.tabindex})}),[f,u,x]),Z=(0,n.useCallback)((e=>{T.includes(Qe.HOVER)&&V(),null==y||y(e)}),[T,V,y]),U=(0,n.useCallback)((e=>{T.includes(Qe.HOVER)&&W(),null==w||w(e)}),[T,W,w]),K=We({reportBiOnClick:h,onClick:D?e=>{A&&e.preventDefault(),!d&&(null==E||E(e))}:void 0}),q=n.useMemo((()=>((e,t,n)=>{let{onClick:r,onDblClick:o,onMouseEnter:a,onMouseLeave:l,onFocus:i,onBlur:s}=e;return{onClick:r,onMouseEnter:a,onMouseLeave:l,onKeyDown:t?Re:Te,onDoubleClick:!n&&o?o:void 0,onFocus:!n&&i?i:void 0,onBlur:!n&&s?s:void 0}})({onClick:K,onDblClick:v,onMouseLeave:U,onMouseEnter:Z,onFocus:C,onBlur:k},O,d)),[K,v,U,Z,C,k,O,d]),$=M.root;H.current=!1;const X=(0,n.useMemo)((()=>{if(!R&&null!==B){if(B)return H.current=!0,st(ct(B,o));if(i)return st(ct(i,o))}return null}),[R,B,o,i]),G=(0,n.useMemo)((()=>s&&c?n.createElement(ht,{svgContent:c,reducedMotion:N,ref:F}):null),[s,N,c]),J=(0,n.useMemo)((()=>s?G:X),[s,G,X]),Q=r=>n.createElement("div",t()({id:o,className:m},$e(p,b),qe(e),{"data-semantic-classname":P.root,lang:_},L&&{dir:L}),n.createElement("button",t()({type:l,disabled:d,className:$,"data-testid":Ye},j,q,{ref:z,role:r,"data-preview":I}),n.createElement(it,{label:u,icon:J,override:H.current,semanticClassNames:P,classNames:M})));return d&&O?Q("link"):O?(()=>{const{onFocus:r,onBlur:l,...i}=q;return n.createElement("div",t()({id:o,className:m},i,qe(e),$e(p,b),{"data-semantic-classname":P.root},L&&{dir:L}),n.createElement(Ge,t()({},a,j,{href:d?void 0:a.href,className:M.link,onFocusCapture:r,onBlurCapture:l,ref:z,dataPreview:I}),n.createElement(it,{label:u,icon:J,semanticClassNames:P,classNames:M})))})():Q()};var gt=n.forwardRef(ft);var vt=e=>{const{label:t,icon:n,override:o,semanticClassNames:a}=e;return r().createElement("span",{className:_e.classes.container},t&&r().createElement("span",{className:(0,_e.st)(_e.classes.label,Oe(a.buttonLabel)),"data-testid":et},t),n&&r().createElement("span",{className:(0,_e.st)(_e.classes.icon,{override:!!o},Oe(a.buttonIcon)),"aria-hidden":"true","data-testid":tt},n))};const yt=(e,r)=>{const{id:o,link:a,type:l="button",svgString:i,label:s,isDisabled:c,className:u,stylableButtonClassName:d,customClassNames:m=[],isQaMode:p,fullNameCompType:b,reportBiOnClick:h,a11y:f,corvid:g,isMaxContent:v=!1,isWrapText:y=!1,onDblClick:w,onMouseEnter:C,onMouseLeave:k,onFocus:x,onBlur:E,ariaAttributes:S,onClick:M,preventLinkNavigation:I,lang:N}=e,_=Je(a),L=I&&_,O=!c&&M||L,A=e.semanticClassNames||lt,D=n.useRef(null);n.useImperativeHandle(r,(()=>({focus:()=>{var e;null==(e=D.current)||e.focus()},blur:()=>{var e;null==(e=D.current)||e.blur()}})));const B=n.useMemo((()=>{var e,t;return Ve({ariaAttributes:{...S,...f,label:null!=(e=null!=(t=null==S?void 0:S.label)?t:f.label)?e:s},tabindex:null==f?void 0:f.tabindex})}),[f,s,S]),R=We({reportBiOnClick:h,onClick:O?e=>{L&&e.preventDefault(),!c&&(null==M||M(e))}:void 0}),T=n.useMemo((()=>((e,t,n)=>{let{onClick:r,onDblClick:o,onMouseEnter:a,onMouseLeave:l,onFocus:i,onBlur:s}=e;return{onClick:r,onMouseEnter:a,onMouseLeave:l,onKeyDown:t?Re:Te,onDoubleClick:!n&&o?o:void 0,onFocus:!n&&i?i:void 0,onBlur:!n&&s?s:void 0}})({onClick:R,onDblClick:w,onMouseLeave:k,onMouseEnter:C,onFocus:x,onBlur:E},_,c)),[R,w,k,C,x,E,_,c]),{iconSvgString:P,iconCollapsed:z,...H}=g||{},F=(0,_e.st)(_e.classes.root,{error:!1,disabled:c,isMaxContent:v,isWrapText:y,...dt(H)},d,Oe(A.root,...m));let V=null,W=!1;z||null===P||(P?(V=st(ct(P,o)),W=!0):i&&(V=st(ct(i,o))));const j=r=>n.createElement("div",t()({id:o,className:u},$e(p,b),qe(e),{"data-semantic-classname":A.root}),n.createElement("button",t()({type:l,disabled:c,className:F,"data-testid":Ye},B,T,{ref:D,role:r}),n.createElement(vt,{label:s,icon:V,override:W,semanticClassNames:A})));return c&&_?j("link"):_?(()=>{const{onFocus:r,onBlur:l,...i}=T;return n.createElement("div",t()({id:o,className:u},i,qe(e),$e(p,b),{"data-semantic-classname":A.root,lang:N}),n.createElement(Ge,t()({},a,B,{href:c?void 0:a.href,className:(0,_e.st)(F,_e.classes.link),onFocusCapture:r,onBlurCapture:l,ref:D}),n.createElement(vt,{label:s,icon:V,semanticClassNames:A})))})():j()};var wt=n.forwardRef(yt);const Ct=(e,n)=>{const{isDisabled:o,stylableButtonClassName:a,customClassNames:l=[],corvid:i,isMaxContent:s=!1,isWrapText:c=!1,isUdpExperimentOn:u}=e;if(!u)return r().createElement(wt,t()({},e,{ref:n}));const{iconSvgString:d,iconCollapsed:m,...p}=i||{},b=e.semanticClassNames||lt,h=(0,_e.st)(_e.classes.root,{error:!1,disabled:o,isMaxContent:s,isWrapText:c,...dt(p)},a,Oe(b.root,...l)),f=(0,_e.st)(h,_e.classes.link),g=(0,_e.st)(_e.classes.label,Oe(b.buttonLabel));let v=!1;m||null===d||d&&(v=!0);const y=(0,_e.st)(_e.classes.icon,{override:v},Oe(b.buttonIcon)),w={...e,classNames:{root:h,link:f,label:g,icon:y,container:(0,_e.st)(_e.classes.container)}};return r().createElement(gt,t()({},w,{ref:n}))};var kt=r().forwardRef(Ct);const xt=e=>{const t={...e,classes:{...Ne.classes,hamburgerCloseButtonRoot:(0,Ne.st)(Ne.classes.root),stylableButtonRoot:(0,Ne.st)(_e.classes.root)},StylableButton:kt};return r().createElement(ge,t)};var Et=r().forwardRef(xt),St={root:"b5zn61",hasBorderColor:"kRkc5Y",hasBackgroundColor:"oAOb11",hasHoverBorderColor:"vOCSHf",hasHoverBackgroundColor:"S1KgBG",hasDisabledBorderColor:"n4G1gL",hasDisabledBackgroundColor:"fagXcI",label:"Q_1B5v",hasColor:"lKBJfO",hasHoverColor:"EgnLMx",hasDisabledColor:"Z8I3Qb",link:"boASKT",container:"PlliFI",icon:"UHJG9m",hasIconColor:"cFMwDV",hasHoverIconColor:"xizV0v",hasDisabledIconColor:"cOr5EQ"},Mt={root:"DPAltb",hasBorderColor:"HJQL50",hasBackgroundColor:"JYQq3z",hasHoverBorderColor:"lpKmSx",hasHoverBackgroundColor:"bk6HFS",hasDisabledBorderColor:"ivX9Rv",hasDisabledBackgroundColor:"E9JJpO",label:"gIbEBg",hasColor:"spiv_Z",hasHoverColor:"pkDIbl",hasDisabledColor:"FOmtsV",link:"OoFUKI",container:"wpLgnL",icon:"HvvH6i",hasIconColor:"EENh5d",hasHoverIconColor:"tN8hsm",hasDisabledIconColor:"lVrFcO"};function It(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=It(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}var Nt=function(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=It(e))&&(r&&(r+=" "),r+=t);return r};function _t(){return function(e){const t=Object.keys(e),n=[];for(const r of t)e[r]&&n.push(r);return n}(dt(...arguments)).filter((e=>e in Mt)).map((e=>Mt[e]))}const Lt=(e,n)=>{const{isDisabled:o,stylableButtonClassName:a,customClassNames:l=[],corvid:i,isMaxContent:s=!1,isWrapText:c=!1,direction:u,fallbackDirectionClassName:d}=e,{iconSvgString:m,iconCollapsed:p,...b}=i||{},h=e.semanticClassNames||lt,f=Nt(Mt.root,a,!u&&d,Oe(h.root,...l),{error:!1,disabled:o,isMaxContent:s,isWrapText:c},_t(b)),g=Nt(f,Mt.link),v=Nt(Mt.label,Oe(h.buttonLabel));let y=!1;p||null===m||m&&(y=!0);const w=Nt(Mt.icon,{override:y},Oe(h.buttonIcon)),C={...e,classNames:{root:f,link:g,label:v,icon:w,container:Mt.container}};return r().createElement(gt,t()({},C,{ref:n}))};var Ot=r().forwardRef(Lt);const At=e=>{const t={...e,classes:{...St,hamburgerCloseButtonRoot:St.root},StylableButton:Ot};return r().createElement(ge,t)};var Dt=r().forwardRef(At);const Bt="mesh-container-content",Rt="inline-content",Tt=e=>r().createElement("div",{key:`${e.props.id}-rotated-wrapper`,"data-mesh-id":`${e.props.id}-rotated-wrapper`},e);var Pt=r().forwardRef(((e,t)=>{const{id:n,className:o,wedges:a=[],rotatedComponents:l=[],children:i,fixedComponents:s=[],extraClassName:c="",renderRotatedComponents:u=Tt}=e,d=r().Children.toArray(i()),m=[],p=[];d.forEach((e=>s.includes(e.props.id)?m.push(e):p.push(e)));const b=(e=>{const{wedges:t,rotatedComponents:n,childrenArray:o,renderRotatedComponents:a}=e,l=n.reduce(((e,t)=>({...e,[t]:!0})),{});return[...o.map((e=>{return l[(t=e,t.props.id.split("__")[0])]?a(e):e;var t})),...t.map((e=>r().createElement("div",{key:e,"data-mesh-id":e})))]})({childrenArray:p,rotatedComponents:l,wedges:a,renderRotatedComponents:u});return r().createElement("div",{...qe(e),"data-mesh-id":`${n}inlineContent`,"data-testid":Rt,className:Nt(o,c),ref:t},r().createElement("div",{"data-mesh-id":`${n}inlineContent-gridContainer`,"data-testid":Bt},b),m)}));const zt=13,Ht=27;function Ft(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}Ft(32),Ft(zt),Ft(Ht);const Vt=["aria-id","aria-metadata","aria-type"],Wt=(e,t)=>Object.entries(e).reduce(((e,n)=>{let[r,o]=n;return t.includes(r)||(e[r]=o),e}),{}),jt=function(e){let{role:t,tabIndex:n,tabindex:r,...o}=void 0===e?{}:e;const a=Object.entries(o).reduce(((e,t)=>{let[n,r]=t;return{...e,[("aria-"+n).toLowerCase()]:r}}),{role:t,tabIndex:null!=n?n:r});return Object.keys(a).forEach((e=>{void 0!==a[e]&&null!==a[e]||delete a[e]})),Wt(a,Vt)},Zt="responsive-container-overflow",Ut="responsive-container-content";var Kt="gNqkn3";const qt=r().forwardRef((({children:e,className:t},n)=>r().createElement("div",{className:Nt(t,Kt),"data-testid":Zt,ref:n},e)));var $t=r().forwardRef((({containerLayoutClassName:e,overlowWrapperClassName:t,hasOverflow:o,hasScrollOverflow:a,shouldOmitWrapperLayers:l,children:i,role:s,label:c,extraRootClass:u="",ariaLive:d,ariaAttributes:m,tabIndex:p,tagName:b="div"},h)=>{const f=!l&&o,g=f?a?p||0:-1:void 0,v=(0,n.useCallback)((e=>f?r().createElement(qt,{className:Nt(t,u)},e):e),[f,t,u]),y=o?e:Nt(e,u),w={ref:h,"data-testid":Ut,tabIndex:g,...s?{role:s}:{},...c?{"aria-label":c}:{},...d?{"aria-live":d}:{},...jt(m)};return"multi-column-layouter"===b?(w.class=y,w.style={visibility:"hidden"}):w.className=y,v(l?r().createElement(r().Fragment,null,i()):r().createElement(b||"div",w,i()))}));var Xt={root:"hamburger-menu-container"},Gt="sR1W7T";var Jt=e=>{const{id:r,className:o,customClassNames:a=[],children:l,hasResponsiveLayout:i,containerProps:s,meshProps:c,containerRootClassName:u,onOpen:d,onClose:m,onClick:p,onMouseEnter:b,onMouseLeave:h,rootClassName:f}=e,{isMenuOpen:g,menuContainerRef:v,animationState:y}=de(),w="function"==typeof l?l:()=>l;n.useEffect((()=>{void 0!==g&&(g?null==d||d({type:"onOpen"}):null==m||m({type:"onClose"}))}),[g,d,m]);const C=n.useCallback((e=>{null==p||p(e)}),[p]);return n.createElement("div",t()({id:r,ref:v,tabIndex:-1,className:Nt(u,o||"",Gt),onClick:C,onMouseEnter:b,onMouseLeave:h,"data-animation-name":y.name,"data-animation-state":y.phase},qe(e)),n.createElement("div",{"aria-hidden":!0,className:Nt(f,Oe(Xt.root,...a))}),i?n.createElement($t,t()({},s,{tabIndex:-1}),w):n.createElement(Pt,t()({},c,{id:r}),w))},Qt=ye((e=>{let{mapperProps:t,controllerUtils:n,stateValues:r}=e;const{editorType:o}=r;return{...t,editorType:o,updateComponentPropsInViewer:e=>{n.updateProps(e)}}})),Yt=o(15076);var en=e=>{const{stylableClassName:r}=e,{isMenuContainerFullscreen:o=!1}=de();return n.createElement(Jt,t()({},e,{rootClassName:Nt(Yt.classes.root,o?"":r)}))},tn="EdP9A9";var nn=e=>n.createElement(Jt,t()({},e,{rootClassName:tn}));const rn=e=>'[data-item-depth="'+e+'"]',on=e=>"rtl"===window.getComputedStyle(e).direction,an=e=>1-Math.pow(1-e,4),ln="scrollLeft",sn=function(e,t,n){void 0===n&&(n=700);const r=an,o=e[ln];let a=null;const l=i=>{null===a&&(a=i);const s=Math.min(1,(i-a)/n);e[ln]=o+r(s)*(t-o),1===s?a=null:requestAnimationFrame(l)};o!==t&&requestAnimationFrame(l)},cn=(e,t)=>{e.scrollTo({left:t,top:0,behavior:"smooth"})},un=(e,t)=>(Ke()?sn:cn)(e,t),dn=rn(0),mn=function(e,t){void 0===t&&(t=!0);const n=Array.from((null==e?void 0:e.querySelectorAll(dn))||[]).map((e=>{let{firstChild:t}=e;return t}));return t&&on(e.firstChild)?[...n].reverse():n},pn=e=>{var t;return(null==(t=e.querySelector('[data-menu-scroll-action="page"]'))?void 0:t.getBoundingClientRect().width)||0},bn=e=>{const{left:t,width:n}=e.getBoundingClientRect();return[t,n+t]},hn=e=>{const[t,n]=bn(e),r=getComputedStyle(e),[o,a,l,i]=[r.paddingLeft,r.paddingRight,r.borderLeftWidth,r.borderRightWidth].map((e=>parseFloat(e)));return[t+o+l,n-a-i]},fn=15,gn=(e,t)=>{const n="forward"===t,r=n?mn(e,!1):mn(e,!1).reverse(),o=on(e.firstChild),a=r.map(bn),[l,i]=hn(e),s=(n?o:!o)?vn(a,l):((e,t)=>{const n=[...e].findIndex((e=>{let[,n]=e;return n>=t}));return-1===n?0:n})(a,i);s<a.length&&yn(e,r[s],s+1<r.length?r[s+1]:void 0)},vn=(e,t)=>{const n=e.findIndex((e=>{let[n]=e;return n<=t}));return-1===n?0:n},yn=(e,t,n)=>{const[r,o]=hn(e),[a,l]=bn(t);if(a>=r&&l<=o)return;const i=r+pn(e);Math.abs(a-i)<fn&&n?yn(e,n):un(e,e.scrollLeft+a-i)},wn=(e,t,n)=>n&&(r=>{null==n||n(r,{...e,selected:t})}),Cn=e=>{if(null==e||!e.current)return;return getComputedStyle(e.current).getPropertyValue("--orientation")},kn=e=>{const t=Cn(e)===m,n=(e=>{if(null==e||!e.current)return;return getComputedStyle(e.current).getPropertyValue("--vertical-dropdown-display")})(e);return t&&n===p},xn=(e,t,n)=>{const r=!e,o=[c.Exit,c.ExitActive].includes(n.phase);return r&&t&&!o};var En={listItem:"YZgQBw",itemWrapper:"djGpM3",labelContainer:"oU_31J",label:"xeYvd7",horizontalDropdown:"TNsPxD",dropdownToggleButton:"okY9U1",coverAllSpace:"DVFVut",srOnly:"VDxzQG",dropdownIcon:"EX5Ksz",noDropdownItems:"RAdtUj",horizontalDropdownDisplayWrapper:"cf3HLb",verticalDropdownDisplayWrapper:"UUkylQ",verticalDropdown:"zIlxcF",expandedDropdown:"mafHsF",divider:"yRGvfY"};const Sn=(e,t,n)=>{const r=(e=>{const t=[];return e.forEach((e=>{t.push(e),e.items&&(t.push(...e.items),e.items.forEach((e=>{t.push(...e.items||[])})))})),t})(e),o=r.find((e=>e.selected));if(o)return o;if(t&&!(e=>"SCROLL_TO_TOP"===e.dataId||"SCROLL_TO_BOTTOM"===e.dataId)(t)){const e=r.find((e=>((e,t)=>{if(!t||"AnchorLink"!==t.type)return!1;const n=t.anchorCompId&&t.anchorCompId===e.compId,r=t.anchorDataId&&t.anchorDataId===e.dataId;return Boolean(n||r)})(t,e.link)));if(e)return e}return r.find((e=>{let{link:t}=e;return!!t&&("AnchorLink"!==t.type&&decodeURIComponent((t.href||"").split("?")[0])===n)}))},Mn=(e,t)=>{if(!t)return!1;if(!0===t.selected)return((e,t)=>!0===e.selected&&e.label===t.label)(e,t);if(!e.link||!t.link)return!1;const{link:n}=e,{link:r}=t;return n.type===r.type&&("AnchorLink"===n.type?(a=r,(o=n).anchorCompId===a.anchorCompId&&o.anchorDataId===a.anchorDataId):n.href===r.href);var o,a},In="menu",Nn="menu__item",_n="menu__item-label",Ln="menu__scroll-button",On="menu__scroll-button-icon",An="menu__item-icon";var Dn={root:"horizontal-menu",menuItemWrapper:"horizontal-menu__item",menuItemLabel:"horizontal-menu__item-label"};const Bn=e=>r().createElement("svg",t()({width:"16",height:"11",viewBox:"0 0 16 11",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),r().createElement("path",{d:"M8 10.5L16 1.86193L14.7387 0.5L8 7.77613L1.26133 0.499999L-5.95321e-08 1.86193L8 10.5Z"})),Rn=e=>{let{chevronButtonRef:t,onClick:n,isOpen:o,hasSubItems:a,hasLink:l,ariaLabel:i,isDropdownIconOpen:s,className:c}=e;return r().createElement("button",{"aria-label":i,ref:t,className:he()(En.dropdownToggleButton,"has-custom-focus",!a&&En.noDropdownItems,!l&&En.coverAllSpace,c),onClick:n,"aria-expanded":o},r().createElement(Bn,{"data-part":Q,"data-open":s?"true":void 0,className:he()(En.dropdownIcon,Oe(An))}))},Tn=Nt(En.label,Oe(Dn.menuItemLabel),Oe(_n)),Pn=e=>{const{item:o,currentItem:a,hasDropdownMenu:l,hasSubItems:i,onItemClick:s,onItemDblClick:u,onItemMouseIn:d,onItemMouseOut:m,partToPreviewStateMap:p,translations:b,chevronButtonRef:h,toggleDropdown:f,isOpen:g,dropdownAnimationState:v}=e,y=Mn(o,a),{label:w,link:C}=o,k=!Ue(C),x=(0,n.useRef)(null),E=Nt(En.labelContainer,Oe(Dn.menuItemWrapper),Oe(Nn)),S=(i||k)&&!y,M=g&&v.phase!==c.ExitActive,{children:I,...N}=o;return r().createElement("div",{className:E,"data-selected":!!y||void 0,"data-part":ae,"data-preview":null==p?void 0:p.item,"data-interactive":S,onClick:wn(N,y,s),onMouseEnter:wn(N,y,d),onMouseLeave:wn(N,y,m),onDoubleClick:wn(N,y,u)},r().createElement(Ge,t()({ref:x},C,{dataPart:le,activateByKey:"Enter"},y&&{"aria-current":"page"}),r().createElement("div",{className:Tn,"data-part":Y},w)),l&&["default","srOnly"].map((e=>{var t;return r().createElement(Rn,{key:e,ariaLabel:null==(t=b.dropdownButtonAriaLabel)?void 0:t.replace("<%= itemName %>",w),chevronButtonRef:h,onClick:f,isOpen:g,hasSubItems:i,hasLink:k,isDropdownIconOpen:M,className:"srOnly"===e?En.srOnly:void 0})})))};o(40126),o(93494);var zn=o(99025),Hn=o.n(zn);const Fn=(e,t)=>{if(Wn(e)){const t=(e=>e.match(/(color_\d+)\)/)[1])(e),n=(e=>{var t;const n=e.match(/,[ ]?(\d+(\.\d*)?)\)$/);return parseFloat(null!=(t=null==n?void 0:n[1])?t:"1")})(e),r={propertyType:"ThemeColor",theme:t};return n<1&&(r.alpha={propertyType:"Opacity",value:n}),r}if(jn(e)){const n={propertyType:"ThemeColor",theme:e};return null!=t&&t<1&&(n.alpha={propertyType:"Opacity",value:t}),n}},Vn=(e,t)=>{try{const n=Fn(e,t);return n||((e,t)=>{const{r:n,g:r,b:o,alpha:a}=Hn()(e).object(),l=null!=t&&t<1?t:a;return{propertyType:"Color",value:Hn()([n,r,o,parseFloat((null==l?void 0:l.toFixed(2))||"1")]).hexa()}})(e,t)}catch(e){return}},Wn=e=>{const t=/^rgb\(var\(--color_\d+\)\)$/.test(e),n=/^rgba\(var\(--color_\d+\),[ ]?\d+(\.\d*)?\)$/.test(e);return t||n},jn=e=>/^color_\d+$/.test(e),Zn=(e,t={fallbackValue:void 0})=>{const{fallbackValue:n,shouldRoundValue:r}=t,o=parseFloat(e),a=Un(e);return l=o,Number.isNaN(l)?n:{value:r?Math.round(o):o,unit:a};var l},Un=e=>e.replace(/-?\d+(\.\d+)?/,"")||"px",Kn=e=>{const t=Zn(""+e);if(!t)return;const{value:n,unit:r}=t;return{propertyType:"Size",unit:r,value:n}},qn=/(\$\d+)/g,$n=e=>{const t=e=>{var r;const o=null==(r=e.match(qn))?void 0:r[0];return o?t(e.replace(o,n.get(o))):e},{tokenMap:n,remainingExpression:r}=((e,t)=>{const n=new Map;let r=e;for(;r.length&&t.test(r);)r=r.replace(t,(e=>{const t="$"+n.size;return n.set(t,e),t}));return{remainingExpression:r,tokenMap:n}})(e,/([A-Za-z-]+\([^()]+\))/g);return r.split(",").map((e=>t(e.trim())))},Xn={ZeroPixels:{propertyType:"Size",value:0,unit:"px"}},Gn=e=>e.trim().split(/\s(?![^(]*\))/),Jn=e=>{const t=Gn(e),n=t.includes("inset"),r=t.slice(-1)[0],o=t.filter((e=>"inset"!==e)).filter((e=>e!==r)).map(Kn).filter(Boolean),[a,l,i,s]=o,c=Vn(r);if(a&&l&&c)return{propertyType:"BoxShadowLayer",offsetX:a,offsetY:l,inset:n,blurRadius:null!=i?i:Xn.ZeroPixels,spreadRadius:null!=s?s:Xn.ZeroPixels,color:c}},Qn={screen:"calc(100vw - 2 * var(--computed-horizontal-margin))",menuStretched:"100%",menuCustomWidth:null,menuItem:"max-content"},Yn=e=>{const t=window.getComputedStyle(e),n=t.getPropertyValue("--dropdown-container-box-shadow"),r=t.getPropertyValue("--container-box-shadow")||n;if(!r||"none"===r)return 0;const o=(e=>{if(!e)return;if("none"===e)return{propertyType:"BoxShadow",layers:[]};const t=$n(e).map(Jn).filter(Boolean);return t.length?{propertyType:"BoxShadow",layers:t}:void 0})(r);if(!o||!o.layers.length)return 0;const a=o.layers.map((e=>{let{offsetX:t,offsetY:n,blurRadius:r,spreadRadius:o}=e;return Math.max(Math.abs(t.value),Math.abs(n.value))+r.value+o.value}));return Math.max(...a)},er=(e,t)=>{const n=e.getBoundingClientRect(),r=window.getComputedStyle(t).getPropertyValue("--computed-space-above"),o=t.offsetHeight+Number.parseInt(r,10),a=window.innerHeight-n.bottom>=o,l=n.top-(()=>{const e=window.getComputedStyle(document.documentElement).getPropertyValue("--wix-ads-height");return Number.parseInt(e,10)||0})();return!a&&l>=o&&(t.style.setProperty("margin-top","-"+(o+n.height)+"px","important"),t.style.setProperty("--before-el-top","100%"),!0)},tr=function(e,t){void 0===t&&(t=!0);const n=null==e?void 0:e.querySelector('[data-part="'+J+'"]'),r=null==e?void 0:e.closest('[data-part="'+G+'"]');if(!e||!n||!r)return;t&&e.setAttribute("data-open","calculating"),((e,t,n)=>{const r=window.getComputedStyle(t),o=r.getPropertyValue("--computed-anchor"),a=r.getPropertyValue("--computed-align");t.style.setProperty("width",Qn[o]),"menuItem"===o&&t.style.setProperty("min-width",e.offsetWidth+"px");const l=(e=>{var t;switch(e.anchor){case"screen":return-(null!=(t=e.menuEl.getBoundingClientRect().left)?t:0);case"menuStretched":return 0;case"menuCustomWidth":return(e=>{let{align:t,menuEl:n,dropdownEl:r}=e;switch(t){case"start":return 0;case"center":return(n.offsetWidth-r.offsetWidth)/2;case"end":return n.offsetWidth-r.offsetWidth}})(e);case"menuItem":return(e=>{let{align:t,itemEl:n,dropdownEl:r}=e;switch(t){case"start":return n.offsetLeft;case"center":return n.offsetLeft+(n.offsetWidth-r.offsetWidth)/2;case"end":return n.offsetLeft+n.offsetWidth-r.offsetWidth}})(e)}})({itemEl:e,dropdownEl:t,menuEl:n,anchor:o,align:a});t.style.setProperty("--dropdown-left",l+"px"),t.style.setProperty("margin-left",(e=>{switch(e){case"screen":return"var(--computed-horizontal-margin)";case"menuStretched":case"menuCustomWidth":return"0";case"menuItem":return"var(--scrolled-left)"}})(o))})(e,n,r);const o=er(e,n),a=Yn(n),l=a?"-"+a+"px":"0%";n.style.setProperty("--shadow-margin",l);const i=o?"inset(100% "+l+" "+l+" "+l+")":"inset("+l+" "+l+" 100% "+l+")";n.style.setProperty("--animation-clip-path",i),n.setAttribute("data-open","true"),e.setAttribute("data-open","true")},nr=e=>{const t=null==e?void 0:e.querySelector('[data-part="'+J+'"]');null==e||e.removeAttribute("data-open"),null==t||t.removeAttribute("style"),null==t||t.removeAttribute("data-open")},rr=(e,t)=>{const r=(0,n.useRef)(!0);(0,n.useEffect)((()=>{r.current||e(),r.current=!1}),t)},or=e=>{var o;const{item:a,currentItem:l,focusOnMenuItem:i,getAnimationPackage:s,partToPreviewStateMap:c,HorizontalDropdown:m,VerticalDropdown:p,submenuDirection:b}=e,{children:h,forceOpen:f=!1}=a,{animationState:g,initEnterAnimation:v,initExitAnimation:y}=u(j,q),w=!(null==(o=a.items)||!o.length),C=!!h,k=!!a.children,x=C||k||w,E=!Ue(a.link),S=(0,n.useRef)(null),M=(0,n.useRef)(null),{isOpen:I,dropdownAnimationState:N,showDropdown:_,hideDropdown:L,toggleDropdown:O}=(e=>{let{itemRef:t,forceOpen:r,getAnimationPackage:o}=e;const{animationState:a,initEnterAnimation:l,initExitAnimation:i}=u(Z,$),[s,c]=(0,n.useState)(r),d=()=>{const e=o();return K[e].dropdownAnimationName},m=()=>{var e;const n=d();tr(t.current),c(!0),null==(e=t.current)||e.focus(),l(n)},p=()=>{const e=d();i(e,(()=>c(!1)))};return rr((()=>{s||nr(t.current)}),[s]),rr((()=>{r?m():p()}),[r]),(0,n.useEffect)((()=>()=>nr(t.current)),[]),{isOpen:s,dropdownAnimationState:a,showDropdown:m,hideDropdown:p,toggleDropdown:()=>{s?p():m()}}})({itemRef:M,forceOpen:f,getAnimationPackage:s}),A=(e=>{let{showDropdown:t,hideDropdown:n,itemRef:r,getMenuItemAnimationName:o,initEnterAnimation:a,initExitAnimation:l,isOpen:i,hasLink:s,dropdownAnimationState:c}=e;const u=()=>{const e=Cn(r)===d,t=kn(r),n=xn(e,i,c);return!(!s&&(t||n)||!e)};return{onMouseEnter:()=>{const e=Cn(r)===d,n=kn(r),l=xn(e,i,c);if(!s&&(n||l))return;const u=o();e&&t(),a(u)},onMouseLeave:()=>{const e=o();u()&&n(),l(e)},onBlur:e=>{const t=!e.relatedTarget||!e.currentTarget.contains(e.relatedTarget),r=o();u()&&t&&n(),l(r)}}})({showDropdown:_,hideDropdown:L,itemRef:M,getMenuItemAnimationName:()=>{const e=s();return K[e].menuItemAnimationName},initEnterAnimation:v,initExitAnimation:y,isOpen:I,hasLink:E,dropdownAnimationState:N});(0,n.useEffect)((()=>{(null==l?void 0:l.id)===a.id&&M.current&&(null==i||i(M.current))}),[l,a,i]);const D=(0,n.useCallback)((()=>{var e;null==(e=S.current)||e.focus(),L()}),[L,S]),B=(0,n.useCallback)((e=>{"Escape"===e.key&&D()}),[D]);(0,n.useEffect)((()=>(I?window.addEventListener("keydown",B):window.removeEventListener("keydown",B),()=>{window.removeEventListener("keydown",B)})),[I,B]);return r().createElement("li",{ref:M,className:En.listItem,"data-part":oe,"data-animation-name":g.name,"data-animation-state":g.phase,"data-item-depth":"0",onFocus:()=>{const e=M.current;e&&!(e=>{const t=e.getBoundingClientRect();return t.top>=0&&t.bottom<=window.innerHeight})(e)&&e.scrollIntoView()}},r().createElement("div",t()({className:En.itemWrapper},A),r().createElement(Pn,t()({},e,{hasDropdownMenu:x,hasSubItems:w,chevronButtonRef:S,toggleDropdown:O,isOpen:I,dropdownAnimationState:N})),x&&r().createElement(m,{onEscKeyDown:D,item:a,currentItem:l,children:h,dropdownAnimationState:N,direction:b})),w&&r().createElement(p,{isOpen:I,onEscKeyDown:D,item:a,currentItem:l,partToPreviewStateMap:c,dropdownAnimationState:N,direction:b}),r().createElement("span",{className:En.divider}))};var ar="zi7u4T",lr="Trmtvb",ir="qi0bcm",sr="n2xrZl",cr="wRGkgf",ur="JRUANI";const dr=Nt("aTo_UF",Oe(On));var mr=e=>{const{className:t,onClick:n,direction:o,isHidden:a,dataPart:l,previewState:i}=e,s=Nt(sr,t,a?ur:cr,Oe(Ln),"scroll-button");return r().createElement("div",{onClick:n,"aria-hidden":"true","aria-label":"scroll",className:s,"data-menu-scroll-action":"page","data-hidden":a,"data-preview":i,"data-part":l},r().createElement("span",{className:dr},r().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 6 12"},r().createElement("path",{d:"forward"===o?"M6 6L.8 0 0 .7 4.7 6 0 11.3l.8.7z":"M0 6L5.2 0 6 .7 1.3 6 6 11.3 5.2 12z"}))))},pr="BV22op";const br=e=>{const{isScrollBackwardButtonShown:t,isScrollForwardButtonShown:n,scrollPageForward:o,scrollPageToBackward:a,previewState:l}=e;return r().createElement("div",{className:pr},r().createElement(mr,{direction:"backward",onClick:a,isHidden:!t,dataPart:se,previewState:l}),r().createElement(mr,{direction:"forward",onClick:o,isHidden:!n,dataPart:ce,previewState:l}))},hr=({callback:e,ref:t,elem:r})=>{(0,n.useEffect)((()=>{const n=new ResizeObserver(e);return t?.current&&n.observe(t.current),r&&n.observe(r),()=>{n.disconnect()}}),[t,r,e])},fr=(0,n.createContext)({items:[],currentUrl:"#",translations:{menuNavAriaLabel:"Site",dropdownButtonAriaLabel:"More pages"},menuStyleId:"",customClassNames:[],getAnimationPackage:()=>b,navAriaLabel:"",isHamburgerMenuOpened:void 0,setIsHamburgerMenuOpened:void 0,a11yProps:{}}),gr=()=>(0,n.useContext)(fr),vr=e=>{let{children:t,items:o,partToPreviewStateMap:a,currentUrl:l,activeAnchor:i,translations:s,menuStyleId:c,customClassNames:u,getAnimationPackage:d,navAriaLabel:m,isHamburgerMenuOpened:p,setIsHamburgerMenuOpened:b,a11yProps:h}=e;const f=(0,n.useMemo)((()=>({items:o,partToPreviewStateMap:a,currentUrl:l,activeAnchor:i,translations:s,menuStyleId:c,customClassNames:u,getAnimationPackage:d,navAriaLabel:m,isHamburgerMenuOpened:p,setIsHamburgerMenuOpened:b,a11yProps:h})),[o,a,l,i,s,c,u,d,m,p,b,h]);return r().createElement(fr.Provider,{value:f},t)};var yr=e=>{const{id:o,className:a,directionClassName:l,direction:i,submenuDirection:s,onItemMouseIn:c,onItemMouseOut:u,onItemClick:d,onItemDblClick:m,HorizontalDropdown:p,VerticalDropdown:b}=e,{items:h,partToPreviewStateMap:f,currentUrl:g,activeAnchor:v,translations:y,customClassNames:w=[],getAnimationPackage:C,navAriaLabel:k,a11yProps:x}=gr(),E=Nt(ar,!i&&(null!=l?l:lr),Oe(Dn.root),Oe(In,...w)),S=r().useRef(null),M=(0,n.useMemo)((()=>Sn(h,v,g)),[h,v,g]),{isScrollable:I,updateScrollState:N,handleOnScroll:_,handleScrollForward:L,handleScrollBackward:O,isScrollBackwardButtonVisible:A,isScrollForwardButtonVisible:D}=(e=>{const[t,r]=(0,n.useState)(!1),[o,a]=(0,n.useState)(!1),[l,i]=(0,n.useState)(!1),s=(0,n.useCallback)((()=>{e.current&&t&&gn(e.current,"backward")}),[t,e]),c=(0,n.useCallback)((()=>{e.current&&t&&gn(e.current,"forward")}),[t,e]),u=(0,n.useCallback)(je((()=>{const{current:t}=e;if(!t)return;const{scrollWidth:n,clientWidth:o}=t,l=Math.abs(t.scrollLeft),s=o<n;r(s),a(s&&l>fn),i(s&&l<n-o-fn)}),100),[]),d=(0,n.useCallback)((()=>{const{current:t}=e;t&&t.style.setProperty("--scrolled-left",-t.scrollLeft+"px")}),[e]),m=(0,n.useCallback)((()=>{u(),d()}),[u,d]);return{isScrollable:t,updateScrollState:u,handleOnScroll:m,handleScrollForward:c,handleScrollBackward:s,isScrollBackwardButtonVisible:o,isScrollForwardButtonVisible:l}})(S),B=(0,n.useCallback)((e=>{const{current:t}=S;if(!I||!e||!t)return;const n=t.closest(dn);yn(t,n&&n!==e?n:e)}),[I]),R=(0,n.useCallback)((e=>{const t=e.target;B(t)}),[B]);return hr({ref:S,callback:N}),(0,n.useEffect)(N,[h,N]),r().createElement("div",{id:o,className:a},r().createElement("nav",t()({className:E,ref:S},I&&{onScroll:_,onFocus:R},{"data-part":G,"data-hook":W},x,{"aria-label":k||y.menuNavAriaLabel,dir:i}),r().createElement("ul",{className:ir},h.map((e=>r().createElement(or,{translations:y,currentItem:M,item:e,key:e.id,partToPreviewStateMap:f,onItemClick:d,focusOnMenuItem:B,onItemMouseIn:c,onItemMouseOut:u,onItemDblClick:m,getAnimationPackage:C,HorizontalDropdown:p,VerticalDropdown:b,submenuDirection:s})))),r().createElement(br,{scrollPageForward:L,scrollPageToBackward:O,isScrollBackwardButtonShown:A,isScrollForwardButtonShown:D,previewState:null==f?void 0:f["scroll-button"]})))};const wr=(0,n.createContext)({item:{label:"",link:{}},dropdownAnimationState:$,isOpen:!1}),Cr=()=>(0,n.useContext)(wr);var kr="iYypXb",xr="XtDqne",Er="Nspbxw",Sr="vXnpjR",Mr="uTHWho",Ir="iJI7uP",Nr="jArlJY",_r="h9SGFo",Lr="HV3wNd";const Or={root:"linkElement"},Ar=(e,r)=>{const{href:o,role:a,target:l,rel:i,className:s="",children:c,linkPopupId:u,anchorDataId:d,anchorCompId:m,tabIndex:p,dataTestId:b=Or.root,title:h,onClick:f,onDoubleClick:g,onMouseEnter:v,onMouseLeave:y,onFocus:w,onFocusCapture:C,onBlurCapture:k,"aria-live":x,"aria-disabled":E,"aria-label":S,"aria-labelledby":M,"aria-pressed":I,"aria-expanded":N,"aria-describedby":_,"aria-haspopup":L,"aria-current":O,dataPreview:A,dataPart:D}=e,B=void 0!==e.activateByKey?e.activateByKey:(e=>e?"SpaceOrEnter":"Space")(u);let R;switch(B){case"Enter":R=Te;break;case"Space":R=Re;break;case"SpaceOrEnter":R=Pe;break;default:R=void 0}return void 0!==o||u?n.createElement("a",t()({},qe(e),{"data-testid":b,"data-popupid":u,"data-anchor":d,"data-anchor-comp-id":m,"data-preview":A,"data-part":D,href:o||void 0,target:l,role:u?"button":a,rel:i,className:s,onKeyDown:R,"aria-live":x,"aria-disabled":E,"aria-label":S,"aria-labelledby":M,"aria-pressed":I,"aria-expanded":N,"aria-haspopup":L,"aria-describedby":_,"aria-current":O,title:h,onClick:f,onMouseEnter:v,onMouseLeave:y,onDoubleClick:g,onFocus:w,onFocusCapture:C,onBlurCapture:k,ref:r,tabIndex:u?0:p}),c):n.createElement("div",t()({},qe(e),{"data-testid":b,"data-preview":A,"data-part":D,className:s,tabIndex:p,"aria-label":S,"aria-labelledby":M,"aria-haspopup":L,"aria-disabled":E,"aria-expanded":N,title:h,role:a,onClick:f,onDoubleClick:g,onMouseEnter:v,onMouseLeave:y,ref:r}),c)};var Dr=n.forwardRef(Ar);const Br="submenu-item-label",Rr="submenu-sub-item-label",Tr="dropdown-menu",Pr="dropdown-menu__item",zr="dropdown-menu__item-label",Hr="dropdown-menu__subitem",Fr="dropdown-menu__subitem-label",Vr="submenu",Wr=e=>{let{items:r,currentItem:o,isRootLevel:a=!0,partToPreviewStateMap:l,onEscKeyDown:i,customClassNames:s=[]}=e;const c=Nt(a?xr:Er,En.dropdownMenu,{[Oe(Tr,...s)]:a}),u=e=>Nt(a?Mr:_r,{[Ir]:e},a?{[Oe(Pr)]:!0}:{[Oe(Hr)]:!0},"has-inner-focus-ring"),d=Nt(a?Nr:Lr,Oe(a?zr:Fr));return n.createElement("ul",{className:c,style:{"--items-number":r.length},onKeyDown:e=>{if("Escape"===e.key){var t;if(i)return void i();const n=e.target.closest("li").closest(rn(0));null==n||null==(t=n.firstChild)||t.focus()}}},r.map(((e,r)=>{const{items:i,link:s,label:c}=e,m=Mn(e,o);return n.createElement("li",{className:Nt({[Sr]:!a}),key:r,"data-item-depth":a?1:2},n.createElement(Dr,t()({},s,{className:u(m),dataPart:a?ee:ne,dataPreview:null==l?void 0:l[a?"item":"sub-item"],onFocus:e=>{e.stopPropagation()}},m&&{"aria-current":"page"}),n.createElement("span",{className:d,"data-selected":m?"true":void 0,"data-part":a?te:re,"data-testid":a?Br:Rr},c)),!(null==i||!i.length)&&n.createElement(Wr,{partToPreviewStateMap:l,items:i,isRootLevel:!1,currentItem:o}))})))};var jr=e=>{const{id:r,partToPreviewStateMap:o,customClassNames:a=[],className:l}=e,{item:i,currentItem:s,onEscKeyDown:c}=Cr();return n.createElement("div",t()({id:r},qe(e),{className:Nt(l,kr),"data-hook":Vr}),n.createElement(Wr,{partToPreviewStateMap:o,items:i.items||[],currentItem:s,onEscKeyDown:c,customClassNames:a}))};const Zr=e=>{let{id:n,childrenWrapperClassName:o,dataAttributes:a,children:l,menuOrientation:i="horizontal"}=e;const{dropdownAnimationState:s=$,item:c}=Cr();return r().createElement("div",t()({id:n},a,{className:"horizontal"===i?En.horizontalDropdown:En.verticalDropdown,role:"group","aria-label":c.label,"data-part":J}),r().createElement("div",{className:o,"data-animation-name":s.name,"data-animation-state":s.phase},l))};var Ur="eH50g1",Kr="fr3A7J";const qr="dropdown-container";var $r=e=>{const{id:t,children:r,containerRootClassName:o,menuOrientation:a,customClassNames:l=[]}=e,i="function"==typeof r?r:()=>r,s=Nt(Ur,Kr,o,Oe(qr,...l));return n.createElement(Zr,{id:t,dataAttributes:qe(e),childrenWrapperClassName:s,menuOrientation:a},n.createElement($t,e.containerProps,i))},Xr="H2ENa1";const Gr={resetStyles:"StylablePanel_Design_State_Match_Regular_Tooltip",viewMoreTooltip:"StylablePanel_Design_State_Tooltip",regularLabel:"StylablePanel_Design_State_Dropdown_Regular",native:{hoverLabel:"StylablePanel_Design_State_Dropdown_Hover",focusLabel:"StylablePanel_Design_State_Dropdown_Selected",disabledLabel:"StylablePanel_Design_State_Dropdown_Disabled",activeLabel:"StylablePanel_Design_State_Dropdown_Clicked"},overrides:{applyRegularDesign:"StylablePanel_Design_State_Apply_Regular_Design",applyToOtherStates:"StylablePanel_Design_State_Apply_to_Other_States",stateApplyToState:"StylablePanel_Design_State_Apply_to_State",stateApplyToSpecificState:"StylablePanel_Design_State_Apply_to_Specific_State"},stateLabel:"StylablePanel_Design_State_Label"},Jr=({size:e,...t})=>n.createElement("svg",{viewBox:"0 0 24 24",fill:"currentColor",width:e||"24",height:e||"24",...t},n.createElement("path",{d:"M16,6 L8,6 C6.35,6 5,7.35 5,9 L5,11 L7,11 L7,9 C7,8.448 7.447,8 8,8 L11,8 L11,17 L9,17 L9,19 L15,19 L15,17 L13,17 L13,8 L16,8 C16.553,8 17,8.448 17,9 L17,11 L19,11 L19,9 C19,7.343 17.657,6 16,6"}));Jr.displayName="TextBold";const Qr=({size:e,...t})=>n.createElement("svg",{viewBox:"0 0 24 24",fill:"currentColor",width:e||"24",height:e||"24",...t},n.createElement("path",{d:"M10.896,16 C9.042,16 8.3,14.881 8.3,13.5 C8.3,12.12 8.979,11 10.896,11 L10.896,16 Z M11.5,5 C11.5,5 6,9.121 6,13.709 C6,16.771 8.517,19 11.523,19 C14.53,19 17,16.771 17,13.709 C17,9.121 11.5,5 11.5,5 L11.5,5 Z"}));Qr.displayName="ColorDropBold";const Yr=({size:e,...t})=>n.createElement("svg",{viewBox:"0 0 24 24",fill:"currentColor",width:e||"24",height:e||"24",...t},n.createElement("path",{d:"M10,7 L14,7 L14,5 L10,5 L10,7 Z M10,19 L14,19 L14,17 L10,17 L10,19 Z M17,14 L19,14 L19,10 L17,10 L17,14 Z M16,17 L16,19 C17.654,19 19,18 19,16 L17,16 C17,16.672 16.552,17 16,17 L16,17 Z M5,8 L7,8 C7,7.344 7.448,7 8,7 L8,5 C6.346,5 5,6 5,8 L5,8 Z M7,16 L5,16 C5,18 6.346,19 8,19 L8,17 C7.448,17 7,16.688 7,16 L7,16 Z M5,14 L7,14 L7,10 L5,10 L5,14 Z M17.004,8 L19,8 C19,6 18,5 16,5 L16,6.997 C16.555,6.997 17.004,7.446 17.004,8 L17.004,8 Z"}));Yr.displayName="BorderBold";const eo=({size:e,...t})=>n.createElement("svg",{viewBox:"0 0 24 24",fill:"currentColor",width:e||"24",height:e||"24",...t},n.createElement("path",{d:"M17,16 C17,16.552 16.553,17 16,17 L13,17 L13,19 L16,19 C17.657,19 19,17.657 19,16 L19,13 L17,13 L17,16 Z M5,8 L5,11 L7,11 L7,8 C7,7.45 7.45,7 8,7 L11,7 L11,5 L8,5 C6.343,5 5,6.343 5,8 L5,8 Z M16,5 L13,5 L13,7 L16,7 C16.553,7 17,7.448 17,8 L17,11 L19,11 L19,8 C19,6.343 17.657,5 16,5 L16,5 Z M7,16 L7,13 L5,13 L5,16 C5,17.657 6.343,19 8,19 L11,19 L11,17 L8,17 C7.45,17 7,16.55 7,16 L7,16 Z"}));eo.displayName="CornersBold";const to=({size:e,...t})=>n.createElement("svg",{viewBox:"0 0 24 24",fill:"currentColor",width:e||"24",height:e||"24",...t},n.createElement("path",{d:"M15,13 L15,7 C15,5.896 14.104,5 13,5 L7,5 C5.896,5 5,5.896 5,7 L5,13 C5,14.104 5.896,15 7,15 L13,15 C14.104,15 15,14.104 15,13 L15,13 Z M17,7 L17,14.5 C17,15.875 15.875,17 14.5,17 L8,17 L8,19 L15,19 C17.209,19 19,17.209 19,15 L19,7 L17,7 Z"}));to.displayName="ShadowBold";const no=({size:e,...t})=>n.createElement("svg",{viewBox:"0 0 24 24",fill:"currentColor",width:e||"24",height:e||"24",...t},n.createElement("path",{d:"M2,4.99508929 L2,19.0049107 C2,19.5552764 2.44394026,20 2.991155,20 L21.008845,20 C21.5568386,20 22,19.555686 22,19.0049107 L22,4.99508929 C22,4.44472363 21.5560597,4 21.008845,4 L2.991155,4 C2.44316143,4 2,4.44431397 2,4.99508929 Z M1,4.99508929 C1,3.8932319 1.88967395,3 2.991155,3 L21.008845,3 C22.1085295,3 23,3.8926228 23,4.99508929 L23,19.0049107 C23,20.1067681 22.1103261,21 21.008845,21 L2.991155,21 C1.89147046,21 1,20.1073772 1,19.0049107 L1,4.99508929 Z M6,8 L6,12 L5,12 L5,7 L10,7 L10,8 L6,8 Z M19,12 L19,17 L14,17 L14,16 L18,16 L18,12 L19,12 Z"}));no.displayName="SizeAndRatio";const ro={id:"regular",label:Gr.regularLabel,default:!0},oo={id:"hover",label:Gr.native.hoverLabel},ao=(Gr.native.disabledLabel,{id:"selected",label:Gr.native.focusLabel}),lo=ro.id,io=oo.id,so=ao.id,co=(e,t)=>Object.entries(e).reduce(((e,n)=>{let[r,o]=n;return{...e,[t+"-"+r]:o}}),{}),uo="sub",mo="dropdown-menu",po={"text-outline":"1px 0px transparent, -1px 0px transparent, 0px 1px transparent, 0px -1px transparent","text-shadow":"0px 0px 0px transparent","--*":"revert","-ms-accelerator":"false","-ms-block-progression":"tb","-ms-content-zoom-chaining":"none","-ms-content-zooming":"zoomForTheTopLevelNoneForTheRest","-ms-content-zoom-limit":"400% 100%","-ms-content-zoom-limit-max":"400%","-ms-content-zoom-limit-min":"100%","-ms-content-zoom-snap":"none snapInterval(0%, 100%)","-ms-content-zoom-snap-points":"snapInterval(0%, 100%)","-ms-content-zoom-snap-type":"none","-ms-filter":"&quot;&quot;","-ms-flow-from":"none","-ms-flow-into":"none","-ms-grid-columns":"none","-ms-grid-rows":"none","-ms-high-contrast-adjust":"revert","-ms-hyphenate-limit-chars":"revert","-ms-hyphenate-limit-lines":"revert","-ms-hyphenate-limit-zone":"revert","-ms-ime-align":"auto","-ms-overflow-style":"revert","-ms-scrollbar-3dlight-color":"revert","-ms-scrollbar-arrow-color":"revert","-ms-scrollbar-base-color":"revert","-ms-scrollbar-darkshadow-color":"revert","-ms-scrollbar-face-color":"revert","-ms-scrollbar-highlight-color":"revert","-ms-scrollbar-shadow-color":"revert","-ms-scrollbar-track-color":"revert","-ms-scroll-chaining":"chained","-ms-scroll-limit":"0 0 auto auto","-ms-scroll-limit-x-max":"auto","-ms-scroll-limit-x-min":"0","-ms-scroll-limit-y-max":"auto","-ms-scroll-limit-y-min":"0","-ms-scroll-rails":"railed","-ms-scroll-snap-points-x":"snapInterval(0px, 100%)","-ms-scroll-snap-points-y":"snapInterval(0px, 100%)","-ms-scroll-snap-type":"none","-ms-scroll-snap-x":"none snapInterval(0px, 100%)","-ms-scroll-snap-y":"none snapInterval(0px, 100%)","-ms-scroll-translation":"revert","-ms-text-autospace":"none","-ms-touch-select":"revert","-ms-user-select":"text","-ms-wrap-flow":"auto","-ms-wrap-margin":"0","-ms-wrap-through":"wrap","-moz-appearance":"noneButOverriddenInUserAgentCSS","-moz-binding":"none","-moz-border-bottom-colors":"none","-moz-border-left-colors":"none","-moz-border-right-colors":"none","-moz-border-top-colors":"none","-moz-context-properties":"revert","-moz-float-edge":"content-box","-moz-force-broken-image-icon":"0","-moz-image-region":"revert","-moz-orient":"inline","-moz-outline-radius":"0 0 0 0","-moz-outline-radius-bottomleft":"0","-moz-outline-radius-bottomright":"0","-moz-outline-radius-topleft":"0","-moz-outline-radius-topright":"0","-moz-stack-sizing":"revert","-moz-text-blink":"none","-moz-user-focus":"none","-moz-user-input":"revert","-moz-user-modify":"revert","-moz-window-dragging":"drag","-moz-window-shadow":"default","-webkit-appearance":"noneButOverriddenInUserAgentCSS","-webkit-border-before":"revert","-webkit-border-before-color":"revert","-webkit-border-before-style":"revert","-webkit-border-before-width":"revert","-webkit-box-reflect":"none","-webkit-line-clamp":"none","-webkit-mask":"none repeat scroll 0% 0% padding border","-webkit-mask-attachment":"scroll","-webkit-mask-clip":"border","-webkit-mask-composite":"source-over","-webkit-mask-image":"none","-webkit-mask-origin":"padding","-webkit-mask-position":"0% 0%","-webkit-mask-position-x":"0%","-webkit-mask-position-y":"0%","-webkit-mask-repeat":"repeat","-webkit-mask-repeat-x":"repeat","-webkit-mask-repeat-y":"repeat","-webkit-mask-size":"auto auto","-webkit-overflow-scrolling":"revert","-webkit-tap-highlight-color":"revert","-webkit-text-fill-color":"revert","-webkit-text-stroke":"revert","-webkit-text-stroke-color":"revert","-webkit-text-stroke-width":"revert","-webkit-touch-callout":"revert","-webkit-user-modify":"revert","accent-color":"revert","align-content":"normal","align-items":"normal","align-self":"auto","align-tracks":"normal",all:"noPracticalInitialValue","anchor-name":"none","anchor-scope":"none",animation:"none 0s ease 0s 1 normal none running auto","animation-composition":"replace","animation-delay":"0s","animation-direction":"normal","animation-duration":"0s","animation-fill-mode":"none","animation-iteration-count":"1","animation-name":"none","animation-play-state":"running","animation-range":"normal normal","animation-range-end":"normal","animation-range-start":"normal","animation-timing-function":"ease","animation-timeline":"auto",appearance:"none","aspect-ratio":"auto",azimuth:"revert","backdrop-filter":"none","backface-visibility":"visible",background:"none 0% 0% auto auto repeat padding-box border-box scroll transparent","background-attachment":"scroll","background-blend-mode":"normal","background-clip":"border-box","background-color":"transparent","background-image":"none","background-origin":"padding-box","background-position":"0% 0%","background-position-x":"0%","background-position-y":"0%","background-repeat":"repeat","background-size":"auto auto","block-size":"auto",border:"medium none currentcolor","border-block":"medium none currentcolor","border-block-color":"currentcolor","border-block-style":"none","border-block-width":"medium","border-block-end":"medium none currentcolor","border-block-end-color":"currentcolor","border-block-end-style":"none","border-block-end-width":"medium","border-block-start":"medium none revert","border-block-start-color":"currentcolor","border-block-start-style":"none","border-block-start-width":"medium","border-bottom":"medium none currentcolor","border-bottom-color":"currentcolor","border-bottom-left-radius":"0","border-bottom-right-radius":"0","border-bottom-style":"none","border-bottom-width":"medium","border-collapse":"revert","border-color":"currentcolor currentcolor currentcolor currentcolor","border-end-end-radius":"0","border-end-start-radius":"0","border-image":"none 100% 1 0 stretch","border-image-outset":"0","border-image-repeat":"stretch","border-image-slice":"100%","border-image-source":"none","border-image-width":"1","border-inline":"medium none currentcolor","border-inline-end":"medium none revert","border-inline-color":"currentcolor","border-inline-style":"none","border-inline-width":"medium","border-inline-end-color":"currentcolor","border-inline-end-style":"none","border-inline-end-width":"medium","border-inline-start":"medium none revert","border-inline-start-color":"currentcolor","border-inline-start-style":"none","border-inline-start-width":"medium","border-left":"medium none currentcolor","border-left-color":"currentcolor","border-left-style":"none","border-left-width":"medium","border-radius":"0 0 0 0","border-right":"medium none currentcolor","border-right-color":"currentcolor","border-right-style":"none","border-right-width":"medium","border-spacing":"revert","border-start-end-radius":"0","border-start-start-radius":"0","border-style":"none none none none","border-top":"medium none currentcolor","border-top-color":"currentcolor","border-top-left-radius":"0","border-top-right-radius":"0","border-top-style":"none","border-top-width":"medium","border-width":"medium medium medium medium",bottom:"auto","box-align":"stretch","box-decoration-break":"slice","box-direction":"normal","box-flex":"0","box-flex-group":"1","box-lines":"single","box-ordinal-group":"1","box-orient":"inlineAxisHorizontalInXUL","box-pack":"start","box-shadow":"none","box-sizing":"content-box","break-after":"auto","break-before":"auto","break-inside":"auto","caption-side":"revert",caret:"revert","caret-color":"revert","caret-shape":"revert",clear:"none",clip:"auto","clip-path":"none",color:"revert","color-scheme":"revert","column-count":"auto","column-fill":"balance","column-gap":"normal","column-rule":"medium none currentcolor","column-rule-color":"currentcolor","column-rule-style":"none","column-rule-width":"medium","column-span":"none","column-width":"auto",columns:"auto auto",contain:"none","contain-intrinsic-size":"none none","contain-intrinsic-block-size":"none","contain-intrinsic-height":"none","contain-intrinsic-inline-size":"none","contain-intrinsic-width":"none",container:"none normal","container-name":"none","container-type":"normal",content:"normal","content-visibility":"visible","counter-increment":"none","counter-reset":"none","counter-set":"none",cursor:"revert",direction:"revert",display:"inline","empty-cells":"revert","field-sizing":"fixed",filter:"none",flex:"0 1 auto","flex-basis":"auto","flex-direction":"row","flex-flow":"row nowrap","flex-grow":"0","flex-shrink":"1","flex-wrap":"nowrap",float:"none",font:"revert","font-family":"revert","font-feature-settings":"revert","font-kerning":"revert","font-language-override":"revert","font-optical-sizing":"revert","font-palette":"revert","font-variation-settings":"revert","font-size":"revert","font-size-adjust":"revert","font-smooth":"revert","font-stretch":"revert","font-style":"revert","font-synthesis":"revert","font-synthesis-position":"revert","font-synthesis-small-caps":"revert","font-synthesis-style":"revert","font-synthesis-weight":"revert","font-variant":"revert","font-variant-alternates":"revert","font-variant-caps":"revert","font-variant-east-asian":"revert","font-variant-emoji":"revert","font-variant-ligatures":"revert","font-variant-numeric":"revert","font-variant-position":"revert","font-weight":"revert","forced-color-adjust":"revert",gap:"normal normal",grid:"none none none auto auto row 0 0 normal normal","grid-area":"auto auto auto auto","grid-auto-columns":"auto","grid-auto-flow":"row","grid-auto-rows":"auto","grid-column":"auto auto","grid-column-end":"auto","grid-column-gap":"0","grid-column-start":"auto","grid-gap":"0 0","grid-row":"auto auto","grid-row-end":"auto","grid-row-gap":"0","grid-row-start":"auto","grid-template":"none none none","grid-template-areas":"none","grid-template-columns":"none","grid-template-rows":"none","hanging-punctuation":"revert",height:"auto","hyphenate-character":"revert","hyphenate-limit-chars":"revert",hyphens:"revert","image-orientation":"revert","image-rendering":"revert","image-resolution":"revert","ime-mode":"auto","initial-letter":"normal","initial-letter-align":"auto","inline-size":"auto","input-security":"auto",inset:"auto auto auto auto","inset-area":"none","inset-block":"auto auto","inset-block-end":"auto","inset-block-start":"auto","inset-inline":"auto auto","inset-inline-end":"auto","inset-inline-start":"auto",isolation:"auto","justify-content":"normal","justify-items":"legacy","justify-self":"auto","justify-tracks":"normal",left:"auto","letter-spacing":"revert","line-break":"revert","line-clamp":"none","line-height":"revert","line-height-step":"revert","list-style":"revert","list-style-image":"revert","list-style-position":"revert","list-style-type":"revert",margin:"0 0 0 0","margin-block":"0 0","margin-block-end":"0","margin-block-start":"0","margin-bottom":"0","margin-inline":"0 0","margin-inline-end":"0","margin-inline-start":"0","margin-left":"0","margin-right":"0","margin-top":"0","margin-trim":"none",mask:"none match-source repeat 0% 0% border-box border-box auto add","mask-border":"alpha 0 stretch 0 none auto","mask-border-mode":"alpha","mask-border-outset":"0","mask-border-repeat":"stretch","mask-border-slice":"0","mask-border-source":"none","mask-border-width":"auto","mask-clip":"border-box","mask-composite":"add","mask-image":"none","mask-mode":"match-source","mask-origin":"border-box","mask-position":"0% 0%","mask-repeat":"repeat","mask-size":"auto","mask-type":"luminance","masonry-auto-flow":"pack","math-depth":"revert","math-shift":"revert","math-style":"revert","max-block-size":"none","max-height":"none","max-inline-size":"none","max-lines":"none","max-width":"none","min-block-size":"0","min-height":"auto","min-inline-size":"0","min-width":"auto","mix-blend-mode":"normal","object-fit":"fill","object-position":"revert",offset:"normal none 0 auto auto","offset-anchor":"auto","offset-distance":"0","offset-path":"none","offset-position":"normal","offset-rotate":"auto",opacity:"1",order:"0",orphans:"revert",outline:"auto none medium","outline-color":"auto","outline-offset":"0","outline-style":"none","outline-width":"medium",overflow:"visible","overflow-anchor":"auto","overflow-block":"auto","overflow-clip-box":"padding-box","overflow-clip-margin":"0px","overflow-inline":"auto","overflow-wrap":"revert","overflow-x":"visible","overflow-y":"visible",overlay:"none","overscroll-behavior":"auto","overscroll-behavior-block":"auto","overscroll-behavior-inline":"auto","overscroll-behavior-x":"auto","overscroll-behavior-y":"auto",padding:"0 0 0 0","padding-block":"0 0","padding-block-end":"0","padding-block-start":"0","padding-bottom":"0","padding-inline":"0 0","padding-inline-end":"0","padding-inline-start":"0","padding-left":"0","padding-right":"0","padding-top":"0",page:"auto","page-break-after":"auto","page-break-before":"auto","page-break-inside":"auto","paint-order":"revert",perspective:"none","perspective-origin":"50% 50%","place-content":"normal normal","place-items":"normal legacy","place-self":"auto auto","pointer-events":"revert",position:"static","position-anchor":"implicit","position-try":"none normal","position-try-options":"none","position-try-order":"normal","position-visibility":"anchors-visible","print-color-adjust":"revert",quotes:"revert",resize:"none",right:"auto",rotate:"none","row-gap":"normal","ruby-align":"revert","ruby-merge":"revert","ruby-position":"revert",scale:"none","scrollbar-color":"revert","scrollbar-gutter":"auto","scrollbar-width":"auto","scroll-behavior":"auto","scroll-margin":"0 0 0 0","scroll-margin-block":"0 0","scroll-margin-block-start":"0","scroll-margin-block-end":"0","scroll-margin-bottom":"0","scroll-margin-inline":"0 0","scroll-margin-inline-start":"0","scroll-margin-inline-end":"0","scroll-margin-left":"0","scroll-margin-right":"0","scroll-margin-top":"0","scroll-padding":"auto auto auto auto","scroll-padding-block":"auto auto","scroll-padding-block-start":"auto","scroll-padding-block-end":"auto","scroll-padding-bottom":"auto","scroll-padding-inline":"auto auto","scroll-padding-inline-start":"auto","scroll-padding-inline-end":"auto","scroll-padding-left":"auto","scroll-padding-right":"auto","scroll-padding-top":"auto","scroll-snap-align":"none","scroll-snap-coordinate":"none","scroll-snap-destination":"0px 0px","scroll-snap-points-x":"none","scroll-snap-points-y":"none","scroll-snap-stop":"normal","scroll-snap-type":"none","scroll-snap-type-x":"none","scroll-snap-type-y":"none","scroll-timeline":"none block","scroll-timeline-axis":"block","scroll-timeline-name":"none","shape-image-threshold":"0.0","shape-margin":"0","shape-outside":"none","tab-size":"revert","table-layout":"auto","text-align":"revert","text-align-last":"revert","text-combine-upright":"revert","text-decoration":"currentcolor solid none","text-decoration-color":"currentcolor","text-decoration-line":"none","text-decoration-skip":"revert","text-decoration-skip-ink":"revert","text-decoration-style":"solid","text-decoration-thickness":"auto","text-emphasis":"revert","text-emphasis-color":"revert","text-emphasis-position":"revert","text-emphasis-style":"revert","text-indent":"revert","text-justify":"revert","text-orientation":"revert","text-overflow":"clip","text-rendering":"revert","text-shadow":"revert","text-size-adjust":"revert","text-transform":"revert","text-underline-offset":"revert","text-underline-position":"revert","text-wrap":"revert","text-wrap-mode":"revert","text-wrap-style":"revert","timeline-scope":"none",top:"auto","touch-action":"auto",transform:"none","transform-box":"view-box","transform-origin":"50% 50% 0","transform-style":"flat",transition:"0s 0s all ease normal","transition-behavior":"normal","transition-delay":"0s","transition-duration":"0s","transition-property":"all","transition-timing-function":"ease",translate:"none","unicode-bidi":"normal","user-select":"auto","vertical-align":"baseline","view-timeline":"none block","view-timeline-axis":"block","view-timeline-inset":"auto","view-timeline-name":"none","view-transition-name":"none",visibility:"revert","white-space":"revert","white-space-collapse":"revert",widows:"revert",width:"auto","will-change":"auto","word-break":"revert","word-spacing":"revert","word-wrap":"revert","writing-mode":"revert","z-index":"auto",zoom:"normal"},bo={"container-background":{type:"BACKGROUND_FILL",defaultValue:po.background,state:lo},"container-box-shadow":{type:"BOX_SHADOW",defaultValue:po["box-shadow"],state:lo},"container-border-left":{type:"BORDER_SIDE",defaultValue:po["border-left"],state:lo},"container-border-right":{type:"BORDER_SIDE",defaultValue:po["border-right"],state:lo},"container-border-top":{type:"BORDER_SIDE",defaultValue:po["border-top"],state:lo},"container-border-bottom":{type:"BORDER_SIDE",defaultValue:po["border-bottom"],state:lo},"container-border-radius":{type:"SIDES",defaultValue:po["border-radius"],state:lo}},ho={"container-padding-top":{type:"SIZE",defaultValue:po["padding-top"],state:lo},"container-padding-right":{type:"SIZE",defaultValue:po["padding-right"],state:lo},"container-padding-bottom":{type:"SIZE",defaultValue:po["padding-bottom"],state:lo},"container-padding-left":{type:"SIZE",defaultValue:po["padding-left"],state:lo}},fo={"item-background":{type:"BACKGROUND_FILL",defaultValue:po.background,state:lo},"item-hover-background":{type:"BACKGROUND_FILL",state:io},"item-selected-background":{type:"BACKGROUND_FILL",state:so},"item-font":{type:"FONT",defaultValue:po.font,state:lo},"item-color":{type:"CSS_COLOR",defaultValue:po.color,state:lo},"item-hover-color":{type:"CSS_COLOR",state:io},"item-selected-color":{type:"CSS_COLOR",state:so},"item-text-decoration":{type:"TEXT_DECORATION_LINE",defaultValue:po["text-decoration-line"],state:lo},"item-hover-text-decoration":{type:"TEXT_DECORATION_LINE",state:io},"item-selected-text-decoration":{type:"TEXT_DECORATION_LINE",state:so},"item-text-transform":{type:"TEXT_TRANSFORM",defaultValue:po["text-transform"],state:lo},"item-text-outline":{type:"TEXT_OUTLINE",defaultValue:po["text-outline"],state:lo},"item-hover-text-outline":{type:"TEXT_OUTLINE",state:io},"item-selected-text-outline":{type:"TEXT_OUTLINE",state:so},"item-text-highlight":{type:"CSS_COLOR",defaultValue:po["background-color"],state:lo},"item-hover-text-highlight":{type:"CSS_COLOR",state:io},"item-selected-text-highlight":{type:"CSS_COLOR",state:so},"item-letter-spacing":{type:"SIZE",defaultValue:po["letter-spacing"],state:lo},"item-line-height":{type:"SIZE",defaultValue:po["line-height"],state:lo},"item-text-shadow":{type:"TEXT_SHADOW",defaultValue:"0px 0px transparent",state:lo},"item-hover-text-shadow":{type:"TEXT_SHADOW",state:io},"item-selected-text-shadow":{type:"TEXT_SHADOW",state:so},"item-border-left":{type:"BORDER_SIDE",defaultValue:po["border-left"],state:lo},"item-hover-border-left":{type:"BORDER_SIDE",state:io},"item-selected-border-left":{type:"BORDER_SIDE",state:so},"item-border-right":{type:"BORDER_SIDE",defaultValue:po["border-right"],state:lo},"item-hover-border-right":{type:"BORDER_SIDE",state:io},"item-selected-border-right":{type:"BORDER_SIDE",state:so},"item-border-top":{type:"BORDER_SIDE",defaultValue:po["border-top"],state:lo},"item-hover-border-top":{type:"BORDER_SIDE",state:io},"item-selected-border-top":{type:"BORDER_SIDE",state:so},"item-border-bottom":{type:"BORDER_SIDE",defaultValue:po["border-bottom"],state:lo},"item-hover-border-bottom":{type:"BORDER_SIDE",state:io},"item-selected-border-bottom":{type:"BORDER_SIDE",state:so},"item-border-radius":{type:"SIDES",defaultValue:po["border-radius"],state:lo},"item-hover-border-radius":{type:"SIDES",state:io},"item-selected-border-radius":{type:"SIDES",state:so},"item-box-shadow":{type:"BOX_SHADOW",defaultValue:po["box-shadow"],state:lo},"item-hover-box-shadow":{type:"BOX_SHADOW",state:io},"item-selected-box-shadow":{type:"BOX_SHADOW",state:so}},go={"horizontal-item-icon-display":{type:"CSSString",defaultValue:"initial",state:lo},"item-icon-size":{type:"SIZE",defaultValue:po.height,state:lo},"item-icon-color":{type:"CSS_COLOR",defaultValue:po.color,state:lo},"item-hover-icon-color":{type:"CSS_COLOR",state:io},"item-selected-icon-color":{type:"CSS_COLOR",state:so},"item-divider":{type:"BORDER_SIDE",defaultValue:po["border-top"],state:lo},"item-text-align":{type:"TEXT_ALIGNMENT",defaultValue:po["justify-content"],state:lo},"item-direction":{type:"DIRECTION",defaultValue:po.direction,state:lo}},vo={"item-vertical-padding":{type:"SIZE",defaultValue:po["padding-top"],state:lo},"item-horizontal-padding":{type:"SIZE",defaultValue:po["padding-left"],state:lo}},yo=co(fo,uo),wo={"item-hover-font":{type:"FONT",state:io},"item-selected-font":{type:"FONT",state:so},"item-hover-text-transform":{type:"TEXT_TRANSFORM",state:io},"item-selected-text-transform":{type:"TEXT_TRANSFORM",state:so},"item-hover-letter-spacing":{type:"SIZE",state:io},"item-selected-letter-spacing":{type:"SIZE",state:so},"item-hover-line-height":{type:"SIZE",state:io},"item-selected-line-height":{type:"SIZE",state:so},"sub-item-hover-font":{type:"FONT",state:io},"sub-item-selected-font":{type:"FONT",state:so},"sub-item-hover-text-transform":{type:"TEXT_TRANSFORM",state:io},"sub-item-selected-text-transform":{type:"TEXT_TRANSFORM",state:so},"sub-item-hover-letter-spacing":{type:"SIZE",state:io},"sub-item-selected-letter-spacing":{type:"SIZE",state:so},"sub-item-hover-line-height":{type:"SIZE",state:io},"sub-item-selected-line-height":{type:"SIZE",state:so}},Co={...{...bo,...fo,...yo},...wo},ko=co(Co,mo),xo={"container-vertical-padding":{type:"SIZE",defaultValue:po["padding-top"],state:lo},"container-horizontal-padding":{type:"SIZE",defaultValue:po["padding-left"],state:lo},"item-vertical-spacing":{type:"SIZE",defaultValue:po["row-gap"],state:lo},"item-horizontal-spacing":{type:"SIZE",defaultValue:po["column-gap"],state:lo},"sub-items-vertical-spacing-before":{type:"SIZE",defaultValue:po["margin-top"],state:lo},"sub-items-vertical-spacing-between":{type:"SIZE",defaultValue:po.gap,state:lo},"sub-item-vertical-padding":{type:"SIZE",defaultValue:po["padding-top"],state:lo},"sub-item-horizontal-padding":{type:"SIZE",defaultValue:po["padding-left"],state:lo},"columns-number":{type:"CSSString",state:lo,defaultValue:"1"},align:{type:"TEXT_ALIGN",state:lo,defaultValue:po["text-align"]},"item-align":{type:"TEXT_ALIGN",state:lo,defaultValue:po["text-align"]},"sub-item-align":{type:"TEXT_ALIGN",state:lo,defaultValue:po["text-align"]}},Eo=["top","bottom","left","right"].reduce(((e,t)=>({...e,["container-padding-"+t]:{type:"SIZE",defaultValue:"initial",state:lo}})),{}),So={...vo,...xo},Mo=co({...So,...Eo},mo),Io={...Co,...So},No=[...Object.keys(Io),"submenu-direction","submenu-item-direction","submenu-sub-item-direction"],_o=Object.fromEntries(No.map((e=>["--"+e,"initial"]))),Lo=e=>{let{item:t,menuOrientation:o,className:a,children:l,partToPreviewStateMap:i}=e;const s=(0,n.useMemo)((()=>(e=>{if(!e)return{};const t=mo+"-";return Object.entries(e).reduce(((e,n)=>{let[r,o]=n;return r.startsWith(t)&&(e[r.slice(t.length)]=o),e}),{})})(i)),[i]);return r().createElement("div",{className:a},null!=l?l:r().createElement("div",{style:_o},r().createElement($r,{id:t.id+"-dropdown",containerRootClassName:Xr,menuOrientation:o,parentType:"wixui.Menu",parentStylableClassName:"",containerProps:{containerLayoutClassName:t.id+"-container",hasOverflow:!1,overlowWrapperClassName:t.id+"-overflow-wrapper"}},(()=>r().createElement(jr,{id:t.id+"-submenu",partToPreviewStateMap:s})))))},Oo=e=>r().createElement(wr.Provider,{value:{onEscKeyDown:e.onEscKeyDown,item:e.item,currentItem:e.currentItem,dropdownAnimationState:e.dropdownAnimationState}},r().createElement(Lo,{className:En.horizontalDropdownDisplayWrapper,children:e.children,item:e.item,menuOrientation:"horizontal"})),Ao=e=>r().createElement(wr.Provider,{value:{onEscKeyDown:e.onEscKeyDown,item:e.item,currentItem:e.currentItem,dropdownAnimationState:e.dropdownAnimationState}},r().createElement(Lo,{item:e.item,menuOrientation:"vertical",className:Nt(En.verticalDropdownDisplayWrapper,e.isOpen&&En.expandedDropdown),partToPreviewStateMap:e.partToPreviewStateMap}));var Do=e=>{const{id:t,onItemMouseIn:n,onItemMouseOut:o,onItemClick:a,onItemDblClick:l,containerRootClassName:i}=e,{isHamburgerMenuOpened:s}=gr();return s?r().createElement(yr,{id:t,className:i,onItemMouseIn:n,onItemMouseOut:o,onItemClick:a,onItemDblClick:l,HorizontalDropdown:Oo,VerticalDropdown:Ao}):null},Bo="c_pphB";const Ro=e=>{const t=document.getElementById(e);return t&&getComputedStyle(t).getPropertyValue("--animation-name")||b};var To=e=>{const{id:r,customClassNames:o=[],children:a,compPreviewState:l,containerProps:i,containerRootClassName:s,hasResponsiveLayout:c,shouldFocus:d,isMenuOpen:m,updateComponentPropsInViewer:p}=e,{setIsHamburgerMenuOpened:b}=gr(),{animationState:h,initEnterAnimation:f,initExitAnimation:g}=u(U,X),{shouldOmitWrapperLayers:v}=null!=i?i:{},y="function"==typeof a?a:()=>a;n.useEffect((()=>{void 0!==m&&b&&(null==b||b(m))}),[m,b]);const w=n.useCallback((e=>{if(m===e)return;const t=(()=>{const e=Ro(r);return K[e].hamburgerMenuAnimationName})();e?(p({isMenuOpen:!0}),f(t)):g(t,(()=>p({isMenuOpen:!1})))}),[m,p,r,f,g]);return n.createElement("div",t()({id:r},qe(e),{className:Nt(Oe(null,...o),s,v&&Bo)}),n.createElement(me,{compPreviewState:l,shouldFocus:d,isMenuOpen:m,setIsMenuOpen:w,animationState:h},c?n.createElement($t,t()({},i,{tabIndex:-1,extraRootClass:Bo}),y):y()))},Po=ye((e=>{let{mapperProps:t,controllerUtils:n,stateValues:r}=e;const{editorType:o}=r;return{...t,editorType:o,updateComponentPropsInViewer:e=>{n.updateProps(e)}}}));var zo={root:"hamburger-open-button",buttonLabel:"hamburger-open-button__label",buttonIcon:"hamburger-open-button__icon"};const Ho=(e,o)=>{let{id:a,a11y:l={},className:i,classes:s,StylableButton:c,ariaLabel:u,onClick:d,...m}=e;const p=r().useRef(null),{isMenuOpen:b,shouldFocus:h,setIsMenuOpen:f}=de();(0,n.useImperativeHandle)(o,(()=>({focus:()=>{var e;return null==(e=p.current)?void 0:e.focus()},blur:()=>{var e;return null==(e=p.current)?void 0:e.blur()}}))),(0,n.useEffect)((()=>{var e;h&&!1===b&&(null==(e=p.current)||e.focus())}),[h,b]);return r().createElement("nav",{"aria-label":u,className:s.nav},r().createElement(c,t()({},m,{className:he()(s.hamburgerOpenButtonRoot,s.stylableButtonRoot,i),a11y:{...l,expanded:b||!1,haspopup:"dialog"},id:a,onClick:e=>{f(!b),null==d||d(e)},ref:p,semanticClassNames:zo})))};var Fo=r().forwardRef(Ho),Vo=Me,Wo=o(2901);const jo=e=>{const t={...e,classes:{...Wo.classes,hamburgerOpenButtonRoot:(0,Wo.st)(Wo.classes.root),stylableButtonRoot:(0,Wo.st)(_e.classes.root)},StylableButton:kt};return r().createElement(Fo,t)};var Zo=r().forwardRef(jo),Uo={root:"rvGLLm",hasBorderColor:"jMYm0H",hasBackgroundColor:"y061lW",hasHoverBorderColor:"mxKpu4",hasHoverBackgroundColor:"zBIMAq",hasDisabledBorderColor:"lPvoIB",hasDisabledBackgroundColor:"by2yNJ",label:"hQsOje",hasColor:"xNSufk",hasHoverColor:"ogDVcg",hasDisabledColor:"IfOI50",link:"I56lbS",container:"pMSuoq",icon:"xyuOGi",hasIconColor:"Pczkxq",hasHoverIconColor:"GRPSsV",hasDisabledIconColor:"hGEkMI",nav:"jMLWKi"};const Ko=e=>{const t={...e,classes:{...Uo,hamburgerOpenButtonRoot:Uo.root},StylableButton:Ot};return r().createElement(Fo,t)};var qo=r().forwardRef(Ko),$o=o(95561);const Xo="hamburger-overlay-dialog",Go="hamburger-overlay-root";var Jo=e=>{const{id:o,children:a,hasResponsiveLayout:l,containerProps:i,meshProps:s,hideFromDOM:c,tapOutsideToClose:u,onOpenStateChange:d,ariaLabel:m,onClick:p,onDblClick:b,onMouseEnter:h,onMouseLeave:f,isMenuContainerFullscreen:g,rootClassName:v,overlayClassName:y,scrollContentClassName:w}=e,{isMenuOpen:C=!1,setIsMenuOpen:k,menuContainerRef:x,setIsMenuContainerFullscreen:E,shouldFocus:S,animationState:M}=de(),{menuStyleId:I}=gr(),{containerRef:N}=function(e,t){const r=(0,n.useRef)(null),o=(0,n.useRef)(null),a=e=>{const t=r.current,n=l(t);if("Tab"!==e.key)return;const o=n[0],a=n[n.length-1];e.shiftKey&&document.activeElement===o?(e.preventDefault(),a.focus()):e.shiftKey||document.activeElement!==a||(e.preventDefault(),o.focus())},l=e=>Array.from(e.querySelectorAll('\n  button, [href], input, select, textarea, summary,\n  [tabindex]:not([tabindex="-1"]), [contenteditable="true"]\n')).filter((t=>{const n=getComputedStyle(t);return(t!==e||e.hasAttribute("tabindex"))&&"none"!==n.display&&"hidden"!==n.visibility}));return(0,n.useLayoutEffect)((()=>{const n=r.current;if(e&&n){if(o.current=document.activeElement,null!=t&&t.onFocus)t.onFocus(n);else{const e=l(n);if(!(e.length>0))return;(e=>{var t;null==(t=e[0])||t.focus()})(e)}return n.addEventListener("keydown",a),()=>{o.current instanceof HTMLElement&&o.current.focus(),n.removeEventListener("keydown",a)}}}),[e,null==t?void 0:t.onFocus]),{containerRef:r}}(C,{onFocus:e=>{if(!S)return;const t=()=>{var t;const n=null==x?void 0:x.current,r=null!=(t=null==n?void 0:n.querySelector('a[href], button, input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'))?t:e;r&&r.focus()};"none"===M.name?setTimeout(t,0):"enterDone"===M.phase&&t()}}),_=(0,n.useMemo)((()=>I?e=>r().createElement("div",{id:I},e.children):r().Fragment),[I]),L=r().createElement("div",{"data-hook":Xo,"aria-hidden":!0,className:y});(0,n.useEffect)((()=>{d(C)}),[C,d]),(0,n.useEffect)((()=>()=>{d(!1)}),[]),(0,n.useEffect)((()=>{E(g)}),[g,E]);const O=(0,n.useCallback)((()=>k(!1)),[k]),A=(()=>{const[e,t]=(0,n.useState)(null);return(0,n.useLayoutEffect)((()=>{t(document.getElementById("masterPage"))}),[]),e})(),D=(0,n.useCallback)((e=>{var t;null==p||p(e);const n=e.target,r=null==n?void 0:n.closest("a"),o=!(null!=n&&n.closest("#"+(null==x||null==(t=x.current)?void 0:t.id)));(r||u&&o)&&O()}),[O,x,p,u]),B="function"==typeof a?a:()=>a,R=()=>c&&!C?null:r().createElement(_,null,r().createElement("div",t()({id:o,className:v,role:"dialog","aria-modal":"true","aria-label":m,"data-visible":C,onKeyDown:e=>"Escape"===e.key&&O(),onClick:D,onDoubleClick:b,onMouseEnter:h,onMouseLeave:f,"data-hook":Go,ref:N,tabIndex:-1,"data-part":ie,"data-animation-name":M.name,"data-animation-state":M.phase},qe(e)),l?r().createElement(r().Fragment,null,L,r().createElement($t,t()({},i,{tabIndex:-1}),B)):r().createElement("div",{className:w},L,r().createElement(Pt,t()({id:o},s),B))));return A?(0,$o.createPortal)(R(),A):R()},Qo=ye((e=>{let{mapperProps:t,stateValues:n}=e;const{compId:r}=t,{setSiteScrollingBlocked:o}=n;return{...t,onOpenStateChange:e=>{o(e,r)}}})),Yo=o(15535),ea="scemAM";var ta={root:"hamburger-overlay"};var na=e=>{const{containerRootClassName:n,showBackgroundOverlay:o,hasResponsiveLayout:a,isMenuContainerFullscreen:l,stylableClassName:i,customClassNames:s=[]}=e,{isMenuOpen:c=!1}=de(),u=o&&a||l,d=(0,Yo.st)(n,{showBackgroundOverlay:o,isMenuOpen:c,shouldScroll:!a},Yo.classes.root,ea),m=(0,Yo.st)(Yo.classes.overlay,{},u?i:void 0,o&&a?Oe(ta.root,...s):void 0);return r().createElement(Jo,t()({},e,{rootClassName:d,overlayClassName:m,scrollContentClassName:(0,Yo.st)(Yo.classes.scrollContent)}))},ra="tVir9C",oa="ZTgfG4",aa="meddft",la="Ujf5u0",ia="XTKAK7",sa="IKAmsN";var ca=e=>{const{containerRootClassName:n,showBackgroundOverlay:o,hasResponsiveLayout:a,customClassNames:l=[]}=e,{isMenuOpen:i=!1}=de(),s=Nt(n,o&&la,i&&aa,!a&&ia,ra,ea),c=Nt(oa,o&&a&&Oe(ta.root,...l));return r().createElement(Jo,t()({},e,{rootClassName:s,overlayClassName:c,scrollContentClassName:sa}))};var ua={root:"box"};const da="mesh-container-content",ma="inline-content",pa=e=>r().createElement("div",{key:e.props.id+"-rotated-wrapper","data-mesh-id":e.props.id+"-rotated-wrapper"},e),ba=(e,n)=>{const{id:o,className:a,wedges:l=[],rotatedComponents:i=[],children:s,fixedComponents:c=[],extraClassName:u="",renderRotatedComponents:d=pa}=e,m=r().Children.toArray(s()),p=[],b=[];m.forEach((e=>c.includes(e.props.id)?p.push(e):b.push(e)));const h=(e=>{const{wedges:t,rotatedComponents:n,childrenArray:o,renderRotatedComponents:a}=e,l=n.reduce(((e,t)=>({...e,[t]:!0})),{});return[...o.map((e=>{return l[(t=e,t.props.id.split("__")[0])]?a(e):e;var t})),...t.map((e=>r().createElement("div",{key:e,"data-mesh-id":e})))]})({childrenArray:b,rotatedComponents:i,wedges:l,renderRotatedComponents:d});return r().createElement("div",t()({},qe(e),{"data-mesh-id":o+"inlineContent","data-testid":ma,className:Nt(a,u),ref:n}),r().createElement("div",{"data-mesh-id":o+"inlineContent-gridContainer","data-testid":da},h),p)};var ha=r().forwardRef(ba),fa="J6KGih";const ga="container-bg",va=(e,r)=>{const{id:o,className:a,meshProps:l,renderSlot:i,children:s,onClick:c,onKeyPress:u,onDblClick:d,onFocus:m,onBlur:p,onMouseEnter:b,onMouseLeave:h,translations:f,hasPlatformClickHandler:g,a11y:v={},ariaAttributes:y={},tabIndex:w,role:C,style:k,lang:x}=e,E=n.useRef(null),{"aria-label-interactions":S,...M}=v;S&&(M["aria-label"]=(null==f?void 0:f.ariaLabel)||"Interactive element, focus to trigger content change");const I={id:o,children:s,...l},N=Nt(a,{[fa]:g});return n.useImperativeHandle(r,(()=>({focus:()=>{var e;null==(e=E.current)||e.focus()},blur:()=>{var e;null==(e=E.current)||e.blur()}}))),n.createElement("div",t()({id:o},qe(e),{ref:E,className:N,onClick:c,onKeyDown:e=>{u&&(" "===e.key&&e.preventDefault(),u(e))},onFocus:m,onBlur:p,onDoubleClick:d,onMouseEnter:b,onMouseLeave:h,style:k,lang:x},M,(({role:e,tabIndex:t,tabindex:n,...r}={})=>{const o=Object.entries(r).reduce(((e,[t,n])=>({...e,[`aria-${t}`.toLowerCase()]:n})),{role:e,tabIndex:t??n});return Object.keys(o).forEach((e=>{void 0!==o[e]&&null!==o[e]||delete o[e]})),He(o,ze)})({...y,tabIndex:w,role:C})),i({containerChildren:n.createElement(ha,I)}))},ya=n.forwardRef(va),wa=(e,r)=>{let{classes:o,className:a,customClassNames:l=[],...i}=e;return n.createElement(ya,t()({},i,{ref:r,className:Nt(o.root,a),renderSlot:e=>{let{containerChildren:t}=e;return n.createElement(n.Fragment,null,n.createElement("div",{className:Nt(o.bg,Oe(ua.root,...l)),"data-testid":ga}),t)}}))},Ca=n.forwardRef(wa);var ka={root:"KaEeLN",bg:"uYj0Sg"};const xa=(e,r)=>n.createElement(Ca,t()({ref:r},e,{classes:ka}));var Ea=n.forwardRef(xa),Sa=o(97798),Ma=o(93260),Ia=o.n(Ma),Na=o(29359),_a=o.n(Na),La=o(20435);const Oa=[Ia(),_a(),_a()],Aa=e=>{let{depth:t,isStretched:n=!1,containsChildren:r=!1,hasColumnSubSubs:o=!1}=e;const{st:a,classes:l}=Oa[t]||Ia(),i={isStretched:n,isColumn:!0},s=(0,La.st)(La.classes.root,Sa.classes.columnsLayout);return r?{positionBox:a(l.positionBox,i,n?Sa.classes.containerPositionBox:""),animationBox:l.animationBox,alignBox:a(Sa.classes.megaMenuWrapper,n?La.classes.containerPageStretchWrapper:""),megaMenuComp:a(l.megaMenuComp,s)}:0===t?{positionBox:a(l.positionBox,i,n?Sa.classes.positionBox:""),animationBox:a(s,l.animationBox),alignBox:a(l.alignBox,La.classes.pageWrapper,n?La.classes.pageStretchWrapper:La.classes.overrideWidth),list:a(l.list,La.classes.listWrapper),subItem:o?La.classes.heading:La.classes.menuItem}:{hasSubItems:La.classes.rowItem,positionBox:l.positionBox,alignBox:l.alignBox,list:l.list,subItem:La.classes.menuItem}},Da=e=>e.replace("__root",""),Ba=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.reduce(((t,n)=>({...t,["--"+n]:"var(--"+e+"-"+n+")"})),{})};const Ra="containerMarginTop",Ta="horizontalSpacing",Pa=e=>t=>{if(0!==t)return{};const n=Da(e);return{list:Ba(n,Ta),megaMenuComp:Ba(n,Ra)}},za=(e,t)=>(0,n.useMemo)((()=>{switch(e){case"wixui.Menu":return{className:En.megaMenuComp,cssVars:{}};case"wixui.StylableHorizontalMenu":const e=Aa({depth:0,containsChildren:!0}),n=Pa(t)(0);return{className:e.megaMenuComp,cssVars:n.megaMenuComp};default:return{className:"",cssVars:{}}}}),[e,t]),Ha=(0,n.createContext)({isOpen:void 0,setIsOpen:()=>{}}),Fa=()=>(0,n.useContext)(Ha),Va=e=>{let{children:t,isOpen:o,setIsOpen:a,labelRef:l}=e;const i=(0,n.useMemo)((()=>({isOpen:o,setIsOpen:a,labelRef:l})),[o,a,l]);return r().createElement(Ha.Provider,{value:i},t)};var Wa=e=>{const{id:r,children:o,meshProps:a,parentType:l,parentStylableClassName:i}=e,s="function"==typeof o?o:()=>o,{className:c,cssVars:u}=za(l,i),{isOpen:d,setIsOpen:m,labelRef:p}=Fa();return n.createElement(Ea,t()({},qe(e),{id:r,hasPlatformClickHandler:!1,meshProps:a,className:c,style:u,onMouseLeave:e=>{d&&e.relatedTarget!==(null==p?void 0:p.current)&&m(!1)}}),s)};var ja=e=>{const{id:r,children:o,containerRootClassName:a,parentType:l,parentStylableClassName:i}=e,s="function"==typeof o?o:()=>o,{className:c,cssVars:u}=za(l,i),{isOpen:d,setIsOpen:m,labelRef:p}=Fa();return n.createElement("div",t()({id:r},qe(e),{className:Nt(a,c),style:u,onMouseLeave:e=>{d&&e.relatedTarget!==(null==p?void 0:p.current)&&m(!1)}}),n.createElement($t,e.containerProps,s))},Za="kvKptz";var Ua=e=>{var t,o;const{id:a,containerRootClassName:l,containerProps:i,customClassNames:s=[],children:c,slots:u,items:d,partToPreviewStateMap:m,currentUrl:p,activeAnchor:b,onItemMouseIn:h,onItemMouseOut:f,onItemClick:g,onItemDblClick:v,onMouseEnter:y,onMouseLeave:w,translations:C,navAriaLabel:k,a11y:x,ariaAttributes:E,lang:S,className:M}=e,I=Fe({ariaAttributes:{...x,...E}}),N=(0,n.useMemo)((()=>u?d.map((e=>({...e,children:e.slot&&u[e.slot]}))):d),[d,u]),_=(0,n.useCallback)((()=>Ro(a)),[a]),[L,O]=r().useState(!1);return r().createElement("div",{id:a,className:Nt(l,M),onMouseEnter:y,onMouseLeave:w,lang:S},r().createElement(vr,{items:N,partToPreviewStateMap:m,currentUrl:p,activeAnchor:b,translations:C,menuStyleId:"portal-"+a,customClassNames:s,getAnimationPackage:_,navAriaLabel:null!=(t=null!=(o=null==E?void 0:E.label)?o:null==x?void 0:x.label)?t:k,isHamburgerMenuOpened:L,setIsHamburgerMenuOpened:O,a11yProps:I.ariaAttributes},r().createElement($t,i,(function(){return r().createElement(r().Fragment,null,!L&&r().createElement(yr,{id:a+"-menu-content",className:Za,onItemMouseIn:h,onItemMouseOut:f,onItemClick:g,onItemDblClick:v,HorizontalDropdown:Oo,VerticalDropdown:Ao}),c(...arguments))}))))};var Ka=ye((e=>{let{mapperProps:t,stateValues:n}=e;const{currentUrl:r}=n;return{...t,currentUrl:r}}));var qa="NZHLsZ",$a="ffrP7D",Xa="mfxFLH";const Ga=()=>{const e=[];let t,n=!1,r=null;const o=["a[href]","area[href]","audio[controls]","button",'input[type="button"]','input[type="checkbox"]','input[type="file"]','input[type="image"]','input[type="radio"]','input[type="range"]','input[type="reset"]','input[type="submit"]',"video[controls]"].join(", "),a=["a[href]","audio[controls]","button","details","embed","iframe","img[usemap]",'input:not([type="hidden"])',"label","select","textarea","video[controls]"].join(", ");function l(t){n&&i(t)===r&&(t.preventDefault(),t.stopPropagation(),t.stopImmediatePropagation(),e.unshift(t))}function i(e){return e.target}function s(e){return e.matches("button, input, select, textarea, progress, meter, output")||e.constructor.formAssociated}function c(t){setTimeout((()=>{for(n=!1,r=null,t.focus();e.length>0;){const t=e.pop();t.target.dispatchEvent(new MouseEvent(t.type,t))}}),0)}function u(e){const l=i(e),u=l.closest("label"),d=l.closest(a),m=l.closest(o);let p=null,b=null,h=!1;if(u){const e=u.getAttribute("for");u!==d&&u.contains(d)&&d.contains(m)?b=m:null!=e?(p=""!==e?document.querySelector("#"+CSS.escape(e)):null,p&&s(p)&&(h=!0,b=p,r=p)):(p=Array.from(u.querySelectorAll("*")).find(s),p?(h=!0,b=p,r=p):m&&(b=m,r=l))}else m&&(b=m,r=l);b&&function(e){const t=e.matches('button, input:not([type="hidden"]), select, textarea, a[href], area[href], audio[controls], video[controls]')||e.constructor.formAssociated,n=!Number.isNaN(parseInt(e.getAttribute("tabindex"),10))||["plaintext-only","true"].includes(e.contentEditable),r=function(e){return e.matches("button, fieldset, optgroup, option, select, textarea, input")||e.constructor.formAssociated}(e)&&(e.disabled||e.closest("fieldset:disabled")),o=e.closest("[inert]"),a=!function(e){let t=e,n=!1;for(;t;){if("visible"===t.style.visibility&&(n=!0),"none"===t.style.display||"hidden"===t.style.contentVisibility||!n&&"hidden"===t.style.visibility)return!1;t=t.parentElement}return!0}(e);return(t||n)&&!(r||o||a)}(b)&&(b===document.activeElement?e.preventDefault():(n=!0,h?t=c.bind(null,b):c(b)))}function d(){"function"==typeof t&&(t(),t=null)}if(/apple/i.test(navigator.vendor))return window.addEventListener("mousedown",u,{capture:!0}),window.addEventListener("click",d,{capture:!0}),window.addEventListener("mouseup",l,{capture:!0}),window.addEventListener("click",l,{capture:!0}),()=>{window.removeEventListener("mousedown",u,{capture:!0}),window.removeEventListener("click",d,{capture:!0}),window.removeEventListener("mouseup",l,{capture:!0}),window.removeEventListener("click",l,{capture:!0})}},Ja=e=>"menuItemDepth"+e,Qa="positionBox",Ya="scrollPageToTheLeft",el="scrollPageToTheRight",tl=[Ia(),_a(),_a()];var nl=o(93067),rl=e=>{let{isIconShown:t}=e;const n=t?"10":"0",o=t?"0 0 16 11":"0 0 0 0";return r().createElement("svg",{width:n,height:n,viewBox:o,fill:"black",xmlns:"http://www.w3.org/2000/svg"},r().createElement("path",{d:"M8 10.5L16 1.86193L14.7387 0.5L8 7.77613L1.26133 0.499999L-5.95321e-08 1.86193L8 10.5Z"}))},ol=o(18805);const al=(e,t)=>{const n=()=>e(!1);return{onFocus:()=>{t(!0),n()},onKeyDown:r=>{" "===r.key||"Enter"===r.key?e((e=>!e)):"Escape"===r.key?il(r):r.shiftKey&&"Tab"===r.key&&(n(),t(!1))}}},ll=(e,t)=>{const n=()=>e(!1);return{onMouseEnter:()=>e(!0),onMouseLeave:n,onBlur:e=>{var r;!e.relatedTarget||e.currentTarget.contains(e.relatedTarget)||null!=(r=e.relatedTarget)&&r.contains(e.currentTarget)||(n(),t(!1))},onKeyDown:e=>{"Escape"===e.key&&(n(),il(e),e.stopPropagation())}}},il=e=>{const{currentTarget:t}=e,n=t.getAttribute("data-item-depth");if(null!==n){var r;const e=rn(parseInt(n,10)-1),o=t.closest(e),a=null==o||null==(r=o.firstChild)?void 0:r.children[1],l=null==a?void 0:a.firstChild;null==l||l.focus()}},sl=(e,t,n)=>n&&(r=>{null==n||n(r,{...e,selected:t})}),cl=e=>{var o;let{item:a,className:l,withSubItemsClassname:i,isCurrentItem:s,depth:c,isStretched:u,hasColumnSubSubs:d,positionUpdaters:m,positionBoxRef:p,children:b,onItemClick:h,onItemDblClick:f,onItemMouseIn:g,onItemMouseOut:v}=e;const[y,w]=(0,n.useState)(!1),[C,k]=(0,n.useState)(!1),[x,E]=(0,n.useState)(!1),S=(0,ol.d)(x),M=(0,n.useRef)(null),{label:I,link:N,forceHovered:_=!1}=a,L=ll(w,E),O=al(w,E),A=d&&1===c,D=(e=>{let{depth:t,isHovered:n,isCurrentPage:r,className:o}=e;const{st:a,classes:l}=tl[t]||Ia(),i=0!==t;return{root:a(l.root,{isHovered:n&&!r,isCurrentPage:r},o),itemWrapper:a(l.itemWrapper,i?"":Oe(Dn.menuItemWrapper)),container:l.container,label:a(l.label,i?"":Oe(Dn.menuItemLabel))}})({depth:c,isHovered:!A&&y,isCurrentPage:s,className:l}),B=m[c];(0,n.useEffect)((()=>{if(!(y&&M.current&&p.current&&B))return;const{onEnter:e,onLeave:t}=B({label:M.current,positionBox:p.current,isStretched:u});return e(),k(!0),()=>{t(),k(!1)}}),[y,u,p,B,a]),(0,n.useEffect)((()=>{w(_)}),[_]),(0,n.useEffect)((()=>{const e=()=>{S.current&&(E(!1),w(!1))};return window.addEventListener("mousemove",e),()=>{window.removeEventListener("mousemove",e)}}),[]);const R=((null==(o=a.items)?void 0:o.length)||a.slot)&&!A;return r().createElement("li",t()({className:Nt(D.itemWrapper,i),"data-testid":Ja(c),"data-item-depth":c,"data-is-current":s,"aria-current":s},y&&{"data-hovered":!0},C&&{"data-shown":!0},L),r().createElement("div",{className:(0,nl.st)(nl.classes.rootContainer,{isRow:R})},r().createElement(Ge,t()({},N,{className:Nt(D.root,nl.classes.menuItem),ref:M,activateByKey:"Enter",onClick:sl(a,s,h),onMouseEnter:sl(a,s,g),onMouseLeave:sl(a,s,v),onDoubleClick:sl(a,s,f)},!!b&&{"aria-haspopup":!0,"aria-expanded":y},!!b&&!(null!=N&&N.href)&&{role:"button"},{tabIndex:0,"data-item-label":!0}),r().createElement("div",{className:D.container},r().createElement("span",{className:D.label},I))),R&&r().createElement("div",{className:(0,nl.st)(nl.classes.accessibilityIconWrapper,{isIconShown:x,isTopLevel:0===c})},r().createElement("button",t()({tabIndex:0,className:(0,nl.st)(nl.classes.accessibilityIcon,{isIconShown:x,isOpen:C})},O,{"aria-label":"Toggle "+I}),r().createElement(rl,{isIconShown:x})))),r().createElement(Va,{labelRef:M,isOpen:y,setIsOpen:w},b))},ul=(0,n.memo)((e=>{const{item:o,depth:a=0,currentItem:l,className:i,submenuProps:{positionUpdaters:s,getClasses:c,injectCssVars:u},slots:d,isColumnStretched:m,isContainerStretched:p,hasColumnSubSubs:b,onItemClick:h,onItemMouseIn:f,onItemMouseOut:g,onItemDblClick:v}=e,{label:y,items:w,slot:C}=o,k=C&&d[C],x=(0,n.useRef)(null),E=k?p:m,S=c({depth:a,isStretched:E,containsChildren:!!k,hasColumnSubSubs:b}),M=null==u?void 0:u(a);return r().createElement(cl,{className:i,item:o,isCurrentItem:Mn(o,l),depth:a,isStretched:E,hasColumnSubSubs:b,withSubItemsClassname:null!=w&&w.length?S.hasSubItems:"",positionUpdaters:s,positionBoxRef:x,onItemClick:h,onItemDblClick:v,onItemMouseIn:f,onItemMouseOut:g},((null==w?void 0:w.length)||k)&&r().createElement("div",{className:S.positionBox,ref:x,role:"group","aria-label":y,"data-testid":Qa},r().createElement("div",{className:S.animationBox,style:null==M?void 0:M.animationBox},r().createElement("div",{className:S.alignBox},k?r().createElement(wr.Provider,{value:{item:o,currentItem:l}},k):r().createElement("ul",{className:S.list,style:null==M?void 0:M.list},w.map(((n,o)=>r().createElement(ul,t()({},e,{key:o,item:n,depth:a+1,className:S.subItem,slots:d})))))))))})),dl=(e,o)=>{const{id:a,items:l,submenuProps:i,menuWrapper:s,className:c,customClassNames:u=[],stylableClassName:d,containerRootClassName:m,menuMode:p,submenuMode:b,isContainerStretched:h,style:f,slots:g,isQaMode:v,fullNameCompType:y,currentUrl:w,onMouseEnter:C,onMouseLeave:k,onItemMouseIn:x,onItemMouseOut:E,onItemClick:S,onItemDblClick:M,reportBiOnMenuItemClick:I,ariaAttributes:N,role:_,activeAnchor:L,a11y:O={},screenReader:A,navigationHint:D,lang:B}=e,R=(0,Sa.st)(Sa.classes.root,{menuMode:p},d,m,Oe(Dn.root,...u)),T=(0,n.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];null==I||I(...t),null==S||S(...t)}),[I,S]),P=(0,n.useMemo)((()=>Sn(l,L,w)),[l,L,w]),z=Ve({ariaAttributes:{...O,label:O.label,...N},role:_});return(0,n.useEffect)((()=>{if(Ke())return Ga()}),[]),r().createElement("div",t()({id:a,className:(0,Sa.st)(c,qa,Xa),style:f},qe(e),$e(v,y),{onMouseEnter:C,onMouseLeave:k,lang:B}),r().createElement(s,t()({ref:o,items:l,className:R,currentItem:P,screenReaderHintElement:null!=A&&A.hasHint?r().createElement("div",{className:$a},D):null},z),l.map(((e,t)=>{const n="flyout"!==b||e.slot?"column":"flyout";return r().createElement(ul,{key:t,depth:0,item:e,currentItem:P,className:Sa.classes.menuItem,isContainerStretched:h,isColumnStretched:"columnStretched"===b,submenuProps:i[n],onItemClick:T,onItemMouseIn:x,onItemMouseOut:E,onItemDblClick:M,hasColumnSubSubs:"column"===n&&(o=e,!!o.items?.some((e=>!!e.items?.length))),slots:g});var o}))))};var ml=r().forwardRef(dl);const pl=(e,r)=>{let{className:o,children:a,ref:l,items:i,currentItem:s,screenReaderHintElement:c,...u}=e;const d=n.useRef(null);return n.useImperativeHandle(r,(()=>({focus:()=>{var e;null==(e=d.current)||e.focus()},blur:()=>{var e;null==(e=d.current)||e.blur()}}))),n.createElement("nav",t()({tabIndex:-1,className:o,ref:d},u),c,n.createElement("ul",{className:Sa.classes.menu},a))},bl=n.forwardRef(pl),hl=[Ia(),_a(),_a()],fl=e=>{let{depth:t}=e;const{st:n,classes:r}=hl[t]||Ia(),o=(0,La.st)(La.classes.root,Sa.classes.columnsLayout);return{positionBox:r.positionBox,animationBox:n(r.animationBox,o),alignBox:n(La.classes.pageWrapper,La.classes.overrideWidth),list:r.list,subItem:La.classes.menuItem}},gl=function(e,t,n,r){void 0===r&&(r="");const o="number"==typeof n?Math.round(n):n;e.style.setProperty(t,""+o+r)},vl=(e,t)=>window.getComputedStyle(e).getPropertyValue(t),yl=e=>{e.removeAttribute("style")},wl=function(e,t,n){let{positionBox:r,label:o}=e;void 0===n&&(n=0);const a=r.getBoundingClientRect().height,l=o.getBoundingClientRect(),i=window.innerHeight-l.bottom,s=i>=a,c=l.top-(()=>{const e=window.getComputedStyle(document.documentElement).getPropertyValue("--wix-ads-height");return Number.parseInt(e,10)||0})()>=a;if(s)gl(r,"margin-top",-n,"px");else if(c){const e=a+l.height;gl(r,"margin-top",-e-n,"px")}else"column"===t&&(gl(r,"margin-top",-n,"px"),gl(r,"--animation-box-max-height",i,"px"),gl(r,"--animation-box-overflow-y","scroll"))},Cl=e=>{let{label:t,positionBox:n}=e;const{bottom:r,left:o,width:a}=t.getBoundingClientRect(),{top:l,left:i}=n.getBoundingClientRect(),s=Math.round(l-n.offsetTop),c=o-Math.round(i-n.offsetLeft);gl(n,"top",r-s,"px"),gl(n,"min-width",a,"px");const u=n.offsetWidth,d=(e=>vl(e.firstChild,"--flyoutAlignment").trim())(n);if("center"===d)return void gl(n,"left",c+(a-u)/2,"px");const m="right"===d?c+a-u<0:c+u<=document.body.offsetWidth;gl(n,"left",m?c:c+a-u,"px")},kl=e=>{let{label:t,positionBox:n}=e,r=()=>{};return{onEnter:()=>{var e;r=Ze((()=>Cl({label:t,positionBox:n})),50),r(),wl({label:t,positionBox:n},"flyout"),window.addEventListener("scroll",r),null==(e=t.closest("nav"))||e.addEventListener("scroll",r)},onLeave:()=>{var e;window.removeEventListener("scroll",r),null==(e=t.closest("nav"))||e.removeEventListener("scroll",r),yl(n)}}},xl="data-reverted",El=e=>["--subsubmenu-box-left:"+(e?"unset":"100%"),"--subsubmenu-box-right:"+(e?"100%":"unset")],Sl=e=>{const{paddingTop:t,paddingLeft:n,paddingRight:r,borderTopWidth:o}=window.getComputedStyle(e);return["padding-left:"+n,"padding-right:"+r,"margin-top:-"+(Number.parseInt(t,10)+Number.parseInt(o,10))+"px"]},Ml=e=>{let{positionBox:t,label:n}=e;return{onEnter:()=>{const e=on(t);((e,t,n)=>{const{left:r,right:o}=e.getBoundingClientRect();return n?r-t.offsetWidth<0:o+t.offsetWidth>document.body.offsetWidth})(n,t,e)&&t.setAttribute(xl,""),t.style.cssText=[...El(e),...Sl(t.firstChild)].join(";")},onLeave:()=>{t.removeAttribute(xl),t.removeAttribute("style")}}},Il=e=>t=>{if(0!==t)return{};const n=Da(e);return{animationBox:Ba(n,"flyoutAlignment")}},Nl=e=>({flyout:{positionUpdaters:[kl,Ml],getClasses:fl,injectCssVars:Il(e)}}),_l=(e,t)=>(0,n.useMemo)((()=>Nl(e)),[e,t]),Ll=e=>t=>{let{label:n,positionBox:r,isStretched:o}=t;return{onEnter:()=>{const t="wrap"===e?vl(n,"margin-bottom"):"0";if(wl({label:n,positionBox:r},"column",parseInt(t,10)),o){var a;const{left:e=0,right:t=0}=(null==(a=n.closest("nav"))?void 0:a.getBoundingClientRect())||{};gl(r,"right",-(document.body.clientWidth-t),"px"),gl(r,"left",-e,"px"),(e=>{Ke()&&(gl(e,"display","none"),e.offsetHeight,gl(e,"display",""))})(r)}},onLeave:()=>{yl(r)}}},Ol=(e,t)=>({column:{positionUpdaters:[Ll(t)],getClasses:Aa,injectCssVars:Pa(e)}}),Al=(e,t)=>(0,n.useMemo)((()=>Ol(e,t)),[e,t]),Dl=(e,t)=>(0,n.useMemo)((()=>((e,t)=>({...Ol(e,t),...Nl(e)}))(e,t)),[e,t]),Bl=(e,n)=>r().createElement(ml,t()({},e,{ref:n,menuWrapper:bl,submenuProps:Dl(e.stylableClassName,e.menuMode)}));var Rl=r().forwardRef(Bl),Tl=o(57683);var Pl=e=>{const{className:t,onClick:n,side:o,isHidden:a,dataTestId:l}=e,i=(0,Tl.st)(Tl.classes.root,{side:o,isVisible:!a},Sa.classes.scrollButton,t);return r().createElement("div",{onClick:n,"aria-hidden":"true","aria-label":"scroll",className:i,"data-menu-scroll-action":"page","data-testid":l,"data-hidden":a},r().createElement("span",{className:Tl.classes.icon},r().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 6 12"},r().createElement("path",{d:"M6 6L.8 0 0 .7 4.7 6 0 11.3l.8.7z"}))))},zl=o(23161);const Hl=e=>{const{isScrollLeftButtonShown:t,isScrollRightButtonShown:n,scrollPageToTheRight:o,scrollPageToTheLeft:a}=e;return r().createElement("div",{className:(0,zl.st)(zl.classes.root)},r().createElement(Pl,{side:"left",onClick:a,isHidden:!t,dataTestId:Ya}),r().createElement(Pl,{side:"right",onClick:o,isHidden:!n,dataTestId:el}))},Fl=(e,r)=>{let{items:o,currentItem:a,children:l,className:i,screenReaderHintElement:s,ref:c,...u}=e;const d=n.useRef(null),m=n.useRef(null),[p,b]=n.useState(!1),[h,f]=n.useState(!1),[g,v]=n.useState(!1),[y,w]=n.useState(-1);n.useImperativeHandle(r,(()=>({focus:()=>{var e;null==(e=d.current)||e.focus()},blur:()=>{var e;null==(e=d.current)||e.blur()}})));const C=n.useCallback((()=>je((()=>{const{current:e}=d;if(!e)return;const{scrollLeft:t,scrollWidth:n,clientWidth:r}=e,o=r<n;b(o),f(o&&t>fn),v(o&&t<n-r-fn)}),100)()),[]);n.useEffect(C,[]);const k=C,x=n.useCallback((e=>{const{current:t}=d;if(!t||!p||-1===e)return;w(e);const n=Array.from(t.querySelectorAll(dn))[e];n&&yn(t,n)}),[p]);n.useEffect((()=>{const e=o.find((e=>e.forceHovered))||a,t=e?o.indexOf(e):-1;t!==y&&x(t)}),[o,y,x,a]);return hr({ref:d,callback:C}),n.createElement("nav",t()({tabIndex:-1,className:i,ref:d},p&&{onScroll:k,onFocus:e=>{const t=e.target,{current:n}=d;if(!p||!t||!n)return;const r=n.closest(dn);yn(n,r&&r!==t?r:t)}},u),s,n.createElement("ul",{className:Sa.classes.menu,ref:m},l),n.createElement(Hl,{scrollPageToTheRight:()=>{d.current&&p&&(e=>{const[t,n]=hn(e),r=mn(e).map(bn),o=t+pn(e),a=t=>{if(t>=r.length)return e.scrollWidth;const[n]=r[t];return n<=o+fn?a(t+1):e.scrollLeft+n-o},l=((e,t)=>{const n=[...e].reverse().findIndex((e=>{let[,n]=e;return n<t}));return-1===n?0:e.length-1-n})(r,n),i=a(l+1);un(e,i)})(d.current)},scrollPageToTheLeft:()=>{d.current&&p&&(e=>{const[t,n]=hn(e),r=mn(e).map(bn),o=n-pn(e),a=t=>{if(t<=0)return 0;const[,n]=r[t];return n>=o-fn?a(t-1):e.scrollLeft+n-o},l=((e,t)=>{const n=e.findIndex((e=>{let[n]=e;return n>=t}));return-1===n?0:n})(r,t),i=a(l-1);un(e,i)})(d.current)},isScrollLeftButtonShown:h,isScrollRightButtonShown:g}))},Vl=n.forwardRef(Fl),Wl=(e,n)=>r().createElement(ml,t()({},e,{ref:n,menuWrapper:Vl,submenuProps:Dl(e.stylableClassName,e.menuMode)}));var jl=r().forwardRef(Wl);const Zl=(e,r)=>{const o="wrap"===e.menuMode?Rl:jl;return n.createElement(o,t()({ref:r},e))};var Ul=n.forwardRef(Zl);var Kl=ye((e=>{let{stateValues:t,mapperProps:r}=e;const{currentUrl:o,reportBi:a}=t,{compId:l,language:i,mainPageId:s,trackClicksAnalytics:c,...u}=r,d=n.useCallback((function(e,t){void 0===t&&(t={}),Ee(a,{link:t.link,language:i,trackClicksAnalytics:c,elementTitle:t.label,pagesMetadata:{mainPageId:s},element_id:null!=l?l:e.currentTarget.id,elementType:u.fullNameCompType,elementGroup:Se.MenuAndSearch})}),[a,i,c,l,u.fullNameCompType,s]);return{...u,currentUrl:o,reportBiOnMenuItemClick:d}}));const ql=(e,n)=>r().createElement(ml,t()({},e,{ref:n,menuWrapper:Vl,submenuProps:Al(e.stylableClassName,e.menuMode)}));const $l=(e,n)=>r().createElement(ml,t()({},e,{ref:n,menuWrapper:Vl,submenuProps:_l(e.stylableClassName,e.menuMode)}));const Xl=(e,n)=>r().createElement(ml,t()({},e,{ref:n,menuWrapper:bl,submenuProps:Al(e.stylableClassName,e.menuMode)}));const Gl=(e,n)=>r().createElement(ml,t()({},e,{ref:n,menuWrapper:bl,submenuProps:_l(e.stylableClassName,e.menuMode)}));const Jl={HamburgerCloseButton:{component:ge,controller:Ie},HamburgerCloseButton_Default:{component:Et,controller:Ie},HamburgerCloseButton_HamburgerCloseButton:{component:Dt,controller:Ie},HamburgerMenuContainer:{component:Jt,controller:Qt},HamburgerMenuContainer_Default:{component:en,controller:Qt},HamburgerMenuContainer_HamburgerMenuContainer:{component:nn,controller:Qt},HamburgerMenuContent:{component:Do},HamburgerMenuRoot:{component:To,controller:Po},HamburgerOpenButton:{component:Fo,controller:Vo},HamburgerOpenButton_Default:{component:Zo,controller:Vo},HamburgerOpenButton_HamburgerOpenButton:{component:qo,controller:Vo},HamburgerOverlay:{component:Jo,controller:Qo},HamburgerOverlay_Default:{component:na,controller:Qo},HamburgerOverlay_HamburgerOverlay:{component:ca,controller:Qo},MegaMenuContainerItem_Classic:{component:Wa},MegaMenuContainerItem_Dropdown:{component:$r},MegaMenuContainerItem_Responsive:{component:ja},Menu:{component:Ua,controller:Ka},StylableHorizontalMenu_Default:{component:Ul,controller:Kl},StylableHorizontalMenu_ScrollColumn:{component:r().forwardRef(ql),controller:Kl},StylableHorizontalMenu_ScrollFlyout:{component:r().forwardRef($l),controller:Kl},StylableHorizontalMenu_ScrollFlyoutAndColumn:{component:jl,controller:Kl},StylableHorizontalMenu_WrapColumn:{component:r().forwardRef(Xl),controller:Kl},StylableHorizontalMenu_WrapFlyout:{component:r().forwardRef(Gl),controller:Kl},StylableHorizontalMenu_WrapFlyoutAndColumn:{component:Rl,controller:Kl},StylableHorizontalMenu:{component:ml,controller:Kl},Submenu:{component:jr}}}(),a}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt_menu.826d8aa9.bundle.min.js.map