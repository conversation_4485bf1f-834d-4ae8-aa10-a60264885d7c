<?php $attributes = $attributes->exceptProps([
    'title' => 'Welcome to Codibu Design',
    'subtitle' => null,
    'description' => null,
    'backgroundImage' => null,
    'backgroundVideo' => null,
    'overlay' => true,
    'centered' => true,
    'height' => 'screen'
]); ?>
<?php foreach (array_filter(([
    'title' => 'Welcome to Codibu Design',
    'subtitle' => null,
    'description' => null,
    'backgroundImage' => null,
    'backgroundVideo' => null,
    'overlay' => true,
    'centered' => true,
    'height' => 'screen'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php
$heroClasses = 'hero relative overflow-hidden flex items-center';

$heightClasses = [
    'auto' => '',
    'screen' => 'min-h-screen',
    'large' => 'h-96',
    'medium' => 'h-80',
    'small' => 'h-64'
];

$classes = $heroClasses . ' ' . ($heightClasses[$height] ?? $heightClasses['screen']);

if ($centered) {
    $classes .= ' justify-center text-center';
}
?>

<section <?php echo e($attributes->merge(['class' => $classes])); ?>>
    <!-- Background Media -->
    <?php if($backgroundVideo): ?>
        <div class="absolute inset-0 z-0">
            <video 
                class="w-full h-full object-cover" 
                autoplay 
                muted 
                loop 
                playsinline
                <?php if($overlay): ?> style="filter: brightness(0.7);" <?php endif; ?>
            >
                <source src="<?php echo e($backgroundVideo); ?>" type="video/mp4">
                Your browser does not support the video tag.
            </video>
        </div>
    <?php elseif($backgroundImage): ?>
        <div 
            class="absolute inset-0 z-0 bg-cover bg-center bg-no-repeat"
            style="background-image: url('<?php echo e($backgroundImage); ?>'); <?php if($overlay): ?> filter: brightness(0.7); <?php endif; ?>"
        ></div>
    <?php else: ?>
        <div class="absolute inset-0 z-0 bg-gradient-to-br from-secondary-color via-gray-100 to-gray-200"></div>
    <?php endif; ?>
    
    <!-- Overlay -->
    <?php if($overlay && ($backgroundVideo || $backgroundImage)): ?>
        <div class="absolute inset-0 z-10 bg-black bg-opacity-40"></div>
    <?php endif; ?>
    
    <!-- Content -->
    <div class="relative z-20 w-full">
        <div class="container mx-auto px-4">
            <div class="hero-content <?php if($centered): ?> max-w-4xl mx-auto <?php endif; ?>">
                <?php if($title): ?>
                    <h1 class="hero-title text-4xl md:text-5xl lg:text-6xl font-bold mb-6 <?php if($backgroundVideo || $backgroundImage): ?> text-white <?php else: ?> text-gray-900 <?php endif; ?>">
                        <?php echo $title; ?>

                    </h1>
                <?php endif; ?>
                
                <?php if($subtitle): ?>
                    <p class="hero-subtitle text-xl md:text-2xl font-medium mb-4 <?php if($backgroundVideo || $backgroundImage): ?> text-gray-200 <?php else: ?> text-gray-700 <?php endif; ?>">
                        <?php echo e($subtitle); ?>

                    </p>
                <?php endif; ?>
                
                <?php if($description): ?>
                    <p class="hero-description text-lg md:text-xl mb-8 <?php if($backgroundVideo || $backgroundImage): ?> text-gray-300 <?php else: ?> text-gray-600 <?php endif; ?> max-w-3xl <?php if($centered): ?> mx-auto <?php endif; ?>">
                        <?php echo e($description); ?>

                    </p>
                <?php endif; ?>
                
                <?php if($slot->isNotEmpty()): ?>
                    <div class="hero-actions">
                        <?php echo e($slot); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div class="scroll-indicator animate-bounce">
            <svg class="w-6 h-6 <?php if($backgroundVideo || $backgroundImage): ?> text-white <?php else: ?> text-gray-600 <?php endif; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
        </div>
    </div>
</section>

<?php $__env->startPush('styles'); ?>
<style>
.hero {
    position: relative;
    background-color: var(--secondary-color);
}

.hero-title {
    line-height: 1.1;
    letter-spacing: -0.02em;
}

.hero-title .highlight {
    color: var(--accent-color);
    position: relative;
}

.hero-title .highlight::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-color), transparent);
    opacity: 0.7;
}

.hero-subtitle {
    line-height: 1.3;
}

.hero-description {
    line-height: 1.6;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero.text-center .hero-actions {
    justify-content: center;
}

.scroll-indicator {
    cursor: pointer;
    transition: var(--transition);
}

.scroll-indicator:hover {
    transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero {
        min-height: 80vh;
        padding: 2rem 0;
    }
    
    .hero-title {
        font-size: 2.5rem;
        margin-bottom: 1.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }
    
    .hero-description {
        font-size: 1rem;
        margin-bottom: 2rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* Animation for hero content */
.hero-content {
    animation: heroFadeIn 1s ease-out;
}

@keyframes  heroFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\laragon\www\codibu\resources\views/components/hero.blade.php ENDPATH**/ ?>