<?php

namespace App\Services;

class SeoService
{
    /**
     * Default SEO data
     */
    private array $defaults = [
        'title' => 'Codibu Design | Creative Graphic Solutions for Modern Brands',
        'description' => 'Welcome to Codibu Design, where graphic design meets video production. Specializing in custom solutions like branding, logo design, and marketing materials tailored to your needs.',
        'keywords' => 'graphic design, video production, branding, logo design, web design, marketing materials, social media design, freelance designer, codibu',
        'author' => 'Codibu Design',
        'robots' => 'index, follow',
        'canonical' => null,
        'og_title' => 'Codibu Design | Graphic Design & Video Production Services',
        'og_description' => 'Professional graphic design and video production services. Specializing in branding, logo design, web design, and marketing materials.',
        'og_image' => '/images/codibu-og-image.jpg',
        'og_type' => 'website',
        'twitter_card' => 'summary_large_image',
        'twitter_title' => 'Codibu Design | Graphic Design & Video Production Services',
        'twitter_description' => 'Professional graphic design and video production services. Specializing in branding, logo design, web design, and marketing materials.',
        'twitter_image' => '/images/codibu-og-image.jpg',
    ];

    /**
     * Generate SEO data for a page
     */
    public function generateSeoData(array $data = []): array
    {
        $seoData = array_merge($this->defaults, $data);
        
        // Set canonical URL if not provided
        if (!$seoData['canonical']) {
            $seoData['canonical'] = url()->current();
        }
        
        // Ensure URLs are absolute for social media
        $seoData['og_image'] = $this->ensureAbsoluteUrl($seoData['og_image']);
        $seoData['twitter_image'] = $this->ensureAbsoluteUrl($seoData['twitter_image']);
        
        // Truncate descriptions to appropriate lengths
        $seoData['description'] = $this->truncateText($seoData['description'], 160);
        $seoData['og_description'] = $this->truncateText($seoData['og_description'], 300);
        $seoData['twitter_description'] = $this->truncateText($seoData['twitter_description'], 200);
        
        return $seoData;
    }

    /**
     * Generate SEO data for service pages
     */
    public function generateServiceSeoData(string $serviceName, array $serviceData): array
    {
        $title = $serviceData['title'] . ' | Codibu Design';
        $description = $serviceData['description'];
        
        return $this->generateSeoData([
            'title' => $title,
            'description' => $description,
            'keywords' => $this->defaults['keywords'] . ', ' . strtolower($serviceName),
            'og_title' => $title,
            'og_description' => $description,
            'twitter_title' => $title,
            'twitter_description' => $description,
        ]);
    }

    /**
     * Generate structured data (JSON-LD) for the organization
     */
    public function generateOrganizationStructuredData(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => 'Codibu Design',
            'url' => url('/'),
            'logo' => asset('images/codibu-logo.png'),
            'description' => $this->defaults['description'],
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'telephone' => '******-567-890',
                'contactType' => 'customer service',
                'email' => '<EMAIL>'
            ],
            'sameAs' => [
                'https://www.facebook.com/codibudesign',
                'https://www.instagram.com/codibudesign',
                'https://www.linkedin.com/company/codibudesign'
            ],
            'address' => [
                '@type' => 'PostalAddress',
                'addressLocality' => 'Remote',
                'addressCountry' => 'Worldwide'
            ]
        ];
    }

    /**
     * Generate structured data for services
     */
    public function generateServiceStructuredData(string $serviceName, array $serviceData): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Service',
            'name' => $serviceData['title'],
            'description' => $serviceData['description'],
            'provider' => [
                '@type' => 'Organization',
                'name' => 'Codibu Design',
                'url' => url('/')
            ],
            'serviceType' => $serviceName,
            'areaServed' => 'Worldwide',
            'url' => route('services.show', $serviceName)
        ];
    }

    /**
     * Generate breadcrumb structured data
     */
    public function generateBreadcrumbStructuredData(array $breadcrumbs): array
    {
        $itemListElement = [];
        
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $itemListElement[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url']
            ];
        }
        
        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $itemListElement
        ];
    }

    /**
     * Ensure URL is absolute
     */
    private function ensureAbsoluteUrl(string $url): string
    {
        if (filter_var($url, FILTER_VALIDATE_URL)) {
            return $url;
        }
        
        return asset($url);
    }

    /**
     * Truncate text to specified length
     */
    private function truncateText(string $text, int $length): string
    {
        if (strlen($text) <= $length) {
            return $text;
        }
        
        return substr($text, 0, $length - 3) . '...';
    }

    /**
     * Generate sitemap data
     */
    public function generateSitemapData(): array
    {
        $urls = [
            [
                'url' => route('home'),
                'lastmod' => now()->toISOString(),
                'changefreq' => 'weekly',
                'priority' => '1.0'
            ]
        ];

        // Add service pages
        $services = ['codibu-design', 'web-design', 'video-production', 'visual-media', 'graphic-designs'];
        foreach ($services as $service) {
            $urls[] = [
                'url' => route('services.show', $service),
                'lastmod' => now()->toISOString(),
                'changefreq' => 'monthly',
                'priority' => '0.8'
            ];
        }

        return $urls;
    }
}
