<?php $attributes = $attributes->exceptProps([
    'id' => null,
    'background' => 'default',
    'padding' => 'default',
    'container' => true,
    'fullHeight' => false
]); ?>
<?php foreach (array_filter(([
    'id' => null,
    'background' => 'default',
    'padding' => 'default',
    'container' => true,
    'fullHeight' => false
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php
$sectionClasses = 'section';

// Background variants
$backgroundClasses = [
    'default' => 'bg-secondary-color',
    'white' => 'bg-white',
    'dark' => 'bg-gray-900 text-white',
    'gradient' => 'bg-gradient-to-br from-secondary-color to-gray-100',
    'accent' => 'bg-accent-color text-white',
    'transparent' => 'bg-transparent'
];

// Padding variants
$paddingClasses = [
    'none' => '',
    'sm' => 'py-8',
    'default' => 'py-16',
    'lg' => 'py-24',
    'xl' => 'py-32'
];

$classes = $sectionClasses . ' ' . ($backgroundClasses[$background] ?? $backgroundClasses['default']) . ' ' . ($paddingClasses[$padding] ?? $paddingClasses['default']);

if ($fullHeight) {
    $classes .= ' min-h-screen flex items-center';
}
?>

<section 
    <?php if($id): ?> id="<?php echo e($id); ?>" <?php endif; ?>
    <?php echo e($attributes->merge(['class' => $classes])); ?>

>
    <?php if($container): ?>
        <div class="container mx-auto px-4">
            <?php echo e($slot); ?>

        </div>
    <?php else: ?>
        <?php echo e($slot); ?>

    <?php endif; ?>
</section>

<?php $__env->startPush('styles'); ?>
<style>
.section {
    position: relative;
    overflow: hidden;
}

.section.bg-gradient-to-br {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #e8e6e3 100%);
}

.section.bg-accent-color {
    background-color: var(--accent-color);
}

.section.bg-gray-900 {
    background-color: var(--gray-dark);
}

/* Section animations */
.section[data-animate="true"] {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.section[data-animate="true"].in-view {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive padding adjustments */
@media (max-width: 768px) {
    .section.py-16 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
    
    .section.py-24 {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }
    
    .section.py-32 {
        padding-top: 5rem;
        padding-bottom: 5rem;
    }
}
</style>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\laragon\www\codibu\resources\views/components/section.blade.php ENDPATH**/ ?>