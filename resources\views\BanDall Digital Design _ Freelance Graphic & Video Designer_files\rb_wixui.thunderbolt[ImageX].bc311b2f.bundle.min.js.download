!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[ImageX]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[ImageX]"]=t(require("react")):e["rb_wixui.thunderbolt[ImageX]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var i in a)({}).hasOwnProperty.call(a,i)&&(e[i]=a[i])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},a={};function i(e){var n=a[e];if(void 0!==n)return n.exports;var r=a[e]={exports:{}};return t[e](r,r.exports,i),r.exports}i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,{a:t}),t},i.d=function(e,t){for(var a in t)i.o(t,a)&&!i.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return function(){"use strict";i.r(n),i.d(n,{components:function(){return ie}});var e=i(448),t=i.n(e),a=i(5329),r=i.n(a);function o(e){var t,a,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(a=o(e[t]))&&(i&&(i+=" "),i+=a);else for(t in e)e[t]&&(i&&(i+=" "),i+=t);return i}var l=function(){for(var e,t,a=0,i="";a<arguments.length;)(e=arguments[a++])&&(t=o(e))&&(i&&(i+=" "),i+=t);return i};const s=e=>Object.entries(e).reduce(((e,[t,a])=>(t.includes("data-")&&(e[t]=a),e)),{});const c=13,d=27;function u(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}const p=u(32),m=u(c),g=e=>{m(e),p(e)},h=(u(d),"wixui-"),f=(e,...t)=>{const a=[];return e&&a.push(`${h}${e}`),t.forEach((e=>{e&&(a.push(`${h}${e}`),a.push(e))})),a.join(" ")},y={root:"linkElement"};var v=a.forwardRef(((e,t)=>{const{href:i,role:n,target:r,rel:o,className:l="",children:c,linkPopupId:d,anchorDataId:u,anchorCompId:h,tabIndex:f,dataTestId:v=y.root,title:b,onClick:I,onDoubleClick:w,onMouseEnter:_,onMouseLeave:k,onFocus:S,onFocusCapture:C,onBlurCapture:M,"aria-live":x,"aria-disabled":P,"aria-label":D,"aria-labelledby":E,"aria-pressed":$,"aria-expanded":L,"aria-describedby":O,"aria-haspopup":F,"aria-current":N,dataPreview:A,dataPart:T}=e,j=void 0!==e.activateByKey?e.activateByKey:(e=>e?"SpaceOrEnter":"Space")(d);let B;switch(j){case"Enter":B=m;break;case"Space":B=p;break;case"SpaceOrEnter":B=g;break;default:B=void 0}return void 0!==i||d?a.createElement("a",{...s(e),"data-testid":v,"data-popupid":d,"data-anchor":u,"data-anchor-comp-id":h,"data-preview":A,"data-part":T,href:i||void 0,target:r,role:d?"button":n,rel:o,className:l,onKeyDown:B,"aria-live":x,"aria-disabled":P,"aria-label":D,"aria-labelledby":E,"aria-pressed":$,"aria-expanded":L,"aria-haspopup":F,"aria-describedby":O,"aria-current":N,title:b,onClick:I,onMouseEnter:_,onMouseLeave:k,onDoubleClick:w,onFocus:S,onFocusCapture:C,onBlurCapture:M,ref:t,tabIndex:d?0:f},c):a.createElement("div",{...s(e),"data-testid":v,"data-preview":A,"data-part":T,className:l,tabIndex:f,"aria-label":D,"aria-labelledby":E,"aria-haspopup":F,"aria-disabled":P,"aria-expanded":L,title:b,role:n,onClick:I,onDoubleClick:w,onMouseEnter:_,onMouseLeave:k,ref:t},c)}));const b="imageX",I="displayModeStyle",w="pictureElement";var _={root:"image"};const k=e=>{var t;let{id:a,imageInfo:i,defaultSrc:n,hasSsrSrc:o,defaultPlaceholder:l,sourceSetPlaceholders:s,className:c,isInFirstFold:d}=e;const u=(null==l?void 0:l.uri)||void 0,p=(null==l||null==(t=l.css)?void 0:t.img)||{};delete p.height,delete p.width;return r().createElement("wow-image",{id:"img-"+a,"data-is-responsive":"true","data-image-info":JSON.stringify({...i,containerId:a}),"data-has-ssr-src":o,"data-motion-part":"BG_IMG "+a,class:c},r().createElement("picture",null,i.sourceSets&&((e,t)=>e.map(((e,a)=>{var i;const n=(null==t||null==(i=t[a])?void 0:i.uri)||void 0;return r().createElement("source",{key:a,media:e.mediaQuery,srcSet:e.src||n,suppressHydrationWarning:!0})})))(i.sourceSets,s),r().createElement("img",{loading:!1===d?"lazy":void 0,fetchpriority:!0===d?"high":void 0,src:u||n,alt:i.imageData.alt,style:p,suppressHydrationWarning:!0})))},S=e=>{var t,a;let{id:i,imageInfo:n,defaultSrc:o,getPlaceholder:l,className:s,imageLayerClass:c,isInFirstFold:d,cancelClip:u}=e,p="";const m=r().useRef(null);var g;m.current||(l?(p="true",m.current={defaultSrc:l({fittingType:n.imageData.displayMode||"fill",src:{id:n.imageData.uri,width:n.imageData.width,height:n.imageData.height,crop:n.imageData.crop,name:n.imageData.name,focalPoint:n.imageData.focalPoint},target:{alignment:n.alignType,htmlTag:"img"},options:{hasAnimation:null==n?void 0:n.hasAnimation,...(null==n?void 0:n.encoding)&&{encoding:n.encoding}}}),sourceSet:null==(g=n.sourceSets)?void 0:g.map((e=>l({fittingType:e.displayMode,src:{id:n.imageData.uri,width:n.imageData.width,height:n.imageData.height,crop:e.crop,name:n.imageData.name,focalPoint:e.focalPoint},target:{alignment:n.alignType,htmlTag:"img"}})))}):m.current={defaultSrc:{uri:"",css:{img:{},container:{}},attr:{img:{},container:{}},transformed:!1},sourceSet:[]});const h=null==(t=m.current)?void 0:t.defaultSrc,f=null==(a=m.current)?void 0:a.sourceSet;return r().createElement("div",{className:s,"data-motion-part":u?"":"BG_LAYER "+i},r().createElement("div",{"data-motion-part":"BG_MEDIA "+i},r().createElement(k,{id:i,imageInfo:n,defaultSrc:o,hasSsrSrc:p,defaultPlaceholder:h,sourceSetPlaceholders:f,className:c,isInFirstFold:d})))},C="center",M="top",x="top_left",P="top_right",D="bottom",E="bottom_left",$="bottom_right",L="left",O="right",F="contrast",N="brightness",A="saturation",T="hue",j="blur";function B(e,...t){return function(...a){const i=a[a.length-1]||{},n=[e[0]];return t.forEach((function(t,r){const o=Number.isInteger(t)?a[t]:i[t];n.push(o,e[r+1])})),n.join("")}}["/","\\","?","<",">","|","\u201c",":",'"'].map(encodeURIComponent);const R={isMobile:!1};function G(){if("undefined"!=typeof window&&"undefined"!=typeof navigator){const t=window.matchMedia&&window.matchMedia("(max-width: 767px)").matches,a=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);e=t&&a,R["isMobile"]=e}var e}B`fit/w_${"width"},h_${"height"}`,B`fill/w_${"width"},h_${"height"},al_${"alignment"}`,B`fill/w_${"width"},h_${"height"},fp_${"focalPointX"}_${"focalPointY"}`,B`crop/x_${"x"},y_${"y"},w_${"width"},h_${"height"}`,B`crop/w_${"width"},h_${"height"},al_${"alignment"}`,B`fill/w_${"width"},h_${"height"},al_${"alignment"}`,B`,lg_${"upscaleMethodValue"}`,B`,q_${"quality"}`,B`,quality_auto`,B`,usm_${"radius"}_${"amount"}_${"threshold"}`,B`,bl`,B`,wm_${"watermark"}`,B`,con_${"contrast"}`,B`,br_${"brightness"}`,B`,sat_${"saturation"}`,B`,hue_${"hue"}`,B`,blur_${"blur"}`,B`,enc_auto`,B`,enc_avif`,B`,enc_pavif`,B`,pstr`;const U={[C]:"50% 50%",[x]:"0% 0%",[P]:"100% 0%",[M]:"50% 0%",[E]:"0% 100%",[$]:"100% 100%",[D]:"50% 100%",[O]:"100% 50%",[L]:"0% 50%"};Object.entries(U).reduce(((e,[t,a])=>(e[a]=t,e)),{});"undefined"!=typeof window&&window.devicePixelRatio;G();G();const X=function(e){void 0===e&&(e="");if(/(^https?)|(^data)|(^blob)|(^\/\/)/.test(e))return e;let t=Y()+"/";return e&&(/^micons\//.test(e)?t=Y():"ico"===/[^.]+$/.exec(e)[0]&&(t=t.replace("media","ficons"))),t+e};function Y(){return"undefined"!=typeof window&&window.serviceTopology&&window.serviceTopology.staticMediaUrl||"https://static.wixstatic.com/media"}const q=e=>{let{sources:a,isInFirstFold:i,imageInfo:n,objectFit:o="cover",className:l}=e;const s=a[a.length-1].heightAttribute,c=a[a.length-1].widthAttribute,{uri:d,alt:u}=n.imageData,p=X(d);return r().createElement("picture",{"data-testId":w,className:l},a.map((e=>{let{srcset:t,media:a,sizes:i,heightAttribute:n,widthAttribute:o}=e;return r().createElement("source",{sizes:i,srcSet:t,media:a,height:n,width:o,suppressHydrationWarning:!0})})),r().createElement("img",t()({},i?{fetchpriority:"high"}:{loading:"lazy"},{src:p,alt:u,height:s,width:c,style:{"--responsive-img-object-fit":o}})))},W=e=>{let{className:t,id:a,imageInfo:i,defaultSrc:n,getPlaceholder:o,sources:l,isInFirstFold:s,objectFit:c,shouldUseResponsiveImages:d,imageLayerClass:u,cancelClip:p}=e;return d&&(null==l?void 0:l.length)>0?r().createElement(q,{sources:l,isInFirstFold:s,imageInfo:i,objectFit:c,className:t}):r().createElement(S,{id:a,imageInfo:i,defaultSrc:n,getPlaceholder:o,className:t,isInFirstFold:s,imageLayerClass:u,cancelClip:p})};var z={ImageX:"lyNaha",imageX:"lyNaha",responsiveImg:"JdNFxG",imageStyling:"h1DYhE",imageLayer:"Ux33nC",linkedImage:"QebvG3",ImageXLegacy:"YCPMeD",imageXLegacy:"YCPMeD"};var H=e=>{const{id:i,skin:n,className:r,customClassNames:o=[],link:c,showLink:d,imageInfo:u,aspectRatio:p,onClick:m,onDblClick:g,onMouseEnter:h,onMouseLeave:y,reportBiOnClick:w,shouldUseResponsiveImages:k,sources:S,defaultSrc:C,getPlaceholder:M,isInFirstFold:x,objectFit:P,a11y:D}=e,E=s(e),$=c&&d;$&&(c["data-motion-part"]="BG_LAYER "+i);const L={id:i,imageInfo:u,defaultSrc:C,getPlaceholder:M,sources:S,isInFirstFold:x,objectFit:P,shouldUseResponsiveImages:k,imageLayerClass:z.imageLayer,cancelClip:$},O=((e,t,a)=>"fitWidth"===t?"#"+e.replace("#","")+" {aspect-ratio: "+1/a+";}":"")(i,u.imageData.displayMode,p),F=(({reportBiOnClick:e,onClick:t})=>(0,a.useCallback)((a=>{e?.(a),t?.(a)}),[e,t]))({onClick:m,reportBiOnClick:w});return a.createElement("div",t()({id:i},E,((e={})=>{const t=e.tabIndex??e.tabindex??void 0;return void 0!==t?{tabIndex:Number(t)}:{}})(D),{"data-testid":b,className:l(z[n],r,k&&z.responsiveImg,f(_.root,...o)),onClick:m||$?F:void 0,onDoubleClick:g,onMouseEnter:h,onMouseLeave:y}),O?a.createElement("style",{"data-testid":I},O):null,$?a.createElement(v,t()({},c,{className:z.imageStyling}),a.createElement(W,t()({},L,{className:z.linkedImage}))):a.createElement(W,t()({},L,{className:z.imageStyling})))};const J=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)),K=e=>"linkPopupId"in e,V=(e,t)=>{if(K(e))return e.linkPopupId;{const{pagesMap:a,mainPageId:i}=t||{};if(!a)return;const n=new URL(e.href??"");let r=Object.values(a).find((({pageUriSEO:e})=>!!e&&n.pathname?.includes(e)));return r||(r=i?a[i]:void 0),r?.pageId}},Q=e=>{if(void 0!==e)return null===e?"None":e.type},Z=(e,t)=>{if(!e?.type)return;const{type:a}=e;switch(a){case"AnchorLink":return e.anchorDataId;case"DocumentLink":return e.docInfo?.name;case"PageLink":const a=V(e,t);return a&&t?.pagesMap?.[a]?.title;default:return e.href}},ee=(e,t,a)=>{const{link:i,value:n,details:r,actionName:o,elementType:l,trackClicksAnalytics:s,pagesMetadata:c,...d}=t;if(!s)return;const u=c&&{...c,pagesMap:window.viewerModel?.siteFeaturesConfigs?.router?.pagesMap},p=((e,t)=>{if(!e?.type)return;const{type:a}=e;switch(a){case"AnchorLink":return(e=>"anchorDataId"in e&&("SCROLL_TO_TOP"===e.anchorDataId||"SCROLL_TO_BOTTOM"===e.anchorDataId))(e)?void 0:{id:e.anchorDataId};case"DocumentLink":return{id:e.docInfo?.docId};case"PageLink":return{id:V(e,t),isLightbox:K(e)};default:return}})(i,u),m=r||p?JSON.stringify({...p,...r}):void 0;e({src:76,evid:1113,...{...d,bl:navigator.language,url:window.location.href,details:m,elementType:l??"Unknown",actionName:o??Q(i),value:n??Z(i,u)}},{endpoint:"pa",...a})};var te;!function(e){e.Text="Text",e.Menu="Menu",e.Image="Image",e.Input="Input",e.Login="Login",e.Button="Button",e.Social="Social",e.Gallery="Gallery",e.Community="Community",e.Decorative="Decorative",e.MenuAndSearch="MenuAndSearch",e.MenuAndAnchor="MenuAndAnchor"}(te||(te={}));var ae;const ie={ImageX:{component:H,controller:(ae=e=>{let{mapperProps:t,stateValues:a}=e;const{compId:i,language:n,mainPageId:r,fullNameCompType:o,trackClicksAnalytics:l,...s}=t;return{...s,reportBiOnClick:e=>{const{reportBi:t}=a,{imageInfo:c,link:d}=s;ee(t,{link:d,language:n,trackClicksAnalytics:l,elementType:o,pagesMetadata:{mainPageId:r},elementTitle:c.imageData.name,elementGroup:te.Image,details:{uri:c.imageData.uri},element_id:null!=i?i:e.currentTarget.id})}}},{useComponentProps:(e,t,a)=>{const i=(e=>({...e,updateStyles:t=>{const a=Object.entries(t).reduce(((e,[t,a])=>{return{...e,[(i=t,i.startsWith("--")?t:J(t))]:void 0===a?null:a};var i}),{});e.updateStyles(a)}}))(a);return ae({mapperProps:e,stateValues:t,controllerUtils:i})}})}}}(),n}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[ImageX].bc311b2f.bundle.min.js.map