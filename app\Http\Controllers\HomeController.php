<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;

class HomeController extends Controller
{
    /**
     * Display the home page.
     *
     * @return View
     */
    public function index(): View
    {
        $seoData = [
            'title' => 'Codibu Design | Creative Graphic Solutions for Modern Brands',
            'description' => 'Welcome to Codibu Design, where graphic design meets video production. Specializing in custom solutions like branding, logo design, and marketing materials tailored to your needs.',
            'keywords' => 'graphic design, video production, branding, logo design, web design, marketing materials, social media design, freelance designer',
            'og_title' => 'Codibu Design | Graphic Design & Video Production Services',
            'og_description' => 'Professional graphic design and video production services. Specializing in branding, logo design, web design, and marketing materials.',
            'twitter_title' => 'Codibu Design | Graphic Design & Video Production Services',
            'twitter_description' => 'Professional graphic design and video production services. Specializing in branding, logo design, web design, and marketing materials.',
        ];

        $services = [
            [
                'id' => 'codibu-design',
                'title' => 'Codibu Design',
                'number' => '01',
                'description' => 'Complete branding solutions and visual identity design.',
                'features' => ['Logo Design', 'Brand Guidelines', 'Visual Identity']
            ],
            [
                'id' => 'web-design',
                'title' => 'Web Design',
                'number' => '02',
                'description' => 'Modern, responsive websites that convert visitors into customers.',
                'features' => ['Responsive Design', 'User Experience', 'Performance Optimization']
            ],
            [
                'id' => 'video-production',
                'title' => 'Video Production',
                'number' => '03',
                'description' => 'Professional video content for marketing and brand storytelling.',
                'features' => ['Video Editing', 'Motion Graphics', 'Social Media Videos']
            ],
            [
                'id' => 'visual-media',
                'title' => 'Visual Media Design',
                'number' => '04',
                'description' => 'Eye-catching visuals for digital and print media.',
                'features' => ['Social Media Graphics', 'Print Design', 'Digital Assets']
            ],
            [
                'id' => 'graphic-designs',
                'title' => 'Graphic Designs',
                'number' => '05',
                'description' => 'Custom graphic solutions for all your design needs.',
                'features' => ['Marketing Materials', 'Business Cards', 'Poster Design']
            ]
        ];

        return view('home', compact('seoData', 'services'));
    }

    /**
     * Display a specific service page.
     *
     * @param string $service
     * @return View
     */
    public function service(string $service): View
    {
        $services = [
            'codibu-design' => [
                'title' => 'Codibu Design Services',
                'description' => 'Complete branding solutions and visual identity design.',
                'content' => 'Our comprehensive design services help establish your brand identity...'
            ],
            'web-design' => [
                'title' => 'Web Design Services',
                'description' => 'Modern, responsive websites that convert visitors into customers.',
                'content' => 'We create stunning, user-friendly websites that drive results...'
            ],
            'video-production' => [
                'title' => 'Video Production Services',
                'description' => 'Professional video content for marketing and brand storytelling.',
                'content' => 'From concept to final cut, we produce engaging video content...'
            ],
            'visual-media' => [
                'title' => 'Visual Media Design Services',
                'description' => 'Eye-catching visuals for digital and print media.',
                'content' => 'Our visual media services cover all your graphic design needs...'
            ],
            'graphic-designs' => [
                'title' => 'Graphic Design Services',
                'description' => 'Custom graphic solutions for all your design needs.',
                'content' => 'Professional graphic design services for businesses of all sizes...'
            ]
        ];

        if (!array_key_exists($service, $services)) {
            abort(404);
        }

        $serviceData = $services[$service];
        
        return view('service', compact('serviceData', 'service'));
    }
}
