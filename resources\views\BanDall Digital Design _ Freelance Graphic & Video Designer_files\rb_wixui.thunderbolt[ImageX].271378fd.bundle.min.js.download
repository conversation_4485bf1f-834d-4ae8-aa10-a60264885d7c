!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[ImageX]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[ImageX]"]=t(require("react")):e["rb_wixui.thunderbolt[ImageX]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var i in a)({}).hasOwnProperty.call(a,i)&&(e[i]=a[i])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},a={};function i(e){var r=a[e];if(void 0!==r)return r.exports;var n=a[e]={exports:{}};return t[e](n,n.exports,i),n.exports}i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,{a:t}),t},i.d=function(e,t){for(var a in t)i.o(t,a)&&!i.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};return function(){"use strict";i.r(r),i.d(r,{components:function(){return ie}});var e=i(448),t=i.n(e),a=i(5329),n=i.n(a);function o(e){var t,a,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(a=o(e[t]))&&(i&&(i+=" "),i+=a);else for(t in e)e[t]&&(i&&(i+=" "),i+=t);return i}var s=function(){for(var e,t,a=0,i="";a<arguments.length;)(e=arguments[a++])&&(t=o(e))&&(i&&(i+=" "),i+=t);return i};const l=e=>Object.entries(e).reduce(((e,[t,a])=>(t.includes("data-")&&(e[t]=a),e)),{});const c=13,d=27;function u(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}const p=u(32),m=u(c),g=e=>{m(e),p(e)},h=(u(d),"wixui-"),f=(e,...t)=>{const a=[];return e&&a.push(`${h}${e}`),t.forEach((e=>{e&&(a.push(`${h}${e}`),a.push(e))})),a.join(" ")},y={root:"linkElement"};var v=a.forwardRef(((e,t)=>{const{href:i,role:r,target:n,rel:o,className:s="",children:c,linkPopupId:d,anchorDataId:u,anchorCompId:h,tabIndex:f,dataTestId:v=y.root,title:b,onClick:I,onDoubleClick:w,onMouseEnter:_,onMouseLeave:k,onFocus:S,onFocusCapture:M,onBlurCapture:C,"aria-live":x,"aria-disabled":P,"aria-label":D,"aria-labelledby":E,"aria-pressed":$,"aria-expanded":L,"aria-describedby":O,"aria-haspopup":F,"aria-current":N,dataPreview:T,dataPart:A}=e,j=void 0!==e.activateByKey?e.activateByKey:(e=>e?"SpaceOrEnter":"Space")(d);let B;switch(j){case"Enter":B=m;break;case"Space":B=p;break;case"SpaceOrEnter":B=g;break;default:B=void 0}return void 0!==i||d?a.createElement("a",{...l(e),"data-testid":v,"data-popupid":d,"data-anchor":u,"data-anchor-comp-id":h,"data-preview":T,"data-part":A,href:i||void 0,target:n,role:d?"button":r,rel:o,className:s,onKeyDown:B,"aria-live":x,"aria-disabled":P,"aria-label":D,"aria-labelledby":E,"aria-pressed":$,"aria-expanded":L,"aria-haspopup":F,"aria-describedby":O,"aria-current":N,title:b,onClick:I,onMouseEnter:_,onMouseLeave:k,onDoubleClick:w,onFocus:S,onFocusCapture:M,onBlurCapture:C,ref:t,tabIndex:d?0:f},c):a.createElement("div",{...l(e),"data-testid":v,"data-preview":T,"data-part":A,className:s,tabIndex:f,"aria-label":D,"aria-labelledby":E,"aria-haspopup":F,"aria-disabled":P,"aria-expanded":L,title:b,role:r,onClick:I,onDoubleClick:w,onMouseEnter:_,onMouseLeave:k,ref:t},c)}));const b="imageX",I="displayModeStyle",w="pictureElement";var _={root:"image"};const k=e=>{var t;let{id:a,imageInfo:i,defaultSrc:r,hasSsrSrc:o,defaultPlaceholder:s,sourceSetPlaceholders:l,className:c,isInFirstFold:d}=e;const u=(null==s?void 0:s.uri)||void 0,p=(null==s||null==(t=s.css)?void 0:t.img)||{};delete p.height,delete p.width;return n().createElement("wow-image",{id:"img-"+a,"data-is-responsive":"true","data-image-info":JSON.stringify({...i,containerId:a}),"data-has-ssr-src":o,"data-motion-part":"BG_IMG "+a,class:c},n().createElement("picture",null,i.sourceSets&&((e,t)=>e.map(((e,a)=>{var i;const r=(null==t||null==(i=t[a])?void 0:i.uri)||void 0;return n().createElement("source",{key:a,media:e.mediaQuery,srcSet:e.src||r,suppressHydrationWarning:!0})})))(i.sourceSets,l),n().createElement("img",{loading:!1===d?"lazy":void 0,fetchpriority:!0===d?"high":void 0,src:u||r,alt:i.imageData.alt,style:p,suppressHydrationWarning:!0})))},S=e=>{var t,a;let{id:i,imageInfo:r,defaultSrc:o,getPlaceholder:s,className:l,imageLayerClass:c,isInFirstFold:d}=e,u="";const p=n().useRef(null);var m;p.current||(s?(u="true",p.current={defaultSrc:s({fittingType:r.imageData.displayMode||"fill",src:{id:r.imageData.uri,width:r.imageData.width,height:r.imageData.height,crop:r.imageData.crop,name:r.imageData.name,focalPoint:r.imageData.focalPoint},target:{alignment:r.alignType,htmlTag:"img"},options:{hasAnimation:null==r?void 0:r.hasAnimation,...(null==r?void 0:r.encoding)&&{encoding:r.encoding}}}),sourceSet:null==(m=r.sourceSets)?void 0:m.map((e=>s({fittingType:e.displayMode,src:{id:r.imageData.uri,width:r.imageData.width,height:r.imageData.height,crop:e.crop,name:r.imageData.name,focalPoint:e.focalPoint},target:{alignment:r.alignType,htmlTag:"img"}})))}):p.current={defaultSrc:{uri:"",css:{img:{},container:{}},attr:{img:{},container:{}},transformed:!1},sourceSet:[]});const g=null==(t=p.current)?void 0:t.defaultSrc,h=null==(a=p.current)?void 0:a.sourceSet;return n().createElement("div",{className:l,"data-motion-part":"BG_LAYER "+i},n().createElement("div",{"data-motion-part":"BG_MEDIA "+i},n().createElement(k,{id:i,imageInfo:r,defaultSrc:o,hasSsrSrc:u,defaultPlaceholder:g,sourceSetPlaceholders:h,className:c,isInFirstFold:d})))},M="center",C="top",x="top_left",P="top_right",D="bottom",E="bottom_left",$="bottom_right",L="left",O="right",F="contrast",N="brightness",T="saturation",A="hue",j="blur";function B(e,...t){return function(...a){const i=a[a.length-1]||{},r=[e[0]];return t.forEach((function(t,n){const o=Number.isInteger(t)?a[t]:i[t];r.push(o,e[n+1])})),r.join("")}}["/","\\","?","<",">","|","\u201c",":",'"'].map(encodeURIComponent);const R={isMobile:!1};function U(){if("undefined"!=typeof window&&"undefined"!=typeof navigator){const t=window.matchMedia&&window.matchMedia("(max-width: 767px)").matches,a=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);e=t&&a,R["isMobile"]=e}var e}B`fit/w_${"width"},h_${"height"}`,B`fill/w_${"width"},h_${"height"},al_${"alignment"}`,B`fill/w_${"width"},h_${"height"},fp_${"focalPointX"}_${"focalPointY"}`,B`crop/x_${"x"},y_${"y"},w_${"width"},h_${"height"}`,B`crop/w_${"width"},h_${"height"},al_${"alignment"}`,B`fill/w_${"width"},h_${"height"},al_${"alignment"}`,B`,lg_${"upscaleMethodValue"}`,B`,q_${"quality"}`,B`,quality_auto`,B`,usm_${"radius"}_${"amount"}_${"threshold"}`,B`,bl`,B`,wm_${"watermark"}`,B`,con_${"contrast"}`,B`,br_${"brightness"}`,B`,sat_${"saturation"}`,B`,hue_${"hue"}`,B`,blur_${"blur"}`,B`,enc_auto`,B`,enc_avif`,B`,enc_pavif`,B`,pstr`;const X={[M]:"50% 50%",[x]:"0% 0%",[P]:"100% 0%",[C]:"50% 0%",[E]:"0% 100%",[$]:"100% 100%",[D]:"50% 100%",[O]:"100% 50%",[L]:"0% 50%"};Object.entries(X).reduce(((e,[t,a])=>(e[a]=t,e)),{});"undefined"!=typeof window&&window.devicePixelRatio;U();U();const G=function(e){void 0===e&&(e="");if(/(^https?)|(^data)|(^blob)|(^\/\/)/.test(e))return e;let t=q()+"/";return e&&(/^micons\//.test(e)?t=q():"ico"===/[^.]+$/.exec(e)[0]&&(t=t.replace("media","ficons"))),t+e};function q(){return"undefined"!=typeof window&&window.serviceTopology&&window.serviceTopology.staticMediaUrl||"https://static.wixstatic.com/media"}const W=e=>{let{sources:a,isInFirstFold:i,imageInfo:r,objectFit:o="cover",className:s}=e;const l=a[a.length-1].heightAttribute,c=a[a.length-1].widthAttribute,{uri:d,alt:u}=r.imageData,p=G(d);return n().createElement("picture",{"data-testId":w,className:s},a.map((e=>{let{srcset:t,media:a,sizes:i,heightAttribute:r,widthAttribute:o}=e;return n().createElement("source",{sizes:i,srcSet:t,media:a,height:r,width:o,suppressHydrationWarning:!0})})),n().createElement("img",t()({},i?{fetchpriority:"high"}:{loading:"lazy"},{src:p,alt:u,height:l,width:c,style:{"--responsive-img-object-fit":o}})))},Y=e=>{let{className:t,id:a,imageInfo:i,defaultSrc:r,getPlaceholder:o,sources:s,isInFirstFold:l,objectFit:c,shouldUseResponsiveImages:d,imageLayerClass:u}=e;return d&&(null==s?void 0:s.length)>0?n().createElement(W,{sources:s,isInFirstFold:l,imageInfo:i,objectFit:c,className:t}):n().createElement(S,{id:a,imageInfo:i,defaultSrc:r,getPlaceholder:o,className:t,isInFirstFold:l,imageLayerClass:u})};var z={ImageX:"lyNaha",imageX:"lyNaha",responsiveImg:"JdNFxG",imageStyling:"h1DYhE",imageLayer:"Ux33nC",linkedImage:"QebvG3",ImageXLegacy:"YCPMeD",imageXLegacy:"YCPMeD"};var H=e=>{const{id:i,skin:r,className:n,customClassNames:o=[],link:c,showLink:d,imageInfo:u,aspectRatio:p,onClick:m,onDblClick:g,onMouseEnter:h,onMouseLeave:y,reportBiOnClick:w,shouldUseResponsiveImages:k,sources:S,defaultSrc:M,getPlaceholder:C,isInFirstFold:x,objectFit:P,a11y:D}=e,E=l(e),$={id:i,imageInfo:u,defaultSrc:M,getPlaceholder:C,sources:S,isInFirstFold:x,objectFit:P,shouldUseResponsiveImages:k,imageLayerClass:z.imageLayer},L=((e,t,a)=>"fitWidth"===t?"#"+e.replace("#","")+" {aspect-ratio: "+1/a+";}":"")(i,u.imageData.displayMode,p),O=(({reportBiOnClick:e,onClick:t})=>(0,a.useCallback)((a=>{e?.(a),t?.(a)}),[e,t]))({onClick:m,reportBiOnClick:w}),F=c&&d;return a.createElement("div",t()({id:i},E,((e={})=>{const t=e.tabIndex??e.tabindex??void 0;return void 0!==t?{tabIndex:Number(t)}:{}})(D),{"data-testid":b,className:s(z[r],n,k&&z.responsiveImg,f(_.root,...o)),onClick:m||F?O:void 0,onDoubleClick:g,onMouseEnter:h,onMouseLeave:y}),L?a.createElement("style",{"data-testid":I},L):null,F?a.createElement(v,t()({},c,{className:z.imageStyling}),a.createElement(Y,t()({},$,{className:z.linkedImage}))):a.createElement(Y,t()({},$,{className:z.imageStyling})))};const J=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)),K=e=>"linkPopupId"in e,V=(e,t)=>{if(K(e))return e.linkPopupId;{const{pagesMap:a,mainPageId:i}=t||{};if(!a)return;const r=new URL(e.href??"");let n=Object.values(a).find((({pageUriSEO:e})=>!!e&&r.pathname?.includes(e)));return n||(n=i?a[i]:void 0),n?.pageId}},Q=e=>{if(void 0!==e)return null===e?"None":e.type},Z=(e,t)=>{if(!e?.type)return;const{type:a}=e;switch(a){case"AnchorLink":return e.anchorDataId;case"DocumentLink":return e.docInfo?.name;case"PageLink":const a=V(e,t);return a&&t?.pagesMap?.[a]?.title;default:return e.href}},ee=(e,t,a)=>{const{link:i,value:r,details:n,actionName:o,elementType:s,trackClicksAnalytics:l,pagesMetadata:c,...d}=t;if(!l)return;const u=c&&{...c,pagesMap:window.viewerModel?.siteFeaturesConfigs?.router?.pagesMap},p=((e,t)=>{if(!e?.type)return;const{type:a}=e;switch(a){case"AnchorLink":return(e=>"anchorDataId"in e&&("SCROLL_TO_TOP"===e.anchorDataId||"SCROLL_TO_BOTTOM"===e.anchorDataId))(e)?void 0:{id:e.anchorDataId};case"DocumentLink":return{id:e.docInfo?.docId};case"PageLink":return{id:V(e,t),isLightbox:K(e)};default:return}})(i,u),m=n||p?JSON.stringify({...p,...n}):void 0;e({src:76,evid:1113,...{...d,bl:navigator.language,url:window.location.href,details:m,elementType:s??"Unknown",actionName:o??Q(i),value:r??Z(i,u)}},{endpoint:"pa",...a})};var te;!function(e){e.Text="Text",e.Menu="Menu",e.Image="Image",e.Input="Input",e.Login="Login",e.Button="Button",e.Social="Social",e.Gallery="Gallery",e.Community="Community",e.Decorative="Decorative",e.MenuAndSearch="MenuAndSearch",e.MenuAndAnchor="MenuAndAnchor"}(te||(te={}));var ae;const ie={ImageX:{component:H,controller:(ae=e=>{let{mapperProps:t,stateValues:a}=e;const{compId:i,language:r,mainPageId:n,fullNameCompType:o,trackClicksAnalytics:s,...l}=t;return{...l,reportBiOnClick:e=>{const{reportBi:t}=a,{imageInfo:c,link:d}=l;ee(t,{link:d,language:r,trackClicksAnalytics:s,elementType:o,pagesMetadata:{mainPageId:n},elementTitle:c.imageData.name,elementGroup:te.Image,details:{uri:c.imageData.uri},element_id:null!=i?i:e.currentTarget.id})}}},{useComponentProps:(e,t,a)=>{const i=(e=>({...e,updateStyles:t=>{const a=Object.entries(t).reduce(((e,[t,a])=>{return{...e,[(i=t,i.startsWith("--")?t:J(t))]:void 0===a?null:a};var i}),{});e.updateStyles(a)}}))(a);return ae({mapperProps:e,stateValues:t,controllerUtils:i})}})}}}(),r}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[ImageX].271378fd.bundle.min.js.map