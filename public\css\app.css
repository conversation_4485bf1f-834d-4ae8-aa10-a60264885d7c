@import url(https://fonts.googleapis.com/css2?family=Wix+Madefor+Display:wght@400;500;600;700;800&display=swap);
/* Codibu Design - ACTUAL Wix Studio Design */

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Wix Madefor Display', Arial, sans-serif;
  line-height: 1.6;
  color: #000000;
  background-color: #f5f3f0;
  font-weight: 400;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header - Exact Wix Style */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  padding: 15px 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo img {
  height: 24px;
  width: auto;
}

.nav {
  display: none;
  gap: 30px;
}

.nav a {
  color: #000000;
  text-decoration: none;
  font-size: 16px;
  transition: color 0.3s;
}

.nav a:hover {
  color: #666666;
}

.menu-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #000000;
}

/* Mobile Menu */
.mobile-nav {
  position: fixed;
  top: 70px;
  left: 0;
  right: 0;
  background: white;
  padding: 20px;
  display: none;
  z-index: 999;
}

.mobile-nav.active {
  display: block;
}

.mobile-nav a {
  display: block;
  padding: 15px 0;
  color: #000000;
  text-decoration: none;
  border-bottom: 1px solid #eee;
}

/* Hero Section - Exact Wix Layout */
.hero {
  padding: 120px 0 80px;
  background: #f5f3f0;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 80px;
  align-items: start;
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  color: #000000;
  margin-bottom: 30px;
}

.hero-content .subtitle {
  font-size: 18px;
  color: #000000;
  margin-bottom: 20px;
  line-height: 1.5;
}

.hero-content .description {
  font-size: 16px;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 40px;
}

/* Services Sidebar - Right Side */
.services-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.service-item {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #eee;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
}

.service-item:hover {
  background: #f8f9fa;
  padding-left: 15px;
}

.service-number {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  margin-right: 20px;
  min-width: 40px;
}

.service-content h3 {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 5px;
}

/* Video Section */
.video-section {
  height: 520px;
  position: relative;
  overflow: hidden;
}

.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-background video {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

/* About Section */
.about {
  padding: 80px 0;
  background: #f5f3f0;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.about h2 {
  font-size: 36px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 30px;
}

.about h3 {
  font-size: 20px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 20px;
}

.about p {
  font-size: 16px;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 30px;
}

.about-images img {
  width: 100%;
  border-radius: 8px;
  margin-bottom: 20px;
}

/* Contact Section */
.contact {
  padding: 80px 0;
  background: #ffffff;
  text-align: center;
}

.contact-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.contact h2 {
  font-size: 36px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 20px;
}

.contact p {
  font-size: 18px;
  color: #666666;
  margin-bottom: 40px;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

.contact-item h4 {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 10px;
}

.contact-item a {
  color: #ff6b35;
  text-decoration: none;
  font-size: 16px;
}

.contact-item a:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (min-width: 768px) {
  .nav {
    display: flex;
  }

  .menu-btn {
    display: none;
  }
}

@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .about-container {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 36px;
  }

  .about h2 {
    font-size: 28px;
  }

  .contact h2 {
    font-size: 28px;
  }

  .video-section {
    height: 300px;
  }

  .hero {
    padding: 100px 0 60px;
  }

  .about, .contact {
    padding: 60px 0;
  }
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  padding: 15px 0;
  transition: var(--transition);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo img {
  height: 24px;
  width: auto;
  transition: var(--transition);
}

.logo img:hover {
  transform: scale(1.05);
}

/* Navigation */
.nav-desktop {
  display: flex;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
  padding: 0;
}

.nav-link {
  text-decoration: none;
  color: var(--primary-color);
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.nav-link:hover {
  color: var(--accent-color);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--accent-color);
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

/* Mobile Menu */
.mobile-menu-btn {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
}

.hamburger-line {
  width: 25px;
  height: 3px;
  background-color: var(--primary-color);
  margin: 3px 0;
  transition: var(--transition);
}

.nav-mobile {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--white);
  box-shadow: var(--box-shadow);
}

.nav-menu-mobile {
  list-style: none;
  padding: 1rem 0;
  margin: 0;
}

.nav-menu-mobile li {
  padding: 0.5rem 0;
}

.nav-link-mobile {
  display: block;
  padding: 0.5rem 1rem;
  text-decoration: none;
  color: var(--primary-color);
  font-weight: 500;
  transition: var(--transition);
}

.nav-link-mobile:hover {
  background-color: var(--gray-light);
  color: var(--accent-color);
}

@media (max-width: 768px) {
  .nav-desktop {
    display: none;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .nav-mobile.active {
    display: block;
  }
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo img {
  height: 24px;
  width: auto;
}

.nav {
  display: none;
  gap: 30px;
}

.nav a {
  color: #000000;
  text-decoration: none;
  font-size: 16px;
  transition: color 0.3s;
}

.nav a:hover {
  color: #666666;
}

.contact-btn {
  background: #000000;
  color: #ffffff;
  padding: 10px 20px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  margin-left: 20px;
}

.contact-btn:hover {
  background: #333333;
}

.menu-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #000000;
}

/* Mobile Menu */
.mobile-nav {
  position: fixed;
  top: 70px;
  left: 0;
  right: 0;
  background: white;
  padding: 20px;
  display: none;
  z-index: 999;
}

.mobile-nav.active {
  display: block;
}

.mobile-nav a {
  display: block;
  padding: 15px 0;
  color: #000000;
  text-decoration: none;
  border-bottom: 1px solid #eee;
}

/* Hero Section - ACTUAL Wix Design */
.hero {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f3f0 0%, #e8e4df 100%);
  position: relative;
  display: flex;
  align-items: center;
  padding: 120px 0 80px;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23d4c5b0" points="0,1000 1000,800 1000,1000"/></svg>') no-repeat bottom;
  background-size: cover;
  opacity: 0.3;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  display: grid;
  grid-template-columns: 1.5fr 1fr;
  gap: 80px;
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero-content h1 {
  font-size: 64px;
  font-weight: 800;
  line-height: 1.1;
  color: #2c2c2c;
  margin-bottom: 30px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hero-content .subtitle {
  font-size: 20px;
  color: #4a4a4a;
  margin-bottom: 25px;
  line-height: 1.5;
  font-weight: 500;
}

.hero-content .description {
  font-size: 16px;
  color: #666666;
  line-height: 1.7;
  margin-bottom: 40px;
  max-width: 500px;
}

/* Services Sidebar - Right Side with Wix Style */
.services-list {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}

.service-item {
  display: flex;
  align-items: center;
  padding: 18px 0;
  border-bottom: 1px solid rgba(0,0,0,0.1);
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 5px;
}

.service-item:hover {
  background: rgba(44, 44, 44, 0.05);
  padding-left: 15px;
  transform: translateX(5px);
}

.service-number {
  font-size: 28px;
  font-weight: 800;
  color: #2c2c2c;
  margin-right: 25px;
  min-width: 50px;
}

.service-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2c2c2c;
  margin-bottom: 0;
}

/* Brown/Orange Section - Like in Wix */
.brown-section {
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #CD853F 100%);
  padding: 120px 0;
  position: relative;
  overflow: hidden;
}

.brown-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23654321" points="0,0 1000,200 1000,0"/></svg>') no-repeat top;
  background-size: cover;
  opacity: 0.3;
}

.brown-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  position: relative;
  z-index: 2;
  text-align: center;
}

.brown-section h2 {
  font-size: 48px;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: 30px;
  text-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.brown-section p {
  font-size: 20px;
  color: rgba(255,255,255,0.9);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto 40px;
}

.contact-btn-white {
  background: #ffffff;
  color: #8B4513;
  padding: 12px 24px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  display: inline-block;
  margin: 20px 0 40px;
}

.contact-btn-white:hover {
  background: #f0f0f0;
}

.expertise-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-top: 50px;
}

.expertise-item {
  text-align: center;
  color: #ffffff;
}

.expertise-icon {
  margin-bottom: 20px;
}

.expertise-item h3 {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 15px;
}

.expertise-item p {
  font-size: 16px;
  color: rgba(255,255,255,0.9);
  line-height: 1.6;
}

.resume-section {
  text-align: center;
  margin-top: 50px;
}

.view-resume-btn {
  background: #ffffff;
  color: #8B4513;
  padding: 12px 24px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
}

.view-resume-btn:hover {
  background: #f0f0f0;
}

/* About Section */
.about {
  padding: 80px 0;
  background: #f5f3f0;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.about h2 {
  font-size: 36px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 30px;
}

.about h3 {
  font-size: 20px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 20px;
}

.about p {
  font-size: 16px;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 30px;
}

.about ul {
  list-style: none;
  padding: 0;
}

.about ul li {
  font-size: 16px;
  color: #666666;
  margin-bottom: 10px;
  line-height: 1.6;
}

/* Portfolio Section - Dark Blue/Teal like Wix */
.portfolio {
  padding: 120px 0;
  background: linear-gradient(135deg, #1e3a5f 0%, #2c5f7c 50%, #3a7a99 100%);
  position: relative;
}

.portfolio::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23164a6b" points="0,1000 1000,800 1000,1000"/></svg>') no-repeat bottom;
  background-size: cover;
  opacity: 0.4;
}

.portfolio-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  position: relative;
  z-index: 2;
}

.portfolio h2 {
  font-size: 48px;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: 20px;
  text-align: center;
  text-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.portfolio .subtitle {
  font-size: 20px;
  color: rgba(255,255,255,0.9);
  text-align: center;
  margin-bottom: 60px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.portfolio-item {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 40px;
  border-radius: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.portfolio-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.portfolio-item:hover::before {
  opacity: 1;
}

.portfolio-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.portfolio-item h3 {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
}

.portfolio-item p {
  font-size: 16px;
  color: rgba(255,255,255,0.8);
  line-height: 1.6;
  position: relative;
  z-index: 2;
}

.portfolio-icon {
  margin-bottom: 20px;
  text-align: center;
}

.view-project-btn {
  color: rgba(255,255,255,0.8);
  text-decoration: underline;
  font-size: 14px;
  transition: color 0.3s ease;
}

.view-project-btn:hover {
  color: #ffffff;
}

.portfolio-image {
  text-align: center;
  margin: 40px 0;
}

.portfolio-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(147px, 1fr));
  gap: 15px;
  margin-top: 40px;
}

.portfolio-images-grid img {
  width: 100%;
  height: auto;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.portfolio-images-grid img:hover {
  transform: scale(1.05);
}

/* Codibu Marketing Section */
.codibu-section {
  background: linear-gradient(135deg, #f5f3f0 0%, #e8e4df 100%);
  padding: 80px 0;
  text-align: center;
}

.codibu-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 40px;
}

.codibu-logo {
  margin-bottom: 30px;
}

.codibu-section h2 {
  font-size: 36px;
  font-weight: 700;
  color: #2c2c2c;
  margin-bottom: 20px;
}

.codibu-section p {
  font-size: 18px;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 30px;
}

.cta-btn {
  background: #2c2c2c;
  color: #ffffff;
  padding: 15px 30px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
}

.cta-btn:hover {
  background: #000000;
  transform: translateY(-2px);
}

/* Footer - Dark like BanDall */
.footer {
  padding: 60px 0 40px;
  background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%);
  color: #ffffff;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 60px;
}

.footer-contact h3 {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
}

.footer-contact p {
  font-size: 16px;
  color: rgba(255,255,255,0.8);
  line-height: 1.6;
  margin-bottom: 15px;
}

.footer-contact a {
  color: rgba(255,255,255,0.9);
  text-decoration: none;
}

.footer-contact a:hover {
  color: #ffffff;
}

.footer-nav ul {
  list-style: none;
  padding: 0;
}

.footer-nav ul li {
  margin-bottom: 12px;
}

.footer-nav ul li a {
  color: rgba(255,255,255,0.8);
  text-decoration: none;
  font-size: 16px;
  transition: color 0.3s;
}

.footer-nav ul li a:hover {
  color: #ffffff;
}

.footer h4 {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 15px;
}

.footer-bottom {
  border-top: 1px solid rgba(255,255,255,0.2);
  padding-top: 30px;
  margin-top: 40px;
  text-align: center;
  color: rgba(255,255,255,0.6);
  font-size: 14px;
}

/* Responsive Design */
@media (min-width: 768px) {
  .nav {
    display: flex;
  }

  .menu-btn {
    display: none;
  }
}

@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 40px;
    padding: 0 20px;
  }

  .about-container {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .footer-container {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .brown-container,
  .portfolio-container {
    padding: 0 20px;
  }
}

@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 42px;
  }

  .brown-section h2 {
    font-size: 36px;
  }

  .portfolio h2 {
    font-size: 36px;
  }

  .portfolio-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .hero {
    padding: 80px 0 60px;
  }

  .brown-section,
  .portfolio {
    padding: 80px 0;
  }
}

/* Main Hero Section - Exact Wix Studio Style */
.wix-hero {
  min-height: 100vh;
  background-color: #000000;
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 40px;
}

.wix-hero-container {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 80px;
  align-items: center;
}

.wix-hero-content {
  padding-right: 40px;
}

.wix-hero-title {
  font-size: 72px;
  font-weight: 800;
  color: #ffffff;
  line-height: 0.9;
  margin-bottom: 40px;
  letter-spacing: -2px;
}

.wix-hero-subtitle {
  font-size: 24px;
  color: #ffffff;
  line-height: 1.4;
  margin-bottom: 30px;
  font-weight: 400;
  opacity: 0.9;
}

.wix-hero-description {
  font-size: 18px;
  color: #cccccc;
  line-height: 1.6;
  margin-bottom: 50px;
  max-width: 500px;
}

/* Numbered Services List - Right Side */
.wix-services-sidebar {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.wix-service-item {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #333333;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.wix-service-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
  padding-left: 20px;
}

.wix-service-number {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin-right: 30px;
  min-width: 40px;
}

.wix-service-content {
  flex: 1;
}

.wix-service-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 5px;
}

.wix-service-desc {
  font-size: 14px;
  color: #999999;
  line-height: 1.4;
}

/* About Section - Wix Studio Style */
.wix-about {
  padding: 100px 40px;
  background-color: #111111;
}

.wix-about-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: start;
}

.wix-about-content {
  padding-right: 40px;
}

.wix-about-title {
  font-size: 48px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 30px;
  letter-spacing: -1px;
  line-height: 1.1;
}

.wix-about-subtitle {
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 25px;
}

.wix-about-text {
  font-size: 16px;
  color: #cccccc;
  line-height: 1.6;
  margin-bottom: 40px;
}

.wix-features-title {
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 25px;
}

.wix-features-list {
  list-style: none;
  padding: 0;
}

.wix-features-list li {
  font-size: 16px;
  color: #cccccc;
  margin-bottom: 15px;
  line-height: 1.6;
}

.wix-features-list li strong {
  color: #ffffff;
}

.wix-about-visual {
  background: linear-gradient(135deg, #333333 0%, #666666 100%);
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 18px;
  font-weight: 500;
}

/* Portfolio Section - Wix Studio Style */
.wix-portfolio {
  padding: 100px 40px;
  background-color: #000000;
}

.wix-portfolio-container {
  max-width: 1200px;
  margin: 0 auto;
}

.wix-portfolio-title {
  font-size: 48px;
  font-weight: 700;
  color: #ffffff;
  text-align: center;
  margin-bottom: 20px;
  letter-spacing: -1px;
}

.wix-portfolio-subtitle {
  font-size: 18px;
  color: #cccccc;
  text-align: center;
  margin-bottom: 80px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.wix-portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.wix-portfolio-item {
  position: relative;
  height: 400px;
  overflow: hidden;
  background: #222222;
  transition: all 0.3s ease;
}

.wix-portfolio-item:hover {
  transform: translateY(-10px);
}

.wix-portfolio-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 100%);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 40px;
  transition: all 0.3s ease;
}

.wix-portfolio-item:hover .wix-portfolio-overlay {
  background: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 100%);
}

.wix-portfolio-item-title {
  font-size: 24px;
  font-weight: 600;
  color: white;
  margin-bottom: 10px;
}

.wix-portfolio-item-description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* Footer - Wix Studio Style */
.wix-footer {
  padding: 100px 40px 60px;
  background-color: #111111;
  color: white;
}

.wix-footer-container {
  max-width: 1200px;
  margin: 0 auto;
}

.wix-footer-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 80px;
  margin-bottom: 60px;
}

.wix-footer-title {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 30px;
  letter-spacing: -1px;
}

.wix-footer-text {
  font-size: 16px;
  color: #cccccc;
  line-height: 1.6;
  margin-bottom: 30px;
}

.wix-footer-section-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
}

.wix-footer-nav {
  list-style: none;
  padding: 0;
}

.wix-footer-nav li {
  margin-bottom: 12px;
}

.wix-footer-nav a {
  color: #cccccc;
  text-decoration: none;
  font-size: 16px;
  transition: color 0.3s ease;
}

.wix-footer-nav a:hover {
  color: white;
}

.wix-footer-bottom {
  border-top: 1px solid #333333;
  padding-top: 40px;
  text-align: center;
}

.wix-footer-services {
  color: #999999;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
}

.wix-footer-copyright {
  color: #666666;
  font-size: 14px;
}

/* Responsive Design - Wix Studio Style */
@media (min-width: 768px) {
  .wix-nav {
    display: flex;
  }

  .wix-menu-btn {
    display: none;
  }
}

@media (max-width: 1024px) {
  .wix-hero {
    padding: 0 20px;
  }

  .wix-hero-container {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }

  .wix-hero-content {
    padding-right: 0;
  }

  .wix-hero-title {
    font-size: 56px;
  }

  .wix-about-container {
    grid-template-columns: 1fr;
    gap: 60px;
    padding: 0 20px;
  }

  .wix-about-content {
    padding-right: 0;
  }

  .wix-portfolio {
    padding: 80px 20px;
  }

  .wix-footer {
    padding: 80px 20px 40px;
  }

  .wix-footer-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .wix-header {
    padding: 15px 20px;
  }

  .wix-hero-title {
    font-size: 42px;
  }

  .wix-hero-subtitle {
    font-size: 20px;
  }

  .wix-services-sidebar {
    margin-top: 40px;
  }

  .wix-about-title,
  .wix-portfolio-title {
    font-size: 36px;
  }

  .wix-portfolio-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .wix-portfolio-item {
    height: 300px;
  }
}

/* Special Wix-style elements */
.wix-numbered-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin: 40px 0;
}

.wix-numbered-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-left: 4px solid #000000;
}

.wix-numbered-item .number {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  min-width: 40px;
}

.wix-numbered-item .content {
  flex: 1;
}

.wix-numbered-item .title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 5px;
}

.wix-numbered-item .description {
  font-size: 16px;
  color: #666666;
}

/* Additional mobile styles */
@media (max-width: 767px) {
  .wix-hero-title {
    font-size: 36px;
  }

  .wix-hero-buttons {
    flex-direction: column;
  }

  .wix-btn-primary,
  .wix-btn-secondary {
    text-align: center;
    width: 100%;
  }

  .wix-numbered-list {
    margin-top: 60px;
  }

  .wix-services-grid,
  .wix-portfolio-grid {
    grid-template-columns: 1fr;
  }

  .wix-about-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .wix-footer-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .wix-footer-grid > div[style*="grid-column"] {
    grid-column: span 1 !important;
  }
}
