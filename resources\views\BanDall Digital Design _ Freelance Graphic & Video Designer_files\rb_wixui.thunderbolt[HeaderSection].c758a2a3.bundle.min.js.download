!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[HeaderSection]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[HeaderSection]"]=t(require("react")):e["rb_wixui.thunderbolt[HeaderSection]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={96114:function(e,t,i){var a;!function(t){"use strict";var n=function(){},r=t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.msRequestAnimationFrame||function(e){return setTimeout(e,16)};function o(){var e=this;e.reads=[],e.writes=[],e.raf=r.bind(t),n("initialized",e)}function c(e){e.scheduled||(e.scheduled=!0,e.raf(s.bind(null,e)),n("flush scheduled"))}function s(e){n("flush");var t,i=e.writes,a=e.reads;try{n("flushing reads",a.length),e.runTasks(a),n("flushing writes",i.length),e.runTasks(i)}catch(e){t=e}if(e.scheduled=!1,(a.length||i.length)&&c(e),t){if(n("task errored",t.message),!e.catch)throw t;e.catch(t)}}function l(e,t){var i=e.indexOf(t);return!!~i&&!!e.splice(i,1)}o.prototype={constructor:o,runTasks:function(e){var t;for(n("run tasks");t=e.shift();)t()},measure:function(e,t){n("measure");var i=t?e.bind(t):e;return this.reads.push(i),c(this),i},mutate:function(e,t){n("mutate");var i=t?e.bind(t):e;return this.writes.push(i),c(this),i},clear:function(e){return n("clear",e),l(this.reads,e)||l(this.writes,e)},extend:function(e){if(n("extend",e),"object"!=typeof e)throw new Error("expected object");var t=Object.create(this);return function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])}(t,e),t.fastdom=this,t.initialize&&t.initialize(),t},catch:null};var d=t.fastdom=t.fastdom||new o;void 0===(a=function(){return d}.call(d,i,d,e))||(e.exports=a)}("undefined"!=typeof window?window:void 0!==this?this:globalThis)},5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var a in i)({}).hasOwnProperty.call(i,a)&&(e[a]=i[a])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},i={};function a(e){var n=i[e];if(void 0!==n)return n.exports;var r=i[e]={exports:{}};return t[e].call(r.exports,r,r.exports,a),r.exports}a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,{a:t}),t},a.d=function(e,t){for(var i in t)a.o(t,i)&&!a.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return function(){"use strict";a.r(n),a.d(n,{components:function(){return si}});var e={};a.r(e),a.d(e,{STATIC_MEDIA_URL:function(){return tt},ph:function(){return Xe}});var t=a(448),i=a.n(t),r=a(5329),o=a.n(r);function c(e){var t,i,a="";if("string"==typeof e||"number"==typeof e)a+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(i=c(e[t]))&&(a&&(a+=" "),a+=i);else for(t in e)e[t]&&(a&&(a+=" "),a+=t);return a}var s=function(){for(var e,t,i=0,a="";i<arguments.length;)(e=arguments[i++])&&(t=c(e))&&(a&&(a+=" "),a+=t);return a};const l="wixui-",d=(e,...t)=>{const i=[];return e&&i.push(`${l}${e}`),t.forEach((e=>{e&&(i.push(`${l}${e}`),i.push(e))})),i.join(" ")};const u=()=>"undefined"!=typeof window;const h=13,g=27;function m(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}m(32),m(h),m(g);const f="section-container";var p="jhxvbR";const _="v1",T=2,I=1920,E=1920,L=1e3,b=1e3,A={SCALE_TO_FILL:"fill",SCALE_TO_FIT:"fit",STRETCH:"stretch",ORIGINAL_SIZE:"original_size",TILE:"tile",TILE_HORIZONTAL:"tile_horizontal",TILE_VERTICAL:"tile_vertical",FIT_AND_TILE:"fit_and_tile",LEGACY_STRIP_TILE:"legacy_strip_tile",LEGACY_STRIP_TILE_HORIZONTAL:"legacy_strip_tile_horizontal",LEGACY_STRIP_TILE_VERTICAL:"legacy_strip_tile_vertical",LEGACY_STRIP_SCALE_TO_FILL:"legacy_strip_fill",LEGACY_STRIP_SCALE_TO_FIT:"legacy_strip_fit",LEGACY_STRIP_FIT_AND_TILE:"legacy_strip_fit_and_tile",LEGACY_STRIP_ORIGINAL_SIZE:"legacy_strip_original_size",LEGACY_ORIGINAL_SIZE:"actual_size",LEGACY_FIT_WIDTH:"fitWidth",LEGACY_FIT_HEIGHT:"fitHeight",LEGACY_FULL:"full",LEGACY_BG_FIT_AND_TILE:"legacy_tile",LEGACY_BG_FIT_AND_TILE_HORIZONTAL:"legacy_tile_horizontal",LEGACY_BG_FIT_AND_TILE_VERTICAL:"legacy_tile_vertical",LEGACY_BG_NORMAL:"legacy_normal"},w={FIT:"fit",FILL:"fill",FILL_FOCAL:"fill_focal",CROP:"crop",LEGACY_CROP:"legacy_crop",LEGACY_FILL:"legacy_fill"},y={CENTER:"center",TOP:"top",TOP_LEFT:"top_left",TOP_RIGHT:"top_right",BOTTOM:"bottom",BOTTOM_LEFT:"bottom_left",BOTTOM_RIGHT:"bottom_right",LEFT:"left",RIGHT:"right"},v={[y.CENTER]:{x:.5,y:.5},[y.TOP_LEFT]:{x:0,y:0},[y.TOP_RIGHT]:{x:1,y:0},[y.TOP]:{x:.5,y:0},[y.BOTTOM_LEFT]:{x:0,y:1},[y.BOTTOM_RIGHT]:{x:1,y:1},[y.BOTTOM]:{x:.5,y:1},[y.RIGHT]:{x:1,y:.5},[y.LEFT]:{x:0,y:.5}},O={center:"c",top:"t",top_left:"tl",top_right:"tr",bottom:"b",bottom_left:"bl",bottom_right:"br",left:"l",right:"r"},M={BG:"bg",IMG:"img",SVG:"svg"},R={AUTO:"auto",CLASSIC:"classic",SUPER:"super"},S={classic:1,super:2},C={radius:"0.66",amount:"1.00",threshold:"0.01"},G={uri:"",css:{img:{},container:{}},attr:{img:{},container:{}},transformed:!1},N=25e6,F=[1.5,2,4],P={HIGH:{size:196e4,quality:90,maxUpscale:1},MEDIUM:{size:36e4,quality:85,maxUpscale:1},LOW:{size:16e4,quality:80,maxUpscale:1.2},TINY:{size:0,quality:80,maxUpscale:1.4}},x={HIGH:"HIGH",MEDIUM:"MEDIUM",LOW:"LOW",TINY:"TINY"},k={CONTRAST:"contrast",BRIGHTNESS:"brightness",SATURATION:"saturation",HUE:"hue",BLUR:"blur"},H={JPG:"jpg",JPEG:"jpeg",JPE:"jpe",PNG:"png",WEBP:"webp",WIX_ICO_MP:"wix_ico_mp",WIX_MP:"wix_mp",GIF:"gif",SVG:"svg",AVIF:"avif",UNRECOGNIZED:"unrecognized"},B={AVIF:"AVIF",PAVIF:"PAVIF"};H.JPG,H.JPEG,H.JPE,H.PNG,H.GIF,H.WEBP;function Y(e,...t){return function(...i){const a=i[i.length-1]||{},n=[e[0]];return t.forEach((function(t,r){const o=Number.isInteger(t)?i[t]:a[t];n.push(o,e[r+1])})),n.join("")}}function U(e){return e[e.length-1]}const z=[H.PNG,H.JPEG,H.JPG,H.JPE,H.WIX_ICO_MP,H.WIX_MP,H.WEBP,H.AVIF],$=[H.JPEG,H.JPG,H.JPE];function j(e,t,i){return i&&t&&!(!(a=t.id)||!a.trim()||"none"===a.toLowerCase())&&Object.values(A).includes(e);var a}function D(e,t,i){return function(e,t,i=!1){return!((W(e)||q(e))&&t&&!i)}(e,t,i)&&(function(e){return z.includes(ee(e))}(e)||function(e,t=!1){return Z(e)&&t}(e,i))&&!/(^https?)|(^data)|(^\/\/)/.test(e)}function V(e){return ee(e)===H.PNG}function W(e){return ee(e)===H.WEBP}function Z(e){return ee(e)===H.GIF}function q(e){return ee(e)===H.AVIF}const J=["/","\\","?","<",">","|","\u201c",":",'"'].map(encodeURIComponent),K=["\\.","\\*"],X="_";function Q(e){return function(e){return $.includes(ee(e))}(e)?H.JPG:V(e)?H.PNG:W(e)?H.WEBP:Z(e)?H.GIF:q(e)?H.AVIF:H.UNRECOGNIZED}function ee(e){return(/[.]([^.]+)$/.exec(e)&&/[.]([^.]+)$/.exec(e)[1]||"").toLowerCase()}function te(e,t,i,a,n){let r;return r=n===w.FILL?function(e,t,i,a){return Math.max(i/e,a/t)}(e,t,i,a):n===w.FIT?function(e,t,i,a){return Math.min(i/e,a/t)}(e,t,i,a):1,r}function ie(e,t,i,a,n,r){e=e||a.width,t=t||a.height;const{scaleFactor:o,width:c,height:s}=function(e,t,i,a,n){let r,o=i,c=a;if(r=te(e,t,i,a,n),n===w.FIT&&(o=e*r,c=t*r),o&&c&&o*c>N){const i=Math.sqrt(N/(o*c));o*=i,c*=i,r=te(e,t,o,c,n)}return{scaleFactor:r,width:o,height:c}}(e,t,a.width*n,a.height*n,i);return function(e,t,i,a,n,r,o){const{optimizedScaleFactor:c,upscaleMethodValue:s,forceUSM:l}=function(e,t,i,a){if("auto"===a)return function(e,t){const i=oe(e,t);return{optimizedScaleFactor:P[i].maxUpscale,upscaleMethodValue:S.classic,forceUSM:!1}}(e,t);if("super"===a)return function(e){return{optimizedScaleFactor:U(F),upscaleMethodValue:S.super,forceUSM:!(F.includes(e)||e>U(F))}}(i);return function(e,t){const i=oe(e,t);return{optimizedScaleFactor:P[i].maxUpscale,upscaleMethodValue:S.classic,forceUSM:!1}}(e,t)}(e,t,r,n);let d=i,u=a;if(r<=c)return{width:d,height:u,scaleFactor:r,upscaleMethodValue:s,forceUSM:l,cssUpscaleNeeded:!1};switch(o){case w.FILL:d=i*(c/r),u=a*(c/r);break;case w.FIT:d=e*c,u=t*c}return{width:d,height:u,scaleFactor:c,upscaleMethodValue:s,forceUSM:l,cssUpscaleNeeded:!0}}(e,t,c,s,r,o,i)}function ae(e,t,i,a){const n=re(i)||function(e=y.CENTER){return v[e]}(a);return{x:Math.max(0,Math.min(e.width-t.width,n.x*e.width-t.width/2)),y:Math.max(0,Math.min(e.height-t.height,n.y*e.height-t.height/2)),width:Math.min(e.width,t.width),height:Math.min(e.height,t.height)}}function ne(e){return e.alignment&&O[e.alignment]||O[y.CENTER]}function re(e){let t;return!e||"number"!=typeof e.x||isNaN(e.x)||"number"!=typeof e.y||isNaN(e.y)||(t={x:ce(Math.max(0,Math.min(100,e.x))/100,2),y:ce(Math.max(0,Math.min(100,e.y))/100,2)}),t}function oe(e,t){const i=e*t;return i>P[x.HIGH].size?x.HIGH:i>P[x.MEDIUM].size?x.MEDIUM:i>P[x.LOW].size?x.LOW:x.TINY}function ce(e,t){const i=Math.pow(10,t||0);return(e*i/i).toFixed(t)}function se(e){return e&&e.upscaleMethod&&R[e.upscaleMethod.toUpperCase()]||R.AUTO}function le(e,t){const i=W(e)||q(e);return ee(e)===H.GIF||i&&t}const de={isMobile:!1},ue=function(e){return de[e]};function he(){if("undefined"!=typeof window&&"undefined"!=typeof navigator){const t=window.matchMedia&&window.matchMedia("(max-width: 767px)").matches,i=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);e=t&&i,de["isMobile"]=e}var e}function ge(e,t){const i={css:{container:{}}},{css:a}=i,{fittingType:n}=e;switch(n){case A.ORIGINAL_SIZE:case A.LEGACY_ORIGINAL_SIZE:case A.LEGACY_STRIP_ORIGINAL_SIZE:a.container.backgroundSize="auto",a.container.backgroundRepeat="no-repeat";break;case A.SCALE_TO_FIT:case A.LEGACY_STRIP_SCALE_TO_FIT:a.container.backgroundSize="contain",a.container.backgroundRepeat="no-repeat";break;case A.STRETCH:a.container.backgroundSize="100% 100%",a.container.backgroundRepeat="no-repeat";break;case A.SCALE_TO_FILL:case A.LEGACY_STRIP_SCALE_TO_FILL:a.container.backgroundSize="cover",a.container.backgroundRepeat="no-repeat";break;case A.TILE_HORIZONTAL:case A.LEGACY_STRIP_TILE_HORIZONTAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-x";break;case A.TILE_VERTICAL:case A.LEGACY_STRIP_TILE_VERTICAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-y";break;case A.TILE:case A.LEGACY_STRIP_TILE:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat";break;case A.LEGACY_STRIP_FIT_AND_TILE:a.container.backgroundSize="contain",a.container.backgroundRepeat="repeat";break;case A.FIT_AND_TILE:case A.LEGACY_BG_FIT_AND_TILE:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat";break;case A.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-x";break;case A.LEGACY_BG_FIT_AND_TILE_VERTICAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-y";break;case A.LEGACY_BG_NORMAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="no-repeat"}switch(t.alignment){case y.CENTER:a.container.backgroundPosition="center center";break;case y.LEFT:a.container.backgroundPosition="left center";break;case y.RIGHT:a.container.backgroundPosition="right center";break;case y.TOP:a.container.backgroundPosition="center top";break;case y.BOTTOM:a.container.backgroundPosition="center bottom";break;case y.TOP_RIGHT:a.container.backgroundPosition="right top";break;case y.TOP_LEFT:a.container.backgroundPosition="left top";break;case y.BOTTOM_RIGHT:a.container.backgroundPosition="right bottom";break;case y.BOTTOM_LEFT:a.container.backgroundPosition="left bottom"}return i}const me={[y.CENTER]:"center",[y.TOP]:"top",[y.TOP_LEFT]:"top left",[y.TOP_RIGHT]:"top right",[y.BOTTOM]:"bottom",[y.BOTTOM_LEFT]:"bottom left",[y.BOTTOM_RIGHT]:"bottom right",[y.LEFT]:"left",[y.RIGHT]:"right"},fe={position:"absolute",top:"auto",right:"auto",bottom:"auto",left:"auto"};function pe(e,t){const i={css:{container:{},img:{}}},{css:a}=i,{fittingType:n}=e,r=t.alignment;switch(a.container.position="relative",n){case A.ORIGINAL_SIZE:case A.LEGACY_ORIGINAL_SIZE:e.parts&&e.parts.length?(a.img.width=e.parts[0].width,a.img.height=e.parts[0].height):(a.img.width=e.src.width,a.img.height=e.src.height);break;case A.SCALE_TO_FIT:case A.LEGACY_FIT_WIDTH:case A.LEGACY_FIT_HEIGHT:case A.LEGACY_FULL:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="contain",a.img.objectPosition=me[r]||"unset";break;case A.LEGACY_BG_NORMAL:a.img.width="100%",a.img.height="100%",a.img.objectFit="none",a.img.objectPosition=me[r]||"unset";break;case A.STRETCH:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="fill";break;case A.SCALE_TO_FILL:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="cover"}if("number"==typeof a.img.width&&"number"==typeof a.img.height&&(a.img.width!==t.width||a.img.height!==t.height)){const e=Math.round((t.height-a.img.height)/2),i=Math.round((t.width-a.img.width)/2);Object.assign(a.img,fe,function(e,t,i){return{[y.TOP_LEFT]:{top:0,left:0},[y.TOP_RIGHT]:{top:0,right:0},[y.TOP]:{top:0,left:t},[y.BOTTOM_LEFT]:{bottom:0,left:0},[y.BOTTOM_RIGHT]:{bottom:0,right:0},[y.BOTTOM]:{bottom:0,left:t},[y.RIGHT]:{top:e,right:0},[y.LEFT]:{top:e,left:0},[y.CENTER]:{width:i.width,height:i.height,objectFit:"none"}}}(e,i,t)[r])}return i}function _e(e,t){const i={css:{container:{}},attr:{container:{},img:{}}},{css:a,attr:n}=i,{fittingType:r}=e,o=t.alignment,{width:c,height:s}=e.src;let l;switch(a.container.position="relative",r){case A.ORIGINAL_SIZE:case A.LEGACY_ORIGINAL_SIZE:case A.TILE:e.parts&&e.parts.length?(n.img.width=e.parts[0].width,n.img.height=e.parts[0].height):(n.img.width=c,n.img.height=s),n.img.preserveAspectRatio="xMidYMid slice";break;case A.SCALE_TO_FIT:case A.LEGACY_FIT_WIDTH:case A.LEGACY_FIT_HEIGHT:case A.LEGACY_FULL:n.img.width="100%",n.img.height="100%",n.img.transform="",n.img.preserveAspectRatio="";break;case A.STRETCH:n.img.width=t.width,n.img.height=t.height,n.img.x=0,n.img.y=0,n.img.transform="",n.img.preserveAspectRatio="none";break;case A.SCALE_TO_FILL:D(e.src.id)?(n.img.width=t.width,n.img.height=t.height):(l=function(e,t,i,a,n){const r=te(e,t,i,a,n);return{width:Math.round(e*r),height:Math.round(t*r)}}(c,s,t.width,t.height,w.FILL),n.img.width=l.width,n.img.height=l.height),n.img.x=0,n.img.y=0,n.img.transform="",n.img.preserveAspectRatio="xMidYMid slice"}if("number"==typeof n.img.width&&"number"==typeof n.img.height&&(n.img.width!==t.width||n.img.height!==t.height)){let e,i,a=0,c=0;r===A.TILE?(e=t.width%n.img.width,i=t.height%n.img.height):(e=t.width-n.img.width,i=t.height-n.img.height);const s=Math.round(e/2),l=Math.round(i/2);switch(o){case y.TOP_LEFT:a=0,c=0;break;case y.TOP:a=s,c=0;break;case y.TOP_RIGHT:a=e,c=0;break;case y.LEFT:a=0,c=l;break;case y.CENTER:a=s,c=l;break;case y.RIGHT:a=e,c=l;break;case y.BOTTOM_LEFT:a=0,c=i;break;case y.BOTTOM:a=s,c=i;break;case y.BOTTOM_RIGHT:a=e,c=i}n.img.x=a,n.img.y=c}return n.container.width=t.width,n.container.height=t.height,n.container.viewBox=[0,0,t.width,t.height].join(" "),i}function Te(e,t,i){let a;switch(t.crop&&(a=function(e,t){const i=Math.max(0,Math.min(e.width,t.x+t.width)-Math.max(0,t.x)),a=Math.max(0,Math.min(e.height,t.y+t.height)-Math.max(0,t.y));return i&&a&&(e.width!==i||e.height!==a)?{x:Math.max(0,t.x),y:Math.max(0,t.y),width:i,height:a}:null}(t,t.crop),a&&(e.src.width=a.width,e.src.height=a.height,e.src.isCropped=!0,e.parts.push(Ee(a)))),e.fittingType){case A.SCALE_TO_FIT:case A.LEGACY_FIT_WIDTH:case A.LEGACY_FIT_HEIGHT:case A.LEGACY_FULL:case A.FIT_AND_TILE:case A.LEGACY_BG_FIT_AND_TILE:case A.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case A.LEGACY_BG_FIT_AND_TILE_VERTICAL:case A.LEGACY_BG_NORMAL:e.parts.push(Ie(e,i));break;case A.SCALE_TO_FILL:e.parts.push(function(e,t){const i=ie(e.src.width,e.src.height,w.FILL,t,e.devicePixelRatio,e.upscaleMethod),a=re(e.focalPoint);return{transformType:a?w.FILL_FOCAL:w.FILL,width:Math.round(i.width),height:Math.round(i.height),alignment:ne(t),focalPointX:a&&a.x,focalPointY:a&&a.y,upscale:i.scaleFactor>1,forceUSM:i.forceUSM,scaleFactor:i.scaleFactor,cssUpscaleNeeded:i.cssUpscaleNeeded,upscaleMethodValue:i.upscaleMethodValue}}(e,i));break;case A.STRETCH:e.parts.push(function(e,t){const i=te(e.src.width,e.src.height,t.width,t.height,w.FILL),a={...t};return a.width=e.src.width*i,a.height=e.src.height*i,Ie(e,a)}(e,i));break;case A.TILE_HORIZONTAL:case A.TILE_VERTICAL:case A.TILE:case A.LEGACY_ORIGINAL_SIZE:case A.ORIGINAL_SIZE:a=ae(e.src,i,e.focalPoint,i.alignment),e.src.isCropped?(Object.assign(e.parts[0],a),e.src.width=a.width,e.src.height=a.height):e.parts.push(Ee(a));break;case A.LEGACY_STRIP_TILE_HORIZONTAL:case A.LEGACY_STRIP_TILE_VERTICAL:case A.LEGACY_STRIP_TILE:case A.LEGACY_STRIP_ORIGINAL_SIZE:e.parts.push(function(e){return{transformType:w.LEGACY_CROP,width:Math.round(e.width),height:Math.round(e.height),alignment:ne(e),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}(i));break;case A.LEGACY_STRIP_SCALE_TO_FIT:case A.LEGACY_STRIP_FIT_AND_TILE:e.parts.push(function(e){return{transformType:w.FIT,width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(i));break;case A.LEGACY_STRIP_SCALE_TO_FILL:e.parts.push(function(e){return{transformType:w.LEGACY_FILL,width:Math.round(e.width),height:Math.round(e.height),alignment:ne(e),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(i))}}function Ie(e,t){const i=ie(e.src.width,e.src.height,w.FIT,t,e.devicePixelRatio,e.upscaleMethod);return{transformType:!e.src.width||!e.src.height?w.FIT:w.FILL,width:Math.round(i.width),height:Math.round(i.height),alignment:O.center,upscale:i.scaleFactor>1,forceUSM:i.forceUSM,scaleFactor:i.scaleFactor,cssUpscaleNeeded:i.cssUpscaleNeeded,upscaleMethodValue:i.upscaleMethodValue}}function Ee(e){return{transformType:w.CROP,x:Math.round(e.x),y:Math.round(e.y),width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}function Le(e,t){t=t||{},e.quality=function(e,t){const i=e.fileType===H.PNG,a=e.fileType===H.JPG,n=e.fileType===H.WEBP,r=e.fileType===H.AVIF,o=a||i||n||r;if(o){const a=U(e.parts),n=(c=a.width,s=a.height,P[oe(c,s)].quality);let r=t.quality&&t.quality>=5&&t.quality<=90?t.quality:n;return r=i?r+5:r,r}var c,s;return 0}(e,t),e.progressive=function(e){return!1!==e.progressive}(t),e.watermark=function(e){return e.watermark}(t),e.autoEncode=t.autoEncode??!0,e.encoding=t?.encoding,e.unsharpMask=function(e,t){if(function(e){const t="number"==typeof(e=e||{}).radius&&!isNaN(e.radius)&&e.radius>=.1&&e.radius<=500,i="number"==typeof e.amount&&!isNaN(e.amount)&&e.amount>=0&&e.amount<=10,a="number"==typeof e.threshold&&!isNaN(e.threshold)&&e.threshold>=0&&e.threshold<=255;return t&&i&&a}(t.unsharpMask))return{radius:ce(t.unsharpMask?.radius,2),amount:ce(t.unsharpMask?.amount,2),threshold:ce(t.unsharpMask?.threshold,2)};if(("number"!=typeof(i=(i=t.unsharpMask)||{}).radius||isNaN(i.radius)||0!==i.radius||"number"!=typeof i.amount||isNaN(i.amount)||0!==i.amount||"number"!=typeof i.threshold||isNaN(i.threshold)||0!==i.threshold)&&function(e){const t=U(e.parts);return!(t.scaleFactor>=1)||t.forceUSM||t.transformType===w.FIT}(e))return C;var i;return}(e,t),e.filters=function(e){const t=e.filters||{},i={};be(t[k.CONTRAST],-100,100)&&(i[k.CONTRAST]=t[k.CONTRAST]);be(t[k.BRIGHTNESS],-100,100)&&(i[k.BRIGHTNESS]=t[k.BRIGHTNESS]);be(t[k.SATURATION],-100,100)&&(i[k.SATURATION]=t[k.SATURATION]);be(t[k.HUE],-180,180)&&(i[k.HUE]=t[k.HUE]);be(t[k.BLUR],0,100)&&(i[k.BLUR]=t[k.BLUR]);return i}(t)}function be(e,t,i){return"number"==typeof e&&!isNaN(e)&&0!==e&&e>=t&&e<=i}function Ae(e,t,i,a){const n=function(e){return e?.isSEOBot??!1}(a),r=Q(t.id),o=function(e,t){const i=/\.([^.]*)$/,a=new RegExp(`(${J.concat(K).join("|")})`,"g");if(t&&t.length){let e=t;const n=t.match(i);return n&&z.includes(n[1])&&(e=t.replace(i,"")),encodeURIComponent(e).replace(a,X)}const n=e.match(/\/(.*?)$/);return(n?n[1]:e).replace(i,"")}(t.id,t.name),c=n?1:function(e){return Math.min(e.pixelAspectRatio||1,T)}(i),s=ee(t.id),l=s,d=D(t.id,a?.hasAnimation,a?.allowAnimatedTransform),u={fileName:o,fileExtension:s,fileType:r,fittingType:e,preferredExtension:l,src:{id:t.id,width:t.width,height:t.height,isCropped:!1,isAnimated:le(t.id,a?.hasAnimation)},focalPoint:{x:t.focalPoint&&t.focalPoint.x,y:t.focalPoint&&t.focalPoint.y},parts:[],devicePixelRatio:c,quality:0,upscaleMethod:se(a),progressive:!0,watermark:"",unsharpMask:{},filters:{},transformed:d};return d&&(Te(u,t,i),Le(u,a)),u}function we(e,t,i){const a={...i},n=ue("isMobile");switch(e){case A.LEGACY_BG_FIT_AND_TILE:case A.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case A.LEGACY_BG_FIT_AND_TILE_VERTICAL:case A.LEGACY_BG_NORMAL:const e=n?L:I,i=n?b:E;a.width=Math.min(e,t.width),a.height=Math.min(i,Math.round(a.width/(t.width/t.height))),a.pixelAspectRatio=1}return a}const ye=Y`fit/w_${"width"},h_${"height"}`,ve=Y`fill/w_${"width"},h_${"height"},al_${"alignment"}`,Oe=Y`fill/w_${"width"},h_${"height"},fp_${"focalPointX"}_${"focalPointY"}`,Me=Y`crop/x_${"x"},y_${"y"},w_${"width"},h_${"height"}`,Re=Y`crop/w_${"width"},h_${"height"},al_${"alignment"}`,Se=Y`fill/w_${"width"},h_${"height"},al_${"alignment"}`,Ce=Y`,lg_${"upscaleMethodValue"}`,Ge=Y`,q_${"quality"}`,Ne=Y`,quality_auto`,Fe=Y`,usm_${"radius"}_${"amount"}_${"threshold"}`,Pe=Y`,bl`,xe=Y`,wm_${"watermark"}`,ke={[k.CONTRAST]:Y`,con_${"contrast"}`,[k.BRIGHTNESS]:Y`,br_${"brightness"}`,[k.SATURATION]:Y`,sat_${"saturation"}`,[k.HUE]:Y`,hue_${"hue"}`,[k.BLUR]:Y`,blur_${"blur"}`},He=Y`,enc_auto`,Be=Y`,enc_avif`,Ye=Y`,enc_pavif`,Ue=Y`,pstr`;function ze(e,t,i,a={},n){if(D(t.id,a?.hasAnimation,a?.allowAnimatedTransform)){if(W(t.id)||q(t.id)){const{alignment:r,...o}=i;t.focalPoint={x:void 0,y:void 0},delete t?.crop,n=Ae(e,t,o,a)}else n=n||Ae(e,t,i,a);return function(e){const t=[];e.parts.forEach((e=>{switch(e.transformType){case w.CROP:t.push(Me(e));break;case w.LEGACY_CROP:t.push(Re(e));break;case w.LEGACY_FILL:let i=Se(e);e.upscale&&(i+=Ce(e)),t.push(i);break;case w.FIT:let a=ye(e);e.upscale&&(a+=Ce(e)),t.push(a);break;case w.FILL:let n=ve(e);e.upscale&&(n+=Ce(e)),t.push(n);break;case w.FILL_FOCAL:let r=Oe(e);e.upscale&&(r+=Ce(e)),t.push(r)}}));let i=t.join("/");return e.quality&&(i+=Ge(e)),e.unsharpMask&&(i+=Fe(e.unsharpMask)),e.progressive||(i+=Pe(e)),e.watermark&&(i+=xe(e)),e.filters&&(i+=Object.keys(e.filters).map((t=>ke[t](e.filters))).join("")),e.fileType!==H.GIF&&(e.encoding===B.AVIF?(i+=Be(e),i+=Ne(e)):e.encoding===B.PAVIF?(i+=Ye(e),i+=Ne(e)):e.autoEncode&&(i+=He(e))),e.src?.isAnimated&&e.transformed&&(i+=Ue(e)),`${e.src.id}/${_}/${i}/${e.fileName}.${e.preferredExtension}`}(n)}return t.id}const $e={[y.CENTER]:"50% 50%",[y.TOP_LEFT]:"0% 0%",[y.TOP_RIGHT]:"100% 0%",[y.TOP]:"50% 0%",[y.BOTTOM_LEFT]:"0% 100%",[y.BOTTOM_RIGHT]:"100% 100%",[y.BOTTOM]:"50% 100%",[y.RIGHT]:"100% 50%",[y.LEFT]:"0% 50%"},je=Object.entries($e).reduce(((e,[t,i])=>(e[i]=t,e)),{}),De=[A.TILE,A.TILE_HORIZONTAL,A.TILE_VERTICAL,A.LEGACY_BG_FIT_AND_TILE,A.LEGACY_BG_FIT_AND_TILE_HORIZONTAL,A.LEGACY_BG_FIT_AND_TILE_VERTICAL],Ve=[A.LEGACY_ORIGINAL_SIZE,A.ORIGINAL_SIZE,A.LEGACY_BG_NORMAL];function We(e,t,{width:i,height:a}){return e===A.TILE&&t.width>i&&t.height>a}function Ze(e,{width:t,height:i}){if(!t||!i){const a=t||Math.min(980,e.width),n=a/e.width;return{width:a,height:i||e.height*n}}return{width:t,height:i}}function qe(e,t,i,a="center"){const n={img:{},container:{}};if(e===A.SCALE_TO_FILL){const e=t.focalPoint&&function(e){const t=`${e.x}% ${e.y}%`;return je[t]||""}(t.focalPoint),r=e||a;t.focalPoint&&!e?n.img={objectPosition:Je(t,i,t.focalPoint)}:n.img={objectPosition:$e[r]}}else[A.LEGACY_ORIGINAL_SIZE,A.ORIGINAL_SIZE].includes(e)?n.img={objectFit:"none",top:"auto",left:"auto",right:"auto",bottom:"auto"}:De.includes(e)&&(n.container={backgroundSize:`${t.width}px ${t.height}px`});return n}function Je(e,t,i){const{width:a,height:n}=e,{width:r,height:o}=t,{x:c,y:s}=i;if(!r||!o)return`${c}% ${s}%`;const l=Math.max(r/a,o/n),d=a*l,u=n*l,h=Math.max(0,Math.min(d-r,d*(c/100)-r/2)),g=Math.max(0,Math.min(u-o,u*(s/100)-o/2));return`${h&&Math.floor(h/(d-r)*100)}% ${g&&Math.floor(g/(u-o)*100)}%`}const Ke={width:"100%",height:"100%"};function Xe(e,t,i,a={}){const{autoEncode:n=!0,isSEOBot:r,shouldLoadHQImage:o,hasAnimation:c,allowAnimatedTransform:s,encoding:l}=a;if(!j(e,t,i))return G;const d=void 0===s||s,u=D(t.id,c,d);if(!u||o)return Qe(e,t,i,{...a,autoEncode:n,useSrcset:u});const h={...i,...Ze(t,i)},{alignment:g,htmlTag:m}=h,f=We(e,t,h),p=function(e,t,{width:i,height:a},n=!1){if(n)return{width:i,height:a};const r=!Ve.includes(e),o=We(e,t,{width:i,height:a}),c=!o&&De.includes(e),s=c?t.width:i,l=c?t.height:a,d=r?function(e,t){return e>900?t?.05:.15:e>500?t?.1:.18:e>200?.25:1}(s,V(t.id)):1;return{width:o?1920:s*d,height:l*d}}(e,t,h,r),_=function(e,t,i){return i?0:De.includes(t)?1:e>200?2:3}(h.width,e,r),T=function(e,t){const i=De.includes(e)&&!t;return e===A.SCALE_TO_FILL||i?A.SCALE_TO_FIT:e}(e,f),I=qe(e,t,i,g),{uri:E}=Qe(T,t,{...p,alignment:g,htmlTag:m},{autoEncode:n,filters:_?{blur:_}:{},hasAnimation:c,allowAnimatedTransform:d,encoding:l}),{attr:L={},css:b}=Qe(e,t,{...h,alignment:g,htmlTag:m},{});return b.img=b.img||{},b.container=b.container||{},Object.assign(b.img,I.img,Ke),Object.assign(b.container,I.container),{uri:E,css:b,attr:L,transformed:!0}}function Qe(e,t,i,a){let n={};if(j(e,t,i)){const r=we(e,t,i),o=Ae(e,t,r,a);n.uri=ze(e,t,r,a,o),a?.useSrcset&&(n.srcset=function(e,t,i,a,n){const r=i.pixelAspectRatio||1;return{dpr:[`${1===r?n.uri:ze(e,t,{...i,pixelAspectRatio:1},a)} 1x`,`${2===r?n.uri:ze(e,t,{...i,pixelAspectRatio:2},a)} 2x`]}}(e,t,r,a,n)),Object.assign(n,function(e,t){let i;return i=t.htmlTag===M.BG?ge:t.htmlTag===M.SVG?_e:pe,i(e,t)}(o,r),{transformed:o.transformed})}else n=G;return n}const et="https://static.wixstatic.com/media/";"undefined"!=typeof window&&window.devicePixelRatio;he();he();const tt=et,{STATIC_MEDIA_URL:it}=e,at=({fittingType:e,src:t,target:i,options:a})=>{const n=Xe(e,t,i,{...a,autoEncode:!0});return n?.uri&&!/^[a-z]+:/.test(n.uri)&&(n.uri=`${it}${n.uri}`),n},nt=/^[a-z]+:/,rt=e=>{const{id:t,containerId:i,uri:a,alt:n,name:o="",role:c,width:s,height:l,displayMode:d,devicePixelRatio:u,quality:h,alignType:g,bgEffectName:m="",focalPoint:f,upscaleMethod:_,className:T="",crop:I,imageStyles:E={},targetWidth:L,targetHeight:b,targetScale:A,onLoad:w=()=>{},onError:y=()=>{},shouldUseLQIP:v,containerWidth:O,containerHeight:M,getPlaceholder:R,isInFirstFold:S,placeholderTransition:C,socialAttrs:G,isSEOBot:N,skipMeasure:F,hasAnimation:P,encoding:x}=e,k=r.useRef(null);let H="";const B="blur"===C,Y=r.useRef(null);if(!Y.current)if(R||v||S||N){const e={upscaleMethod:_,...h||{},shouldLoadHQImage:S,isSEOBot:N,hasAnimation:P,encoding:x};Y.current=(R||at)({fittingType:d,src:{id:a,width:s,height:l,crop:I,name:o,focalPoint:f},target:{width:O,height:M,alignment:g,htmlTag:"img"},options:e}),H=!Y.current.transformed||S||N?"":"true"}else Y.current={uri:void 0,css:{img:{}},attr:{img:{},container:{}},transformed:!1};const U=!N&&(R||v)&&!S&&Y.current.transformed,z=r.useMemo((()=>JSON.stringify({containerId:i,...i&&{containerId:i},...g&&{alignType:g},...F&&{skipMeasure:!0},displayMode:d,...O&&{targetWidth:O},...M&&{targetHeight:M},...L&&{targetWidth:L},...b&&{targetHeight:b},...A&&{targetScale:A},isLQIP:U,isSEOBot:N,lqipTransition:C,encoding:x,imageData:{width:s,height:l,uri:a,name:o,displayMode:d,hasAnimation:P,...h&&{quality:h},...u&&{devicePixelRatio:u},...f&&{focalPoint:f},...I&&{crop:I},..._&&{upscaleMethod:_}}})),[i,g,F,d,O,M,L,b,A,U,N,C,x,s,l,a,o,P,h,u,f,I,_]),$=Y.current,j=$?.uri,D=$?.srcset,V=$.css?.img,W=`${p} ${T}`;r.useEffect((()=>{const e=k.current;w&&e?.currentSrc&&e?.complete&&w({target:e})}),[]);const Z=$&&!$?.transformed?`max(${s}px, 100%)`:L?`${L}px`:null;return r.createElement("wow-image",{id:t,class:W,"data-image-info":z,"data-motion-part":`BG_IMG ${i}`,"data-bg-effect-name":m,"data-has-ssr-src":H,"data-animate-blur":!N&&U&&B?"":void 0,style:Z?{"--wix-img-max-width":Z}:{}},r.createElement("img",{src:j,ref:k,alt:n||"",role:c,style:{...V,...E},onLoad:w,onError:y,width:O||void 0,height:M||void 0,...G,srcSet:S?D?.dpr?.map((e=>nt.test(e)?e:`${it}${e}`)).join(", "):void 0,fetchpriority:S?"high":void 0,loading:!1===S?"lazy":void 0,suppressHydrationWarning:!0}))};var ot="Tj01hh";var ct=e=>{var t,a;const{id:n,alt:o,role:c,className:l,imageStyles:d={},targetWidth:u,targetHeight:h,onLoad:g,onError:m,containerWidth:f,containerHeight:p,isInFirstFold:_,socialAttrs:T,skipMeasure:I,responsiveImageProps:E,zoomedImageResponsiveOverride:L,displayMode:b}=e,A=u||f,w=h||p,{fallbackSrc:y,srcset:v,sources:O,css:M}=E||{},{width:R,height:S,...C}=(null==E||null==(t=E.css)?void 0:t.img)||{},G="original_size"===b?null==E||null==(a=E.css)?void 0:a.img:C;var N;return y&&v&&M?r.createElement("img",i()({fetchpriority:_?"high":void 0,loading:!1===_?"lazy":void 0,sizes:A+"px",srcSet:I?null==L?void 0:L.srcset:null==E?void 0:E.srcset,id:n,src:y,alt:o||"",role:c,style:{...d,...I?{...null==L||null==(N=L.css)?void 0:N.img}:{...G}},onLoad:g,onError:m,className:s(l,ot),width:A,height:w},T)):y&&O&&M?r.createElement("picture",null,O.map((e=>{let{srcset:t,media:i,sizes:a}=e;return r.createElement("source",{key:i,srcSet:t,media:i,sizes:a})})),r.createElement("img",i()({fetchpriority:_?"high":void 0,loading:!1===_?"lazy":void 0,id:n,src:O[0].fallbackSrc,alt:o||"",role:c,style:{...d,objectFit:O[0].imgStyle.objectFit,objectPosition:O[0].imgStyle.objectPosition},onLoad:g,onError:m,className:s(l,ot),width:A,height:w},T))):r.createElement(rt,e)};var st=e=>{var t,i,a;const{className:n,customIdPrefix:o,getPlaceholder:c,hasAnimation:s,...l}=e,d=r.useMemo((()=>JSON.stringify({containerId:l.containerId,alignType:l.alignType,fittingType:l.displayMode,hasAnimation:s,imageData:{width:l.width,height:l.height,uri:l.uri,name:l.name,...l.quality&&{quality:l.quality},displayMode:l.displayMode}})),[l,s]),u=r.useRef(null);u.current||(u.current=c?c({fittingType:l.displayMode,src:{id:l.uri,width:l.width,height:l.height,name:l.name},target:{width:l.containerWidth,height:l.containerHeight,alignment:l.alignType,htmlTag:"bg"},options:{hasAnimation:s,allowAnimatedTransform:!1}}):{uri:void 0,css:{img:{}},attr:{img:{},container:{}}});const h=u.current,g=null!=(t=null==h?void 0:h.uri)?t:"",m=null!=(i=null==(a=h.css)?void 0:a.container)?i:{},f=Object.assign(g?{backgroundImage:"url("+g+")"}:{},m);return r.createElement("wix-bg-image",{id:""+(o||"bgImg_")+l.containerId,class:n,style:f,"data-tiled-image-info":d,"data-has-bg-scroll-effect":l.hasBgScrollEffect||"","data-bg-effect-name":l.bgEffectName||"","data-motion-part":"BG_IMG "+l.containerId})};const lt=new RegExp("<%= compId %>","g"),dt=(e,t)=>e.replace(lt,t);var ut=e=>null==e?void 0:e.replace(":hover",""),ht="bX9O_S",gt="Z_wCwr",mt="Jxk_UL",ft="K8MSra",pt="YTb3b4";const _t={quality:{unsharpMask:{radius:.33,amount:1,threshold:0}},devicePixelRatio:1};var Tt=e=>{const{id:t,videoRef:a,videoInfo:n,posterImageInfo:o,muted:c,preload:l,loop:d,alt:u,isVideoEnabled:h,getPlaceholder:g,extraClassName:m=""}=e;n.containerId=ut(n.containerId);const f=r.useMemo((()=>JSON.stringify(n)),[n]),p=r.createElement(r.Fragment,null,o.filterEffectSvgString&&r.createElement("svg",{id:"svg_"+n.containerId,className:pt},r.createElement("defs",{dangerouslySetInnerHTML:{__html:dt(o.filterEffectSvgString,n.containerId)}})),r.createElement(ct,i()({key:n.videoId+"_img",id:o.containerId+"_img",className:s(gt,mt,"bgVideoposter",m),imageStyles:{width:"100%",height:"100%"}},o,_t,{getPlaceholder:g})));return h?r.createElement("wix-video",{id:t,"data-video-info":f,"data-motion-part":"BG_IMG "+n.containerId,class:s(ht,"bgVideo",m)},r.createElement("video",{key:n.videoId+"_video",ref:a,id:n.containerId+"_video",className:ft,crossOrigin:"anonymous","aria-label":u,playsInline:!0,preload:l,muted:c,loop:d}),p):p},It="SUz0WK";var Et=e=>{const{id:t,containerId:i,pageId:a,children:n,bgEffectName:o="",containerSize:c}=e;return r.createElement("wix-bg-media",{id:t,class:It,"data-container-id":i,"data-container-size":((null==c?void 0:c.width)||0)+", "+((null==c?void 0:c.height)||0),"data-page-id":a,"data-bg-effect-name":o,"data-motion-part":"BG_MEDIA "+i},n)};const Lt="bgOverlay";var bt="m4khSP",At="FNxOn5";var wt=e=>{const{imageOverlay:t}=e;return r.createElement("div",{"data-testid":Lt,className:bt},t&&r.createElement(st,i()({customIdPrefix:"bgImgOverlay_",className:At},t)))};const yt="bgLayers",vt="colorUnderlay",Ot="mediaPadding",Mt="canvas";var Rt="MW5IWV",St="N3eg0s",Ct="Kv1aVt",Gt="dLPlxY",Nt="VgO9Yg",Ft="LWbAav",Pt="yK6aSC",xt="K_YxMd",kt="NGjcJN",Ht="mNGsUM",Bt="I8xA4L";const Yt="bgImage";var Ut=e=>{const{videoRef:t,canvasRef:a,hasBgFullscreenScrollEffect:n,image:o,backgroundImage:c,backgroundMedia:l,video:d,backgroundOverlay:u,shouldPadMedia:h,extraClass:g="",shouldRenderUnderlay:m=!d,reducedMotion:f=!1,getPlaceholder:p,hasCanvasAnimation:_,useWixMediaCanvas:T,onClick:I}=e,{onImageLoad:E}=(e=>{let{onReady:t,image:i}=e;return(0,r.useEffect)((()=>{t&&!i&&t()}),[t,i]),{onImageLoad:e=>{null!=i&&i.onLoad&&i.onLoad(e),t&&t()}}})(e),L=ut(e.containerId),b="img_"+ut(L),A=o&&r.createElement(ct,i()({id:b,className:s(Ct,Gt,Ht,Yt),imageStyles:{width:"100%",height:"100%"},getPlaceholder:p},o,{onLoad:E})),w=c&&r.createElement(st,i()({},c,{containerId:L,className:s(Ct,Gt,Ht,Yt),getPlaceholder:p})),y=d&&r.createElement(Tt,i()({id:"videoContainer_"+L},d,{extraClassName:Pt,reducedMotion:f,videoRef:t,getPlaceholder:p})),v=T&&a||_?r.createElement("wix-media-canvas",{"data-container-id":L,class:_?Bt:""},A,w,y,r.createElement("canvas",{id:L+"webglcanvas",className:s(xt,"webglcanvas"),"aria-label":(null==d?void 0:d.alt)||"",role:"presentation","data-testid":Mt})):r.createElement(r.Fragment,null,A,w,y,a&&r.createElement("canvas",{id:L+"webglcanvas",ref:a,className:s(xt,"webglcanvas"),"aria-label":(null==d?void 0:d.alt)||"",role:"presentation","data-testid":Mt})),O=l?r.createElement(Et,i()({id:"bgMedia_"+L},l),v):r.createElement("div",{id:"bgMedia_"+L,"data-motion-part":"BG_MEDIA "+L,className:Nt},v),M=u&&r.createElement(wt,u);return r.createElement("div",{id:yt+"_"+L,"data-hook":yt,"data-motion-part":"BG_LAYER "+L,className:s(Rt,g,{[St]:n}),onClick:I},m&&r.createElement("div",{"data-testid":vt,className:s(Ft,Ct)}),h?r.createElement("div",{"data-testid":Ot,className:kt},O,M):r.createElement(r.Fragment,null,O,M))};var zt=e=>r.createElement(Ut,e),$t="dkukWC",jt="FRCqDF",Dt="xnZvZH",Vt="MBOSCN";const Wt=(e,t,i)=>{const a=((e,t)=>e?[...Array(1+(t||0)).keys()].reverse().map((e=>r.createElement("div",{key:"divider-layer-"+e,style:{"--divider-layer-i":e},className:jt,"data-testid":"divider-layer-"+e,"data-divider-layer":e}))):null)(!!t,i);return t?r.createElement("div",{className:s($t,{[Dt]:"top"===e,[Vt]:"bottom"===e}),"data-testid":e+"-divider"},a):null};var Zt=e=>{var t,i;const a=r.useMemo((()=>{var t;return Wt("top",null==e?void 0:e.hasTopDivider,null==e||null==(t=e.topLayers)?void 0:t.size)}),[null==e?void 0:e.hasTopDivider,null==e||null==(t=e.topLayers)?void 0:t.size]),n=r.useMemo((()=>{var t;return Wt("bottom",null==e?void 0:e.hasBottomDivider,null==e||null==(t=e.bottomLayers)?void 0:t.size)}),[null==e?void 0:e.hasBottomDivider,null==e||null==(i=e.bottomLayers)?void 0:i.size]);return r.createElement(r.Fragment,null,a,n)};var qt={root:"section"},Jt={"bg-underlay":"LWbAav",bgUnderlay:"LWbAav","layers-container":"MW5IWV",layersContainer:"MW5IWV",animate:"V7OeEw",move:"BHIo43",fade:"UvF1nu",scrollUp:"YzrQFb",RectangleArea:"xuzjBY",rectangleArea:"xuzjBY",DefaultAreaSkin:"O7Ybkb",defaultAreaSkin:"O7Ybkb","full-screen-scroll-effect":"GeNLDt",fullScreenScrollEffect:"GeNLDt","fill-layer":"K8pHFh",fillLayer:"K8pHFh","image-fill-layer":"TaweqS",imageFillLayer:"TaweqS","bg-media":"yzHyNT",bgMedia:"yzHyNT",videoFillLayer:"zBFCpO","alpha-canvas":"xjgrS3",alphaCanvas:"xjgrS3","media-padding-layer":"b3zSS0",mediaPaddingLayer:"b3zSS0",transforms:"Wsv3ak","media-canvas":"IiJMfn",mediaCanvas:"IiJMfn",RectangleAreaAfterScroll:"KJgt14",rectangleAreaAfterScroll:"KJgt14",scrolled:"VTwcX7",section:"Gzsk0j","video-play-pause-button":"MdLl0h",videoPlayPauseButton:"MdLl0h",childrenContainer:"Gmmci1"};const Kt=({size:e,...t})=>r.createElement("svg",{viewBox:"0 0 18 18",fill:"currentColor",width:e||"18",height:e||"18",...t},r.createElement("path",{d:"M7.5,5 C8.32842712,5 9,5.67157288 9,6.5 L9,11.5 C9,12.3284271 8.32842712,13 7.5,13 C6.67157288,13 6,12.3284271 6,11.5 L6,6.5 C6,5.67157288 6.67157288,5 7.5,5 Z M11.5,5 C12.3284271,5 13,5.67157288 13,6.5 L13,11.5 C13,12.3284271 12.3284271,13 11.5,13 C10.6715729,13 10,12.3284271 10,11.5 L10,6.5 C10,5.67157288 10.6715729,5 11.5,5 Z M7.5,6 C7.22385763,6 7,6.22385763 7,6.5 L7,11.5 C7,11.7761424 7.22385763,12 7.5,12 C7.77614237,12 8,11.7761424 8,11.5 L8,6.5 C8,6.22385763 7.77614237,6 7.5,6 Z M11.5,6 C11.2238576,6 11,6.22385763 11,6.5 L11,11.5 C11,11.7761424 11.2238576,12 11.5,12 C11.7761424,12 12,11.7761424 12,11.5 L12,6.5 C12,6.22385763 11.7761424,6 11.5,6 Z"}));Kt.displayName="PauseSmall";var Xt=Kt;const Qt=({size:e,...t})=>r.createElement("svg",{viewBox:"0 0 18 18",fill:"currentColor",width:e||"18",height:e||"18",...t},r.createElement("path",{d:"M6.87468837,5.45041947 L12.7318793,8.46657119 C13.20163,8.68731241 13.20163,9.26940918 12.7318793,9.53342881 L6.87468837,12.5495805 C6.58008377,12.7012867 6.00070071,12.5801226 6,12.0161517 L6,5.98384828 C6,5.65247743 6.35266876,5.20682168 6.87468837,5.45041947 Z M7,11.3602529 L11.5834735,9 L7,6.63974714 L7,11.3602529 Z"}));Qt.displayName="PlaySmall";var ei=Qt;const ti=(e,t)=>{var a;const{id:n,skin:c="RectangleArea",className:l,containerRootClassName:u="",customClassNames:h=[],containerProps:g,children:m,fillLayers:p=e.fillLayers||e.background,tagName:_,getPlaceholder:T,dividers:I,semanticClassNames:E,onStop:L,onClick:b,onDblClick:A,onMouseEnter:w,onMouseLeave:y,lang:v,translations:O,isPlayPauseSectionExperimentOn:M}=e,R=_||"section",{shouldOmitWrapperLayers:S}=g,C=s(Jt[c],u,l,E?d(E.root,...h):d(qt.root,...h),{[Jt.shouldOmitWrapperLayers]:S}),G=!(null==p||!p.video),N=function(e,t,i){const a=o().useRef(null),n=o().useRef(null);return t?n.current||(n.current={play:()=>a.current?.play(),load:()=>a.current?.load(),pause:()=>a.current?.pause(),stop:()=>{a.current&&(a.current.pause(),a.current.currentTime=0,i&&i(a.current))}}):n.current=null,o().useImperativeHandle(e,(()=>n.current||{load(){},stop(){}})),a}(t,G,L),[F,P]=r.useState(!(null!=N&&null!=(a=N.current)&&a.paused));return r.createElement(R,i()({id:n},(e=>Object.entries(e).reduce(((e,[t,i])=>(t.includes("data-")&&(e[t]=i),e)),{}))(e),((e={})=>{const t=e.tabIndex??e.tabindex??void 0;return void 0!==t?{tabIndex:Number(t)}:{}})(e.a11y||{tabIndex:-1}),{"data-block-level-container":"Section",className:C+" "+Jt.section,"data-testid":f,onClick:b,onDoubleClick:A,onMouseEnter:w,onMouseLeave:y,lang:v}),p&&r.createElement(zt,i()({},p,{videoRef:N,getPlaceholder:T})),G&&M&&r.createElement("button",{className:""+Jt.videoPlayPauseButton,onClick:()=>{const e=null==N?void 0:N.current;e&&(e.paused?e.play():e.pause(),P((t=>{const i=!t,a=document.getElementById(n);if(a){a.querySelectorAll("video").forEach((t=>{t!==e&&(i?t.play():t.pause())}))}return i})))},"aria-pressed":F,"aria-label":null==O?void 0:O.ariaLabel},F?r.createElement(Xt,null):r.createElement(ei,null)),I&&r.createElement(Zt,I),m())};var ii=r.forwardRef(ti),ai=a(96114),ni=a.n(ai);function ri(){if(!u())return{x:0,y:0,isAtPageBottom:!1};const{left:e,top:t}=document.body.getBoundingClientRect();return{x:e,y:t,isAtPageBottom:window.innerHeight+window.scrollY===document.body.scrollHeight}}function oi(e,t,i){void 0===i&&(i={}),i={waitFor:100,disabled:!1,...i};const a=(0,r.useRef)(ri());let n=null;const o=()=>{ni().measure((()=>{const t=ri(),i=a.current;a.current=t,n=null,ni().mutate((()=>e({prevPos:i,currPos:t})))}))};(u()?r.useLayoutEffect:r.useEffect)((()=>{if(!u())return;const e=()=>{null===n&&(n=window.setTimeout(o,i.waitFor))};return i.disabled?()=>{}:(window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e),n&&window.clearTimeout(n)})}),t)}var ci={root:"header"};const si={HeaderSection:{component:e=>{const[t,a]=r.useState(!1);oi((e=>{let{currPos:i}=e;-1*i.y>=2?t||a(!0):t&&a(!1)}),[t],{disabled:"RectangleAreaAfterScroll"!==e.skin});const[n,o]=r.useState("");return oi(((e,t)=>{let i=0,a="DOWN";return n=>{let{prevPos:r,currPos:o}=n;const c=-1*o.y,s=-1*r.y;c>=s?("UP"===a&&(i=s,a="DOWN"),c-i>(e=>"move"===e?400:200)(e)&&t("scrolled-down")):("DOWN"===a&&(i=s,a="UP"),(i-c>100||c<=10)&&t("scrolled-up"))}})(e.animate,o),[],{waitFor:45,disabled:"none"===e.animate}),r.createElement(ii,i()({},e,{semanticClassNames:ci,className:s(e.className,{[Jt[e.skin]]:!0,[Jt.scrolled]:t,[Jt.animate]:"none"!==e.animate,[Jt.move]:"scrolled-down"===n&&"move"===e.animate,[Jt.fade]:"scrolled-down"===n&&"fade"===e.animate,[Jt.scrollUp]:"scrolled-up"===n})}))}}}}(),n}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[HeaderSection].c758a2a3.bundle.min.js.map