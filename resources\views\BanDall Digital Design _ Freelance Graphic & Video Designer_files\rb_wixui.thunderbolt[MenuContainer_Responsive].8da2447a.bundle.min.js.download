!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[MenuContainer_Responsive]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[MenuContainer_Responsive]"]=t(require("react")):e["rb_wixui.thunderbolt[MenuContainer_Responsive]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},n={};function r(e){var o=n[e];if(void 0!==o)return o.exports;var a=n[e]={exports:{}};return t[e](a,a.exports,r),a.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};return function(){"use strict";r.r(o),r.d(o,{components:function(){return D}});var e=r(448),t=r.n(e),n=r(5329),a=r.n(n);function i(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=i(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}var s=function(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=i(e))&&(r&&(r+=" "),r+=t);return r};const l=()=>"undefined"!=typeof window,c=e=>Object.entries(e).reduce(((e,[t,n])=>(t.includes("data-")&&(e[t]=n),e)),{});const u="wixui-",d=(e,...t)=>{const n=[];return e&&n.push(`${u}${e}`),t.forEach((e=>{e&&(n.push(`${u}${e}`),n.push(e))})),n.join(" ")};var p={menuContainer:"z_w3mq",visible:"axLCtp",inlineContent:"Lu9Epa",container:"w3z6uS",overlay:"hXDI49",horizontallyDocked:"iFw_h3",verticallyDocked:"e5DvMY",inlineContentParent:"cl36wz",open:"qqLcCB"};const f=13,v=27;function m(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}m(32),m(f),m(v);const b=["aria-id","aria-metadata","aria-type"],y=(e,t)=>Object.entries(e).reduce(((e,n)=>{let[r,o]=n;return t.includes(r)||(e[r]=o),e}),{}),h=function(e){let{role:t,tabIndex:n,tabindex:r,...o}=void 0===e?{}:e;const a=Object.entries(o).reduce(((e,t)=>{let[n,r]=t;return{...e,[("aria-"+n).toLowerCase()]:r}}),{role:t,tabIndex:null!=n?n:r});return Object.keys(a).forEach((e=>{void 0!==a[e]&&null!==a[e]||delete a[e]})),y(a,b)},x="responsive-container-overflow",C="responsive-container-content";var g="xpmKd_";const w=a().forwardRef(((e,t)=>{let{children:n,className:r}=e;return a().createElement("div",{className:s(r,g),"data-testid":x,ref:t},n)})),E=(e,t)=>{let{containerLayoutClassName:r,overlowWrapperClassName:o,hasOverflow:i,hasScrollOverflow:l,shouldOmitWrapperLayers:c,children:u,role:d,label:p,extraRootClass:f="",ariaLive:v,ariaAttributes:m,tabIndex:b,tagName:y="div"}=e;const x=!c&&i,g=x?l?b||0:-1:void 0,E=(0,n.useCallback)((e=>x?a().createElement(w,{className:s(o,f)},e):e),[x,o,f]),O=i?r:s(r,f),_={ref:t,"data-testid":C,tabIndex:g,...d?{role:d}:{},...p?{"aria-label":p}:{},...v?{"aria-live":v}:{},...h(m)};return"multi-column-layouter"===y?(_.class=O,_.style={visibility:"hidden"}):_.className=O,E(c?a().createElement(a().Fragment,null,u()):a().createElement(y||"div",_,u()))};var O=a().forwardRef(E),_="SPKdgB",P="gWQdkh",j="bPVN2R",M="_YXqpG",N="MES4Yq";const L=(e,n)=>{let{classNames:r,cssEditingClasses:o,layerIds:i,containerProps:l,ariaLabel:c,children:u}=e;return a().createElement(a().Fragment,null,a().createElement("div",{id:i.overlay,className:s(P,{[j]:r.includes("horizontallyDocked")})}),a().createElement("div",{id:i.container,className:s(_),"data-block-level-container":"MenuContainer"},a().createElement("div",{className:""+N}),a().createElement("div",{id:i.inlineContentParent,className:M},a().createElement(O,t()({},l,{extraRootClass:s(M,o),role:"dialog",label:c,ref:n,tabIndex:-1}),u))))};var R=a().forwardRef(L);var S={root:"mobile-menu"};const k=(e,r)=>{const{id:o,customClassNames:a=[],isOpen:i,children:l,classNames:u,containerProps:f,onClick:v,onMouseEnter:m,onMouseLeave:b,open:y,close:h,animate:x,translations:{containerAriaLabel:C},onKeyUp:g}=e;let{isVisible:w}=e;i&&!w&&(w=!0);const E=n.useRef(null);n.useImperativeHandle(r,(()=>({open:y,close:h,animate:x,focus:()=>{var e;null==(e=E.current)||e.focus()}})));const O={overlay:"overlay-"+o,container:"container-"+o,inlineContentParent:"inlineContentParent-"+o};return n.createElement("div",t()({id:o,onClick:v,onMouseEnter:m,onMouseLeave:b,onKeyUp:g,className:s(p.menuContainer,u.map((e=>p[e])),{[p.visible]:w,[p.open]:i})},c(e)),n.createElement(R,t()({containerProps:f,ref:E,id:o,layerIds:O,ariaLabel:C,classNames:u},c(e),{cssEditingClasses:d(S.root,...a)}),l))};const I=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`));var q;const D={MenuContainer_Responsive:{component:n.forwardRef(k),controller:(q=e=>{let{mapperProps:t,stateValues:n,controllerUtils:{updateStyles:r}}=e;const{isMobileView:o,compId:a,...i}=t,{toggle:s,...c}=n;return!0===n.isOpen&&r({"--menu-height":l()&&o?window.getComputedStyle(document.body).height:"100vh"}),{...i,...(u=c,Object.entries(u).reduce(((e,t)=>{let[n,r]=t;return void 0===r?e:{...e,[n]:r}}),{})),onClick:e=>{e.target.closest("a , #overlay-"+a)&&s&&(null==s||s(!0))}};var u},{useComponentProps:(e,t,n)=>{const r=(e=>({...e,updateStyles:t=>{const n=Object.entries(t).reduce(((e,[t,n])=>{return{...e,[(r=t,r.startsWith("--")?t:I(t))]:void 0===n?null:n};var r}),{});e.updateStyles(n)}}))(n);return q({mapperProps:e,stateValues:t,controllerUtils:r})}})}}}(),o}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[MenuContainer_Responsive].8da2447a.bundle.min.js.map