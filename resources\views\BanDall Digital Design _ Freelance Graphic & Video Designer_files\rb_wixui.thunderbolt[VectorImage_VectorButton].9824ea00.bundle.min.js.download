!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[VectorImage_VectorButton]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[VectorImage_VectorButton]"]=t(require("react")):e["rb_wixui.thunderbolt[VectorImage_VectorButton]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},a={};function r(e){var n=a[e];if(void 0!==n)return n.exports;var o=a[e]={exports:{}};return t[e](o,o.exports,r),o.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return function(){"use strict";r.r(n),r.d(n,{components:function(){return T}});var e=r(448),t=r.n(e),a=r(5329);function o(e){var t,a,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(a=o(e[t]))&&(r&&(r+=" "),r+=a);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}var i=function(){for(var e,t,a=0,r="";a<arguments.length;)(e=arguments[a++])&&(t=o(e))&&(r&&(r+=" "),r+=t);return r};const l=e=>Object.entries(e).reduce(((e,[t,a])=>(t.includes("data-")&&(e[t]=a),e)),{});const c=new RegExp("<%= compId %>","g"),s=(e,t)=>e.replace(c,t),u=13,d=27;function p(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}const f=p(32),b=p(u),v=e=>{b(e),f(e)},g=(p(d),"wixui-"),m=(e,...t)=>{const a=[];return e&&a.push(`${g}${e}`),t.forEach((e=>{e&&(a.push(`${g}${e}`),a.push(e))})),a.join(" ")},y={root:"linkElement"},k=(e,r)=>{const{href:n,role:o,target:i,rel:c,className:s="",children:u,linkPopupId:d,anchorDataId:p,anchorCompId:g,tabIndex:m,dataTestId:k=y.root,title:h,onClick:C,onDoubleClick:x,onMouseEnter:I,onMouseLeave:M,onFocus:O,onFocusCapture:w,onBlurCapture:S,"aria-live":E,"aria-disabled":L,"aria-label":_,"aria-labelledby":D,"aria-pressed":P,"aria-expanded":T,"aria-describedby":j,"aria-haspopup":B,"aria-current":A,dataPreview:N,dataPart:$}=e,R=void 0!==e.activateByKey?e.activateByKey:(e=>e?"SpaceOrEnter":"Space")(d);let V;switch(R){case"Enter":V=b;break;case"Space":V=f;break;case"SpaceOrEnter":V=v;break;default:V=void 0}return void 0!==n||d?a.createElement("a",t()({},l(e),{"data-testid":k,"data-popupid":d,"data-anchor":p,"data-anchor-comp-id":g,"data-preview":N,"data-part":$,href:n||void 0,target:i,role:d?"button":o,rel:c,className:s,onKeyDown:V,"aria-live":E,"aria-disabled":L,"aria-label":_,"aria-labelledby":D,"aria-pressed":P,"aria-expanded":T,"aria-haspopup":B,"aria-describedby":j,"aria-current":A,title:h,onClick:C,onMouseEnter:I,onMouseLeave:M,onDoubleClick:x,onFocus:O,onFocusCapture:w,onBlurCapture:S,ref:r,tabIndex:d?0:m}),u):a.createElement("div",t()({},l(e),{"data-testid":k,"data-preview":N,"data-part":$,className:s,tabIndex:m,"aria-label":_,"aria-labelledby":D,"aria-haspopup":B,"aria-disabled":L,"aria-expanded":T,title:h,role:o,onClick:C,onDoubleClick:x,onMouseEnter:I,onMouseLeave:M,ref:r}),u)};var h=a.forwardRef(k);var C={root:"vector-image"},x={link:"aeyn4z",clickable:"qQrFOK",svgRoot:"VDJedC",nonScalingStroke:"l4CAhn"};var I=e=>{const{id:r,svgContent:n,shouldScaleStroke:o,withShadow:c,link:u,ariaLabel:d,ariaExpanded:p,ariaAttributes:f,className:b="",customClassNames:v=[],containerClass:g="",onClick:y,onDblClick:k,onMouseEnter:I,onMouseLeave:M,hasPlatformClickHandler:O,onKeyDown:w,toggle:S,reportBiOnClick:E,tag:L="div",isClassNameToRootEnabled:_}=e,D=O||y,P=!(!(T=u)||0===Object.keys(T).length&&T.constructor===Object);var T;const j=i(x.svgRoot,{[x.nonScalingStroke]:!o,[x.hasShadow]:c,[x.clickable]:D,[b]:!_}),B=a.useRef(null);a.useEffect((()=>{let e;const t=B.current;return S&&(e=()=>S(!1),null==t||t.addEventListener("click",e)),()=>{e&&(null==t||t.removeEventListener("click",e))}}),[B,S]);const A=a.useMemo((()=>{if(!n)return n;const e=((e,t)=>{const a={},r=e.replace(/\sid="([^"<]+)"/g,((e,r)=>{const n=r.endsWith(t)?r:`${r}_${t}`;return a[r]=n,` id="${n}"`}));return Object.keys(a).reduce(((e,r)=>e.replace(new RegExp(`(${r})(?!_${t})`,"g"),a[r])),r)})(n,r),t=null!=f&&f.label?((e,t)=>{let a=e;const r=/aria-label="[^"]*"/;return a=e.match(r)?e.replace(r,`aria-label="${t}"`):e.replace(/(<svg[^>]*)>/,`$1 aria-label="${t}">`),a})(e,null==f?void 0:f.label):e;return s(t,r)}),[r,n,null==f?void 0:f.label]),N=a.createElement("div",{"data-testid":"svgRoot-"+r,className:j,dangerouslySetInnerHTML:{__html:A}}),$=(({reportBiOnClick:e,onClick:t})=>(0,a.useCallback)((a=>{e?.(a),t?.(a)}),[e,t]))({onClick:y,reportBiOnClick:E});return a.createElement(L,t()({id:r},l(e),((e={})=>{const t=e.tabIndex??e.tabindex??void 0;return void 0!==t?{tabIndex:Number(t)}:{}})(e.a11y),{className:i(g,b,m(C.root,...v)),onClick:D||P?$:void 0,onDoubleClick:k,onMouseEnter:I,onMouseLeave:M,onKeyDown:w,ref:B,"aria-expanded":p}),P?a.createElement(h,t()({className:x.link,"aria-label":d},u),N):N)};var M=e=>a.createElement(I,t()({},e,{tag:"button"}));const O=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)),w=e=>"linkPopupId"in e,S=(e,t)=>{if(w(e))return e.linkPopupId;{const{pagesMap:a,mainPageId:r}=t||{};if(!a)return;const n=new URL(e.href??"");let o=Object.values(a).find((({pageUriSEO:e})=>!!e&&n.pathname?.includes(e)));return o||(o=r?a[r]:void 0),o?.pageId}},E=e=>{if(void 0!==e)return null===e?"None":e.type},L=(e,t)=>{if(!e?.type)return;const{type:a}=e;switch(a){case"AnchorLink":return e.anchorDataId;case"DocumentLink":return e.docInfo?.name;case"PageLink":const a=S(e,t);return a&&t?.pagesMap?.[a]?.title;default:return e.href}},_=(e,t,a)=>{const{link:r,value:n,details:o,actionName:i,elementType:l,trackClicksAnalytics:c,pagesMetadata:s,...u}=t;if(!c)return;const d=s&&{...s,pagesMap:window.viewerModel?.siteFeaturesConfigs?.router?.pagesMap},p=((e,t)=>{if(!e?.type)return;const{type:a}=e;switch(a){case"AnchorLink":return(e=>"anchorDataId"in e&&("SCROLL_TO_TOP"===e.anchorDataId||"SCROLL_TO_BOTTOM"===e.anchorDataId))(e)?void 0:{id:e.anchorDataId};case"DocumentLink":return{id:e.docInfo?.docId};case"PageLink":return{id:S(e,t),isLightbox:w(e)};default:return}})(r,d),f=o||p?JSON.stringify({...p,...o}):void 0;e({src:76,evid:1113,...{...u,bl:navigator.language,url:window.location.href,details:f,elementType:l??"Unknown",actionName:i??E(r),value:n??L(r,d)}},{endpoint:"pa",...a})};var D;!function(e){e.Text="Text",e.Menu="Menu",e.Image="Image",e.Input="Input",e.Login="Login",e.Button="Button",e.Social="Social",e.Gallery="Gallery",e.Community="Community",e.Decorative="Decorative",e.MenuAndSearch="MenuAndSearch",e.MenuAndAnchor="MenuAndAnchor"}(D||(D={}));var P;const T={VectorImage_VectorButton:{component:M,controller:(P=e=>{let{stateValues:t,mapperProps:a}=e;const{compId:r,language:n,mainPageId:o,fullNameCompType:i,trackClicksAnalytics:l,...c}=a,{toggle:s,reportBi:u}=t,d=e=>{const{link:t}=c;_(u,{link:t,language:n,trackClicksAnalytics:l,elementType:i,pagesMetadata:{mainPageId:o},element_id:null!=r?r:e.currentTarget.id,elementGroup:D.Decorative})};return s?{...c,toggle:s,reportBiOnClick:d,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||s(!1)}}:{...c,reportBiOnClick:d}},{useComponentProps:(e,t,a)=>{const r=(e=>({...e,updateStyles:t=>{const a=Object.entries(t).reduce(((e,[t,a])=>{return{...e,[(r=t,r.startsWith("--")?t:O(t))]:void 0===a?null:a};var r}),{});e.updateStyles(a)}}))(a);return P({mapperProps:e,stateValues:t,controllerUtils:r})}})}}}(),n}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[VectorImage_VectorButton].9824ea00.bundle.min.js.map